/**
 * @id5io/id5-api.js
 * @version v1.0.86
 * @link https://id5.io/
 * @license Apache-2.0
 */
!function(){"use strict";function r(t,e){var i,r=Object.keys(t);return Object.getOwnPropertySymbols&&(i=Object.getOwnPropertySymbols(t),e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,i)),r}function h(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?r(Object(i),!0).forEach(function(e){d(t,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):r(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}function c(e,t,i,r,s,n,o){try{var a=e[n](o),c=a.value}catch(e){return void i(e)}a.done?t(c):Promise.resolve(c).then(r,s)}function s(a){return function(){var e=this,o=arguments;return new Promise(function(t,i){var r=a.apply(e,o);function s(e){c(r,t,i,s,n,"next",e)}function n(e){c(r,t,i,s,n,"throw",e)}s(void 0)})}}function d(e,t,i){return(t=function(e){e=function(e,t){if("object"!=typeof e||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0===i)return("string"===t?String:Number)(e);t=i.call(e,t||"default");if("object"!=typeof t)return t;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"==typeof e?e:String(e)}(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i,r=arguments[t];for(i in r)Object.prototype.hasOwnProperty.call(r,i)&&(e[i]=r[i])}return e}).apply(this,arguments)}function t(e,t){if(null==e)return{};var i,r=function(e,t){if(null==e)return{};for(var i,r={},s=Object.keys(e),n=0;n<s.length;n++)i=s[n],0<=t.indexOf(i)||(r[i]=e[i]);return r}(e,t);if(Object.getOwnPropertySymbols)for(var s=Object.getOwnPropertySymbols(e),n=0;n<s.length;n++)i=s[n],0<=t.indexOf(i)||Object.prototype.propertyIsEnumerable.call(e,i)&&(r[i]=e[i]);return r}function g(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var i=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=i){var r,s,n,o,a=[],c=!0,l=!1;try{if(n=(i=i.call(e)).next,0===t){if(Object(i)!==i)return;c=!1}else for(;!(c=(r=n.call(i)).done)&&(a.push(r.value),a.length!==t);c=!0);}catch(e){l=!0,s=e}finally{try{if(!c&&null!=i.return&&(o=i.return(),Object(o)!==o))return}finally{if(l)throw s}}return a}}(e,t)||a(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(e,t){if(e){if("string"==typeof e)return n(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Map"===(i="Object"===i&&e.constructor?e.constructor.name:i)||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?n(e,t):void 0}}function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,r=new Array(t);i<t;i++)r[i]=e[i];return r}function f(e,t){var i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=a(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var r=0,t=function(){};return{s:t,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:t}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,n=!0,o=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return n=e.done,e},e:function(e){o=!0,s=e},f:function(){try{n||null==i.return||i.return()}finally{if(o)throw s}}}}function i(e,t,i){return function(e,t){if(e!==t)throw new TypeError("Private static access of wrong provenance")}(e,t),i}class e{debug(){}info(){}warn(){}error(){}}const p=new e;class l extends e{constructor(e,t){super(),this._prefix=e,this._delegate=t}debug(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];this._delegate.debug((new Date).toISOString(),this._prefix,...t)}info(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];this._delegate.info((new Date).toISOString(),this._prefix,...t)}warn(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];this._delegate.warn((new Date).toISOString(),this._prefix,...t)}error(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];this._delegate.error((new Date).toISOString(),this._prefix,...t)}}function o(e,t){var i="^\\d+(\\.\\d+(\\.\\d+){0,1}){0,1}$";if(e.match(i)&&t.match(i)){var r=e.split("."),s=t.split("."),i=e=>parseInt(e)||0,e=(e,t)=>{t=e-t;return 0==t?0:t<0?-1:1},t=e(i(r[0]),i(s[0]));if(0!==t)return t;t=e(i(r[1]),i(s[1]));return 0===t?e(i(r[2]),i(s[2])):t}}const _="Array",v="String",m="Function",I=Object.prototype.toString;function w(e,t){return I.call(e)==="[object "+t+"]"}function C(e){return w(e,m)}function S(e){return w(e,v)}function y(e){return w(e,_)}function b(e){return w(e,"Number")}function D(e){return w(e,"Object")}function E(e){return void 0!==e}function A(t,i){if(!function(e){if(!e)return 1;if(y(e)||S(e))return!(0<e.length);for(var t in e)if(hasOwnProperty.call(e,t))return;return 1}(t)){if(C(t.forEach))return t.forEach(i,this);let e=0;var r=t.length;if(0<r)for(;e<r;e++)i(t[e],e,t);else for(e in t)hasOwnProperty.call(t,e)&&i.call(this,t[e],e)}}function T(e,t){let i=document.createElement("a");t&&"noDecodeWholeURL"in t&&t.noDecodeWholeURL?i.href=e:i.href=decodeURIComponent(e);t=t&&"decodeSearchAsString"in t&&t.decodeSearchAsString;return{href:i.href,protocol:(i.protocol||"").replace(/:$/,""),hostname:i.hostname,port:+i.port,pathname:i.pathname.replace(/^(?!\/)/,"/"),search:t?i.search:(t=i.search||"")?t.replace(/^\?/,"").split("&").reduce((e,t)=>{let i=t.split("="),r=g(i,2),s=r[0],n=r[1];return/\[\]$/.test(s)?(s=s.replace("[]",""),e[s]=e[s]||[],e[s].push(n)):e[s]=n||"",e},{}):{},hash:(i.hash||"").replace(/^#/,""),host:i.host||window.location.host}}function P(e){return(e.protocol||"http")+"://"+(e.host||e.hostname+(e.port?":".concat(e.port):""))+(e.pathname||"")+(e.search?"?".concat((i=e.search||"",Object.keys(i).map(t=>Array.isArray(i[t])?i[t].map(e=>"".concat(t,"[]=").concat(e)).join("&"):"".concat(t,"=").concat(i[t])).join("&"))):"")+(e.hash?"#".concat(e.hash):"");var i}function O(r,s,n,o,e){o=3<arguments.length&&void 0!==o?o:{};let a=4<arguments.length&&void 0!==e?e:p;try{let i;var c,l=o.method||(n?"POST":"GET");let e=document.createElement("a");e.href=r;let t="object"==typeof s&&null!==s?s:{success:function(){a.info("ajax","xhr success")},error:function(e){a.error("ajax","xhr error",null,e)}};"function"==typeof s&&(t.success=s),i=new window.XMLHttpRequest,i.onreadystatechange=function(){var e;4===i.readyState&&(200<=(e=i.status)&&e<300||304===e?t.success(i.responseText,i):t.error(i.statusText,i))},i.ontimeout=function(){a.error("ajax","xhr timeout after ",i.timeout,"ms")},"GET"===l&&n&&(u((c=T(r,o)).search,n),r=P(c)),i.open(l,r,!0),o.withCredentials&&(i.withCredentials=!0),A(o.customHeaders,(e,t)=>{i.setRequestHeader(t,e)}),o.preflight&&i.setRequestHeader("X-Requested-With","XMLHttpRequest"),i.setRequestHeader("Content-Type",o.contentType||"text/plain"),"POST"===l&&n?i.send(n):i.send()}catch(e){a.error("ajax","xhr construction",e)}}function R(i,e){function r(e,t){if(C(Math.imul))return Math.imul(e,t);var i=(4194303&e)*(t|=0);return 4290772992&e&&(i+=(4290772992&e)*t|0),0|i}e=1<arguments.length&&void 0!==e?e:0;let s=3735928559^e,n=1103547991^e;for(let e=0,t;e<i.length;e++)t=i.charCodeAt(e),s=r(s^t,2654435761),n=r(n^t,1597334677);return s=r(s^s>>>16,2246822507)^r(n^n>>>13,3266489909),n=r(n^n>>>16,2246822507)^r(s^s>>>13,3266489909),(4294967296*(2097151&n)+(s>>>0)).toString()}const x="TRUE"===M("id5_debug").toUpperCase(),L="TRACE"===M("id5_debug").toUpperCase(),F=Boolean(window.console);let N=!1;function U(e,t){for(var i=arguments.length,r=new Array(2<i?i-2:0),s=2;s<i;s++)r[s-2]=arguments[s];k(console.error,e,t,"ERROR",r)}function k(e,t,i,r,s){V()&&F&&e&&e.apply(console,["%cID5 - ".concat(t,"#").concat(i),"color: #fff; background: #1c307e; padding: 1px 4px; border-radius: 3px;",r].concat(s))}function V(){return x||L||N}class G extends e{constructor(e,t){super(),d(this,"_invocationId",void 0),d(this,"_origin",void 0),this._invocationId=t,this._origin=e}debug(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];!function(e,t){for(var i=arguments.length,r=new Array(2<i?i-2:0),s=2;s<i;s++)r[s-2]=arguments[s];k(console.info,e,t,"DEBUG",r)}(this._origin,this._invocationId,...t)}info(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];!function(e,t){for(var i=arguments.length,r=new Array(2<i?i-2:0),s=2;s<i;s++)r[s-2]=arguments[s];k(console.info,e,t,"INFO",r)}(this._origin,this._invocationId,...t)}warn(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];!function(e,t){for(var i=arguments.length,r=new Array(2<i?i-2:0),s=2;s<i;s++)r[s-2]=arguments[s];k(console.warn,e,t,"WARNING",r)}(this._origin,this._invocationId,...t)}error(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];U(this._origin,this._invocationId,...t)}}function M(e){const t=new RegExp("[\\?&]"+e+"=([^&#]*)"),i=t.exec(window.location.search);return null===i?"":decodeURIComponent(i[1].replace(/\+/g," "))}const j=w,W=C,H=S,q=y,B=b,J=D,z=function(e){return w(e,"Boolean")},X=E,Y=new G("ajax");function K(e,t,i,r){O(e,t,i,3<arguments.length&&void 0!==r?r:{},Y)}function Q(e){let t=new Image;t.src=e}function $(e,t,i,r,s){for(t=t.split?t.split("."):t,r=0;r<t.length;r++)e=e?e[t[r]]:s;return e===s?i:e}const Z=e=>null!=e&&"object"==typeof e;function ee(e,t){if(!(Z(e)&&Z(t)))return e===t;if(q(e)&&q(t))return function(t,i){var e=t.length===i.length;if(!e)return!1;for(let e=0;e<t.length;e++)if(!ee(t[e],i[e]))return!1;return!0}(e,t);var i=Object.keys(e),r=Object.keys(t);if(i.length!==r.length)return!1;for(var s=0,n=i;s<n.length;s++){var o=n[s];if(!ee(e[o],t[o]))return!1}return!0}const te={A:0,B:1,C:2,D:3,E:4,F:5,G:6,H:7,I:8,J:9,K:10,L:11,M:12,N:13,O:14,P:15,Q:16,R:17,S:18,T:19,U:20,V:21,W:22,X:23,Y:24,Z:25,a:26,b:27,c:28,d:29,e:30,f:31,g:32,h:33,i:34,j:35,k:36,l:37,m:38,n:39,o:40,p:41,q:42,r:43,s:44,t:45,u:46,v:47,w:48,x:49,y:50,z:51,0:52,1:53,2:54,3:55,4:56,5:57,6:58,7:59,8:60,9:61,"-":62,_:63,"+":62,"/":63};const ie=["localStoragePurposeConsent","ccpaString"],re="131",se=Object.freeze({NONE:"none",TCF_V1:"TCFv1",TCF_V2:"TCFv2",USP_V1:"USPv1",ID5_ALLOWED_VENDORS:"ID5",PREBID:"PBJS",GPP_V1_0:"GPPv1.0",GPP_V1_1:"GPPv1.1"}),ne=Object.freeze({cmp:"cmp",partner:"partner",prebid:"prebid",none:"none"}),oe=Object.freeze({TCFEUV2:2,TCFCAV1:5});class ae{constructor(e,t){d(this,"localStoragePurposeConsent",void 0),d(this,"vendorsConsentForId5Granted",void 0),this.localStoragePurposeConsent=e,this.vendorsConsentForId5Granted=t}isGranted(){return!1!==this.localStoragePurposeConsent&&!1!==this.vendorsConsentForId5Granted}getDebugInfo(e,t){const i={};return void 0!==this.localStoragePurposeConsent&&(i[e+"-"+t+"-localStoragePurposeConsent"]=this.localStoragePurposeConsent),void 0!==this.vendorsConsentForId5Granted&&(i[e+"-"+t+"-vendorsConsentForId5Granted"]=this.vendorsConsentForId5Granted),i}}class ce{constructor(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:void 0,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:void 0,i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:void 0,r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:void 0,s=4<arguments.length&&void 0!==arguments[4]?arguments[4]:void 0;d(this,"version",void 0),d(this,"applicableSections",void 0),d(this,"gppString",void 0),d(this,"euTcfSection",void 0),d(this,"canadaTcfSection",void 0),this.version=e,this.applicableSections=t,this.gppString=i,this.euTcfSection=r,this.canadaTcfSection=s}isGranted(){var e;return this.applicableSections.includes(oe.TCFEUV2)?null===(e=this.euTcfSection)||void 0===e?void 0:e.isGranted():(this.applicableSections.includes(oe.TCFCAV1),!0)}getDebugInfo(){var e={};return void 0!==this.euTcfSection&&u(e,this.euTcfSection.getDebugInfo(this.version,"tcfeuv2")),void 0!==this.canadaTcfSection&&u(e,this.canadaTcfSection.getDebugInfo(this.version,"tcfcav1")),e}static createFrom(e){const t=u(new ce,e);return E(t.euTcfSection)?t.euTcfSection=u(new ae,t.euTcfSection):void 0===t.localStoragePurposeConsent&&void 0===t.vendorsConsentForId5Granted||(t.euTcfSection=new ae(e.localStoragePurposeConsent,e.vendorsConsentForId5Granted),delete t.localStoragePurposeConsent,delete t.vendorsConsentForId5Granted),E(t.canadaTcfSection)&&(t.canadaTcfSection=u(new ae,t.canadaTcfSection)),t}}class le{constructor(){d(this,"apiTypes",void 0),d(this,"gdprApplies",void 0),d(this,"consentString",void 0),d(this,"localStoragePurposeConsent",void 0),d(this,"allowedVendors",void 0),d(this,"ccpaString",void 0),d(this,"forcedGrantByConfig",void 0),d(this,"gppData",void 0),d(this,"source",void 0),d(this,"vendorsConsentForId5Granted",void 0),this.apiTypes=[],this.gdprApplies=!1,this.consentString=void 0,this.localStoragePurposeConsent=!1,this.ccpaString=void 0,this.allowedVendors=void 0,this.forcedGrantByConfig=!1,this.gppData=void 0}localStorageGrant(){return!0===this.forcedGrantByConfig?new he(!0,de.FORCE_ALLOWED_BY_CONFIG):0===this.apiTypes.length?new he(!0,de.PROVISIONAL):this._getLocalStorageGrantFromApi()}_getLocalStorageGrantFromApi(){const e=this.apiTypes,t={};var i={};e.includes(se.TCF_V1)&&(t[se.TCF_V1]=this._isGranted(),this._addToDebugInfo(se.TCF_V1,this,i)),e.includes(se.TCF_V2)&&(t[se.TCF_V2]=this._isGranted(),this._addToDebugInfo(se.TCF_V2,this,i)),e.includes(se.ID5_ALLOWED_VENDORS)&&(t[se.ID5_ALLOWED_VENDORS]=this.allowedVendors.includes(re)),e.includes(se.USP_V1)&&(t[se.USP_V1]=!0),e.includes(se.GPP_V1_0)&&(t[se.GPP_V1_0]=this.gppData.isGranted(),u(i,this.gppData.getDebugInfo())),e.includes(se.GPP_V1_1)&&(t[se.GPP_V1_1]=this.gppData.isGranted(),u(i,this.gppData.getDebugInfo()));var r=Object.keys(t).map(e=>t[e]).reduce((e,t)=>e&&t,!0);return new he(r,de.CONSENT_API,t,i)}_addToDebugInfo(e,t,i){return void 0!==t.localStoragePurposeConsent&&(i[e+"-localStoragePurposeConsent"]=t.localStoragePurposeConsent),void 0!==t.vendorsConsentForId5Granted&&(i[e+"-vendorsConsentForId5Granted"]=t.vendorsConsentForId5Granted),i}_isGranted(){return!1===this.gdprApplies||!0===this.localStoragePurposeConsent&&!1!==this.vendorsConsentForId5Granted}hashCode(){this.localStoragePurposeConsent,this.ccpaString;var e=t(this,ie);return R(JSON.stringify(e))}static createFrom(e){const t=u(new le,e);return E(t.api)&&(t.apiTypes=function(e){var t=e.api;if(t===se.NONE)return[];if(t!==se.PREBID)return[t];{const i=[];return(E(e.gdprApplies)||E(e.consentString))&&i.push(se.TCF_V2),E(e.ccpaString)&&i.push(se.USP_V1),E(e.gppData)&&E(e.gppData.version)&&i.push(e.gppData.version),i}}(e),t.api=void 0),D(t.gppData)&&(t.gppData=ce.createFrom(t.gppData)),t}getApiTypeData(e){if(this.apiTypes.includes(e)){if(e===se.USP_V1)return{ccpaString:this.ccpaString};if(e===se.TCF_V2)return{consentString:this.consentString,gdprApplies:this.gdprApplies,localStoragePurposeConsent:this.localStoragePurposeConsent};if(e===se.GPP_V1_1||e===se.GPP_V1_0)return this.gppData;if(e===se.ID5_ALLOWED_VENDORS)return{allowedVendors:this.allowedVendors}}}toConsents(){let e={};return E(this.gdprApplies)&&(e.gdpr=this.gdprApplies),E(this.consentString)&&(e.gdpr_consent=this.consentString),E(this.ccpaString)&&(e.us_privacy=this.ccpaString),E(this.gppData)&&(e.gpp=this.gppData.gppString,e.gpp_sid=this.gppData.applicableSections.join(",")),e}}const de=Object.freeze({FORCE_ALLOWED_BY_CONFIG:"force_allowed_by_config",ID5_CONSENT:"id5_consent",PROVISIONAL:"provisional",JURISDICTION:"jurisdiction",CONSENT_API:"consent_api"});class he{constructor(e,t){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:{};d(this,"allowed",!1),d(this,"grantType",de.NONE),d(this,"api",{}),d(this,"_debugInfo",{}),this.allowed=e,this.grantType=t,this.api=i,this._debugInfo=r}isDefinitivelyAllowed(){return this.allowed&&this.grantType!==de.PROVISIONAL}}const ue="undefined"!=typeof Promise&&"undefined"!=typeof fetch;class ge{constructor(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:void 0;this.url=e||"https://diagnostics.id5-sync.com/measurements",this._metadata=t}publish(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:void 0;return e&&0<e.length?(e.forEach(e=>function(i){Object.keys(i).forEach(function(e){var t=i[e];t&&(t instanceof Object?i[e]=JSON.stringify(t):i[e]="".concat(t))})}(e.tags)),fetch(this.url,{method:"POST",headers:{"Content-Type":"text/plain"},mode:"no-cors",body:JSON.stringify({metadata:h(h({},this._metadata),t),measurements:e})})):Promise.resolve()}}class pe{constructor(e,t){d(this,"_publisher",void 0),d(this,"_scheduled",void 0),this.meterRegistry=e,this._publisher=t;const i=this;this.meterRegistry.onUnregister(function(){const e=i._onUnloadPublishAbortController;if(e)return e.abort(),i.publish({trigger:"unregister"})})}publish(){let t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:void 0;return Promise.resolve(this.meterRegistry.getAllMeasurements()).then(e=>this._publisher(e,t)).then(()=>this.meterRegistry.reset())}schedulePublishAfterMsec(e){const t=this;return t._scheduled||(setTimeout(()=>(t._scheduled=!1,t.publish({trigger:"fixed-time",fixed_time_msec:e})),e),t._scheduled=!0),this}schedulePublishBeforeUnload(){const e=this;var t="undefined"!=typeof AbortController?new AbortController:void 0;return t&&(addEventListener("beforeunload",()=>e.publish({trigger:"beforeunload"}),{capture:!1,signal:t.signal}),this._onUnloadPublishAbortController=t),this}}const _e=Object.freeze({});var ve,fe={EMPTY:_e,from:function(e){return e?e instanceof Map?Object.fromEntries(e):e:_e},toString:function(e){return Array.from(Object.entries(e),e=>{var t=g(e,2),e=t[0],t=t[1];return"".concat(e,"=").concat(t)}).sort().toString()}};const me=Object.freeze({TIMER:"TIMER",SUMMARY:"SUMMARY",COUNTER:"COUNTER"});class Ie{constructor(e,t,i){d(this,"name",void 0),d(this,"tags",void 0),d(this,"values",void 0),this.name=e,this.tags=fe.from(t),this.type=i,this.values=[]}reset(){this.values=[]}}class we extends Ie{constructor(e){super(e,1<arguments.length&&void 0!==arguments[1]?arguments[1]:void 0,me.TIMER)}startMeasurement(){try{return new Ce(this)}catch(e){return}}record(e){try{isNaN(e)||this.values.push({value:e,timestamp:Date.now()})}catch(e){}}recordNow(){try{var e;this.record(0|(null===(e=performance)||void 0===e?void 0:e.now()))}catch(e){}}}class Ce{constructor(){this.timer=0<arguments.length&&void 0!==arguments[0]?arguments[0]:void 0,this.startTime=performance.now()}record(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:void 0;try{var i=performance.now()-this.startTime|0;let e=t||this.timer;return e&&e.record(i),i}catch(e){return}}}class Se extends Ie{constructor(e){super(e,1<arguments.length&&void 0!==arguments[1]?arguments[1]:void 0,me.COUNTER)}inc(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:1;try{return 0===this.values.length?this.values.push({value:e,timestamp:Date.now()}):(this.values[0].value+=e,this.values[0].timestamp=Date.now()),this.values[0].value}catch(e){}}}class ye extends Ie{constructor(e){super(e,1<arguments.length&&void 0!==arguments[1]?arguments[1]:void 0,me.SUMMARY)}record(e){try{this.values.push({value:e,timestamp:Date.now()})}catch(e){}}}class be{has(e){return!1}set(e,t){return this}get(e){}values(){return[]}}class De extends be{has(e){return void 0!==this[e]}set(e,t){return this[e]=t,this}get(e){return this[e]}values(){return Object.entries(this).map(e=>{return g(e,2)[1]})}}function Ee(){return new Ce}function Ae(e){return{partner:e}}class Te extends class{constructor(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:new be,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:void 0,i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:void 0;d(this,"_registry",void 0),d(this,"commonTags",void 0),d(this,"_onUnregisterCallback",void 0),this._registry=e,this.commonTags=fe.from(t),this.commonPrefix=i}getOrCreate(e,t,i){var r=h(h({},t),this.commonTags),t=this.commonPrefix?this.commonPrefix+"."+e:e,e="".concat(t,"[").concat(fe.toString(r),"]");if(this._registry.has(e))return this._registry.get(e);r=i(t,r);return this._registry.set(e,r),r}getAllMeasurements(){return this._registry.values().map(e=>({name:e.name,type:e.type,tags:e.tags,values:e.values})).filter(function(e){return e.values&&0<e.values.length})}reset(){Array.from(this._registry.values()).forEach(e=>e.reset())}addCommonTags(e){this.commonTags=h(h({},this.commonTags),fe.from(e))}timer(e){return this.getOrCreate(e,1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},(e,t)=>new we(e,t))}counter(e){return this.getOrCreate(e,1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},(e,t)=>new Se(e,t))}summary(e){return this.getOrCreate(e,1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},(e,t)=>new ye(e,t))}unregister(){void 0!==this._onUnregisterCallback&&this._onUnregisterCallback(this)}onUnregister(e){this._onUnregisterCallback=e}}{constructor(e,t){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:void 0,r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:void 0;super(4<arguments.length&&void 0!==arguments[4]?arguments[4]:new De,h(h({source:e,version:t},Ae(i)),r),"id5.api")}}function Pe(e,t){return e.counter("consent.discrepancy.count",1<arguments.length&&void 0!==t?t:{})}function Oe(e,t,i){return e.timer("consent.request.time",h({requestType:t},2<arguments.length&&void 0!==i?i:{}))}const Re=Object.freeze({tcfv2:{objName:"__tcfapiCall",objKeys:["command","version"],returnObjName:"__tcfapiReturn"},uspv1:{objName:"__uspapiCall",objKeys:["command","version"],returnObjName:"__uspapiReturn"},gppv1:{objName:"__gppCall",objKeys:["command","parameter"],returnObjName:"__gppReturn"}}),xe=Object.freeze({TCF:0,USP:1,GPP:2});class Le{constructor(e,t){d(this,"direct",void 0),d(this,"version",void 0),this.direct=e,this.version=t}getConsentData(){var e=this;return s(function*(){return e.getClientConsentData()})()}static create(a,c){return s(function*(){var e=Ve._findCmpApi("__gpp"),t=e.cmpApiFrame,e=e.cmpApiFunction;let i,r=!1;if(!t)return a.warn("cmpApi: GPP not found! Using defaults."),Promise.resolve();if(W(e))r=!0,a.info("cmpApi: Detected GPP is directly accessible, calling it now."),i=e;else{a.info("cmpApi: Detected GPP is outside the current iframe. Using message passing.");const o=Ve._buildCmpSurrogate(Re.gppv1,t);i=function(e,t,i){o(e,i,t)}}var s=yield new Promise(t=>{var e=i("ping",function(e){t(e)});J(e)&&t(e)});switch(s.gppVersion){case Ne.version:return new Ne(s,i,r);case ke.version:return new ke(s,i,r,c);default:var n="Unsupported version of gpp: ".concat(s.gppVersion);return a.warn(n),Promise.reject(n)}})()}static caTcfDataHasExpressConsent(e){e=e.PurposesExpressConsent;if(q(e)&&0<e.length)return!0===e[9]}static caTcfDataHasVendorExpressConsent(e){let t=e.VendorExpressConsent;if(q(t)&&0<t.length)return void 0!==t.find(e=>e==re)}static tcfDataHasLocalStorageGrant(e){e=i(Le,Le,Fe).call(Le,e,"PurposeConsent","PurposeConsents");if(q(e)&&0<e.length)return!0===e[0]}static tcfDataHasID5VendorConsented(e){let t=i(Le,Le,Fe).call(Le,e,"VendorConsent","VendorConsents");if(0<t.length)return void 0!==(null===t||void 0===t?void 0:t.find(e=>e==re))}static getTcfData(e){let t=void 0;return q(e)&&J(e[0])?t=e[0]:J(e)&&(t=e),t}}function Fe(e,t,i){return t in e?e[t]:e[i]}class Ne extends Le{constructor(e,t,i){super(i,Ne.version),d(this,"gppFn",void 0),d(this,"ready",void 0),this.gppFn=t,this.ready=this.isReady(e)}isReady(e){return"loaded"===e.cmpStatus&&"visible"!==e.cmpDisplayStatus}getClientConsentData(){var r=this;return s(function*(){r.ready||(r.ready=yield new Promise(t=>{r.gppFn("addEventListener",e=>!!r.isReady(e.pingData)&&void t(!0))}));var e=new Promise(t=>{r.gppFn("getGPPData",e=>{t(e)})}),t=new Promise(t=>{r.gppFn("getSection",e=>{t(e)},"tcfeuv2")}),e=g(yield Promise.all([e,t]),2),t=e[0],e=e[1];const i=new ce(se.GPP_V1_0,t.applicableSections,t.gppString);return e&&(i.euTcfSection=new ae(Le.tcfDataHasLocalStorageGrant(e),Le.tcfDataHasID5VendorConsented(e))),i})()}}function Ue(e,t,i){i&&(e.apiTypes.push(t),u(e,i))}d(Ne,"version","1.0");class ke extends Le{constructor(e,t,i,r){super(i,ke.version),d(this,"gppFn",void 0),d(this,"readyPingData",void 0),d(this,"metrics",void 0),this.gppFn=t,"ready"===e.signalStatus&&(this.readyPingData=e),this.metrics=r}getClientConsentData(){var o=this;return s(function*(){const n=o.metrics;return new Promise(i=>{let r=!1;if(o.readyPingData)i(o.parsePingData(o.readyPingData));else{const s=Date.now();let t=setTimeout(()=>{t=void 0,o.gppFn("ping",e=>{"stub"===e.cmpStatus&&(n.counter("gpp.stubUsed",{cmpId:e.cmpId}).inc(),i(o.parsePingData(e)))})},1e3);o.gppFn("addEventListener",e=>"ready"===e.pingData.signalStatus&&void(r?ke.measureAdditionalEvent(e,n,s):(t?(clearTimeout(t),t=void 0):r||n.timer("gpp.lateCmp",{cmpId:e.pingData.cmpId}).record(Date.now()-s),i(o.parsePingData(e.pingData)),r=!0)))}})})()}static measureAdditionalEvent(e,t,i){["cmpStatus","cmpDisplayStatus","signalStatus","sectionChange"].includes(e.eventName)&&t.timer("gpp.additionalEvents",{cmpId:e.pingData.cmpId,name:e.eventName}).record(Date.now()-i)}parsePingData(e){const t=new ce(se.GPP_V1_1,e.applicableSections,e.gppString);var i;return!t.applicableSections.includes(oe.TCFEUV2)||(i=Le.getTcfData(null===(i=e.parsedSections)||void 0===i?void 0:i.tcfeuv2))&&(t.euTcfSection=new ae(Le.tcfDataHasLocalStorageGrant(i),Le.tcfDataHasID5VendorConsented(i))),!t.applicableSections.includes(oe.TCFCAV1)||(e=Le.getTcfData(null===(e=e.parsedSections)||void 0===e?void 0:e.tcfcav1))&&(t.canadaTcfSection=new ae(Le.caTcfDataHasExpressConsent(e),Le.caTcfDataHasVendorExpressConsent(e))),t}}d(ke,"version","1.1");class Ve{constructor(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:p;d(this,"_lookupInProgress",void 0),d(this,"_log",void 0),d(this,"_metrics",void 0),d(this,"_consentDataPromise",void 0),this._metrics=e,this._log=t}refreshConsentData(e,t,i){const r=this;return r._lookupInProgress||(r._lookupInProgress=!0,r._consentDataPromise=this._lookupConsentData(e,t,i).finally(()=>{r._lookupInProgress=!1})),this._consentDataPromise}_lookupConsentData(e,t,i){if(e){this._log.warn("cmpApi: ID5 is operating in forced consent mode and will not retrieve any consent signals from the CMP");let e=new le;return e.forcedGrantByConfig=!0,e.source=ne.partner,Promise.resolve(e)}switch(t){case"static":return new Promise(e=>{this._parseStaticConsentData(i,e)});case"iab":return new Promise(e=>{this._lookupIabConsent(e)});default:return this._log.error("cmpApi: Unknown consent API: ".concat(t)),Promise.reject(new Error("Unknown consent API: ".concat(t)))}}_parseStaticConsentData(e,t){e=e||{};let i=new le;if(i.source=ne.partner,J(e.getTCData)){const r=this._parseTcfData(e.getTCData);Ue(i,se.TCF_V2,r);try{const s=this._metrics;this._lookupTcf(e=>{Pe(s,{apiType:se.TCF_V2,sameString:(null==e?void 0:e.consentString)===(null===r||void 0===r?void 0:r.consentString),sameLSPC:(null==e?void 0:e.localStoragePurposeConsent)===(null===r||void 0===r?void 0:r.localStoragePurposeConsent),sameVendorsConsentForId5Granted:(null==e?void 0:e.vendorsConsentForId5Granted)===(null===r||void 0===r?void 0:r.vendorsConsentForId5Granted),sameGdpr:(null==e?void 0:e.gdprApplies)===(null===r||void 0===r?void 0:r.gdprApplies)}).inc()})}catch(e){}}if(q(e.allowedVendors)&&Ue(i,se.ID5_ALLOWED_VENDORS,{allowedVendors:e.allowedVendors.map(e=>e.toString()),gdprApplies:!0}),J(e.getUSPData)){const n=this._parseUspData(e.getUSPData);Ue(i,se.USP_V1,n);try{const o=this._metrics;this._lookupUsp(e=>{Pe(o,{apiType:se.USP_V1,sameString:(null==e?void 0:e.ccpaString)===(null===n||void 0===n?void 0:n.ccpaString)}).inc()})}catch(e){}}0===i.apiTypes.length&&this._log.warn("cmpApi: No static consent data detected! Using defaults."),this._log.info("cmpApi: Detected APIs '".concat(i.apiTypes,"' from static consent data"),e),t(i)}_lookupIabConsent(r){const s=[];let n=new le;n.source=ne.cmp;var e=i=>(s[i]=0,(e,t)=>{s[i]||(s[i]=Date.now(),e&&Ue(n,t,e),s.every(e=>0<e)&&r(n))}),t=e(xe.TCF),i=e(xe.USP),e=e(xe.GPP);this._lookupGpp(e),this._lookupTcf(t),this._lookupUsp(i)}_lookupUsp(i){var e=Ve._findCmpApi("__uspapi"),t=e.cmpApiFrame,e=e.cmpApiFunction;let r;if(!t)return this._log.warn("cmpApi: USP not found! Using defaults for CCPA."),void i();r=W(e)?(this._log.info("cmpApi: Detected USP is directly accessible, calling it now."),e):(this._log.info("cmpApi: Detected USP is outside the current iframe. Using message passing."),Ve._buildCmpSurrogate(Re.uspv1,t));r("getUSPData",1,(e,t)=>{t?i(this._parseUspData(e),se.USP_V1):(this._log.error("cmpApi: USP callback not successful. Using defaults for CCPA."),i())})}_lookupGpp(n){var o=this;return s(function*(){var t=Date.now();try{let e=yield Le.create(o._log,o._metrics);if(e){var i={gppVersion:e.version,directCmp:e.direct};try{var r=yield e.getConsentData();n({gppData:r},r.version);var s=Date.now();o._metrics.timer("gpp.delay",i).record(s-t)}catch(e){o._metrics.counter("gpp.failure",u({type:"CONSENT"},i)).inc(),o._log.error("cmpApi: getting GPP consent not successful. Using defaults for Gpp."),n()}}else n()}catch(e){o._metrics.counter("gpp.failure",{type:"CLIENT"}).inc(),o._log.error("cmpApi: creating GPP client not successful. Using defaults for Gpp."),n()}})()}static _buildCmpSurrogate(a,c){return(e,t,i)=>{const r=Math.random()+"",s={},n={};n[a.objKeys[0]]=e,n[a.objKeys[1]]=t,n.callId=r,s[a.objName]=n;const o=e=>{e=$(e,"data.".concat(a.returnObjName));e&&e.callId===r&&(void 0!==(e=i(e.returnValue,e.success))&&!0!==e||window.removeEventListener("message",o))};window.addEventListener("message",o,!1),c.postMessage(s,"*")}}_lookupTcf(e){var t=Ve._findTCF(),i=t.cmpFrame,t=t.cmpFunction;if(!i)return this._log.warn("cmpApi: TCF not found! Using defaults for GDPR."),void e();W(t)?this._lookupDirectTcf(t,e):(this._log.info("cmpApi: Detected TCF is outside the current iframe. Using message passing."),this._lookupMessageTcf(i,e))}_lookupMessageTcf(e,t){e=Ve._buildCmpSurrogate(Re.tcfv2,e);this._lookupDirectTcf(e,t)}_lookupDirectTcf(e,s){const n=this._log;e("addEventListener",2,(e,t)=>{var i,r;return i="event",r=e,n.info("cmpApi: TCFv2 - Received a call back: ".concat(i),r),t?!(!e||!1!==e.gdprApplies&&"tcloaded"!==e.eventStatus&&"useractioncomplete"!==e.eventStatus)&&void s(this._parseTcfData(e),se.TCF_V2):(n.error("cmpApi: TCFv2 - Received insuccess: ".concat("addEventListener",". Please check your CMP setup. Using defaults for GDPR.")),void s())})}_parseUspData(e){if(J(e)&&H(e.uspString))return{ccpaString:e.uspString};this._log.error("cmpApi: No or malformed USP data. Using defaults for CCPA.")}_parseTcfData(e){let t=this._log,i,r;if(i=Ve._isValidV2ConsentObject,r=Ve._normalizeV2Data,i(e))return r(e);t.error("cmpApi: Invalid CMP data. Using defaults for GDPR.",e)}static _isValidV2ConsentObject(e){var t=e&&e.gdprApplies,e=e&&e.tcString;return!1===t||H(e)}static _tcfDataHasID5VendorConsented(e){var t,i;return!0===(null==e||null===(t=e.vendor)||void 0===t||null===(i=t.consents)||void 0===i?void 0:i[re])}static _normalizeV2Data(e){let t=$(e,"purpose.consents.1");z(t)||(t=function(e,t){var i=152+t-1,t=~~(i/6);if(e&&"C"===e.charAt(0)&&!(e.length<=t)){t=e.charAt(t),t=te[t];if(void 0!==t)return 0!=(t&1<<6-i%6-1)}}(e.tcString,1));var i=Ve._tcfDataHasID5VendorConsented(e);return{consentString:e.tcString,localStoragePurposeConsent:t,gdprApplies:e.gdprApplies,vendorsConsentForId5Granted:i}}static _findTCF(){let e=window,t,i;for(;!t;){try{if("function"==typeof e.__tcfapi){i=e.__tcfapi,t=e;break}}catch(e){}try{if(e.frames.__tcfapiLocator){t=e;break}}catch(e){}if(e===window.top)break;e=e.parent}return{cmpFrame:t,cmpFunction:i}}static _findCmpApi(e){let t=window,i,r;for(;!i;){try{if("function"==typeof t[e]){r=t[e],i=t;break}}catch(e){}try{if(t.frames["".concat(e,"Locator")]){i=t;break}}catch(e){}if(t===window.top)break;t=t.parent}return{cmpApiFrame:i,cmpApiFunction:r}}}const Ge=Object.freeze({CONSENT_UPDATED:"consent_updated",USER_ID_READY:"user_id_ready",CASCADE_NEEDED:"fire_sync_pixel",USER_ID_FETCH_CANCELED:"user_id_fetch_canceled",USER_ID_FETCH_FAILED:"user_id_fetch_failed"}),Me=Object.freeze({ID5_MESSAGE_RECEIVED:"message",ID5_INSTANCE_JOINED:"instance-joined",ID5_LEADER_ELECTED:"leader-elected"}),je=Object.freeze([...Object.values(Me),...Object.values(Ge)]);class We{constructor(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:p;d(this,"_handlers",void 0),d(this,"_log",void 0),this._log=e,this._handlers={}}_dispatch(t){var e=this._handlers[t];if(e){for(var i=arguments.length,r=new Array(1<i?i-1:0),s=1;s<i;s++)r[s-1]=arguments[s];var n,o=f(e);try{for(o.s();!(n=o.n()).done;){const a=n.value;try{a(...r)}catch(e){this._log.error("Event ".concat(t," handler execution failed."),e)}}}catch(e){o.e(e)}finally{o.f()}}}emit(e){if(void 0!==e&&je.includes(e)){for(var t=arguments.length,i=new Array(1<t?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];this._dispatch(e,...i)}else this._log.warn("Unsupported event",e)}on(e,t){void 0!==e&&je.includes(e)?(this._handlers[e]||(this._handlers[e]=[]),this._handlers[e].push(t)):this._log.warn("Unsupported event",e)}}class He{constructor(e,t,i,r,s,n,o){d(this,"_called",void 0),d(this,"_callbackFn",void 0),d(this,"_callbackArgs",void 0),d(this,"_beforeTrigger",void 0),d(this,"_watchdog",void 0),d(this,"_log",void 0),d(this,"_metrics",void 0),d(this,"_callbackName",void 0),this._callbackName=e,this._log=t,this._metrics=i,this._callbackFn=r;for(var a=arguments.length,c=new Array(7<a?a-7:0),l=7;l<a;l++)c[l-7]=arguments[l];this._callbackArgs=c,this._beforeTrigger=o,this._called=!1,this._callbackTriggerTimer=Ee(),this._timeout=s,n?this._watchdog=setTimeout(()=>this._trigger("immediate"),0):0<s&&(this._watchdog=setTimeout(()=>this._trigger("timeout"),s))}_trigger(){let e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"unknown";this._watchdog=void 0;let t=this._callbackTriggerTimer.record(this._metrics.timer("callback.trigger.time",{trigger:e,callbackName:this._callbackName,timeout:this._timeout}));this._called||(this._called=!0,setTimeout(()=>{this._log.debug("Firing ".concat(this._callbackName," callback after ").concat(t,"ms. Triggered by ").concat(e,", configured timeoutMs=").concat(this._timeout)),this._beforeTrigger(),this._callbackFn.call(globalThis,...this._callbackArgs)},0))}triggerNow(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"eventOccurred";this.disableWatchdog(),this._trigger(e)}disableWatchdog(){this._watchdog&&(clearTimeout(this._watchdog),this._watchdog=void 0)}}const qe="1.0.86",Be=Object.freeze({NEVER:"never",AFTER_UID_SET:"after-uid-set",ASAP:"asap"}),Je=Object.freeze({allowGCReclaim:Object.values(Be)});class ze{constructor(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:p;if(d(this,"invocationId",void 0),d(this,"options",void 0),d(this,"providedOptions",void 0),d(this,"invalidSegments",void 0),this._log=t,this.options={debugBypassConsent:!1,allowLocalStorageWithoutConsentApi:!1,cmpApi:"iab",consentData:{},refreshInSeconds:7200,partnerId:void 0,partnerUserId:void 0,callbackOnAvailable:void 0,callbackOnUpdates:void 0,callbackTimeoutInMs:void 0,pd:void 0,abTesting:{enabled:!1,controlGroupPct:0},provider:void 0,maxCascades:8,applyCreativeRestrictions:!1,acr:!1,segments:void 0,disableUaHints:!1,storageExpirationDays:void 0,att:void 0,diagnostics:{publishingDisabled:!1,publishAfterLoadInMsec:3e4,publishBeforeWindowUnload:!0,publishingSampleRatio:.01},multiplexing:{_disabled:!1},allowGCReclaim:Be.AFTER_UID_SET},this.providedOptions={},!B(e.partnerId)&&!H(e.partnerId))throw new Error("partnerId is required and must be a number or a string");this.invalidSegments=0,this.updOptions(e)}getOptions(){return this.options}getProvidedOptions(){return this.providedOptions}getInvalidSegments(){return this.invalidSegments}hasCreativeRestrictions(){return this.options.applyCreativeRestrictions||this.options.acr}isForceAllowLocalStorageGrant(){var e=this.options;return e.allowLocalStorageWithoutConsentApi||e.debugBypassConsent}updOptions(a){const c=this,l=c._log;if(J(a)){this.setPartnerId(a.partnerId);const d=(e,t)=>{this.options[e]=t,this.providedOptions[e]=t};Object.keys(a).forEach(e=>{if("segments"===e){const r=a[e],s=[];E(r)&&(q(r)?(r.forEach((e,t)=>{t="segments[".concat(t,"]");return q(e.ids)&&function(e,t){let i=!0;return A(e,e=>i=i&&t(e)),i}(e.ids,H)?e.ids.length<1?(l.error("Config option ".concat(t,".ids should contain at least one segment ID")),void(c.invalidSegments+=1)):H(e.destination)?void s.push(e):(Xe(l,"".concat(t,".destination"),"String",e.destination),void(c.invalidSegments+=1)):(Xe(l,"".concat(t,".ids"),"Array of String",e.ids),void(c.invalidSegments+=1))}),d(e,s)):Xe(l,e,"Array",r))}else if("diagnostics"===e){const n=this.options.diagnostics,o=a.diagnostics;if(j(o,ze.configTypes.diagnostics)){let t=h({},n);Object.keys(o).forEach(e=>{void 0!==n[e]&&typeof n[e]==typeof o[e]&&(t[e]=o[e])}),this.options[e]=t}this.providedOptions[e]=a[e]}else{var t,i;void 0!==Je[e]?(i=a[e])&&Je[e].includes(i)&&d(e,i):"partnerId"!==e&&(t=ze.configTypes[e],E(i=a[e])&&(j(i,t)?d(e,i):Xe(l,e,t,i)))}})}else l.error("Config options must be an object")}setPartnerId(e){let t;if(H(e)){if(t=parseInt(e),isNaN(t)||t<0)throw new Error("partnerId is required and must parse to a positive integer")}else B(e)&&(t=e);if(B(t)){if(B(this.options.partnerId)&&t!==this.options.partnerId)throw new Error("Cannot update config with a different partnerId");this.options.partnerId=t,this.providedOptions.partnerId=e}}}function Xe(e,t,i,r){e.error("Config option ".concat(t," must be of type ").concat(i," but was ").concat(toString.call(r),". Ignoring..."))}d(ze,"configTypes",{debugBypassConsent:"Boolean",allowLocalStorageWithoutConsentApi:"Boolean",cmpApi:"String",consentData:"Object",refreshInSeconds:"Number",partnerUserId:"String",callbackOnAvailable:"Function",callbackOnUpdates:"Function",callbackTimeoutInMs:"Number",pd:"String",abTesting:"Object",provider:"String",maxCascades:"Number",applyCreativeRestrictions:"Boolean",acr:"Boolean",disableUaHints:"Boolean",storageExpirationDays:"Number",att:"Number",diagnostics:"Object",multiplexing:"Object",dynamicConfig:"Object",allowGCReclaim:"String"});class Ye{constructor(e,t,i){d(this,"refererInfo",void 0),d(this,"apiVersion",void 0),d(this,"isUsingCdn",void 0),this.refererInfo=e,this.apiVersion=t,this.isUsingCdn=i}}class Ke{constructor(e,t){d(this,"_targets",void 0),d(this,"_metrics",void 0),d(this,"_survivalTimer",void 0),this._targets=[e,t],this._metrics=t,this._survivalTimer=Ee()}unregister(e){var t;this._survivalTimer&&this._metrics&&this._survivalTimer.record((t=this._metrics,e={unregisterTrigger:e},t.timer("instance.survival.time",e))),this._targets.forEach(e=>{try{X(e)&&W(e.unregister)&&e.unregister()}catch(e){}})}}globalThis.__id5_finalization_registry||(globalThis.__id5_finalization_registry=new class{constructor(){d(this,"_finalizationRegistry",void 0),d(this,"_instancesHolder",void 0),this._instancesHolder=new Set;try{this._finalizationRegistry=new FinalizationRegistry(e=>{try{X(e)&&W(e.unregister)&&e.unregister("gc-reclaim")}catch(e){}})}catch(e){}}register(e){try{e.getOptions().allowGCReclaim!==Be.ASAP&&this._instancesHolder.add(e),this._finalizationRegistry.register(e,e._unregisterTargets,e)}catch(e){}}unregister(e){try{this.releaseInstance(e,!0),this._finalizationRegistry.unregister(e)}catch(e){}}releaseInstance(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1];!e.getOptions().allowGCReclaim===Be.NEVER&&!t||this._instancesHolder.delete(e)}});const Qe=globalThis.__id5_finalization_registry;class $e{constructor(e){d(this,"_ref",void 0),this._ref=e}deref(){return this._ref}}class Ze{getOptions(){return this.config.getOptions()}getProvidedOptions(){return this.config.getProvidedOptions()}getInvalidSegments(){return this.config.getInvalidSegments()}getUserId(){return!1===this._isExposed?"0":this._userId}isFromCache(){return this._fromCache}exposeUserId(){return this._isExposed}getConsents(){return this._consents}onAvailable(e,t){if(!W(e))throw new Error("onAvailable expects a function");return this._availableCallback?this._log.warn("onAvailable was already called, ignoring"):this._availableCallback=new He("onAvailable",this._log,this._metrics,e,t,this._userIdAvailable,()=>{},this),this}onUpdate(e){if(!W(e))throw new Error("onUpdate expects a function");return this._updateCallback=e,this._userIdAvailable&&this._fireOnUpdate(),this}unregister(){try{this._unregisterTargets.unregister("api-call"),Qe.unregister(this)}catch(e){}}constructor(e,t,i,r,s,n){var o=6<arguments.length&&void 0!==arguments[6]?arguments[6]:{};d(this,"_availableCallback",void 0),d(this,"_updateCallback",void 0),d(this,"_refreshCallback",void 0),d(this,"_isExposed",void 0),d(this,"_fromCache",void 0),d(this,"_signature",void 0),d(this,"_isRefreshing",!1),d(this,"_isRefreshingWithFetch",!1),d(this,"_userId",void 0),d(this,"_ext",void 0),d(this,"_userIdAvailable",!1),d(this,"_userIdAvailablePromise",void 0),d(this,"_userIdAvailablePromiseResolver",void 0),d(this,"invocationId",void 0),d(this,"config",void 0),d(this,"_metrics",void 0),d(this,"_log",void 0),d(this,"_multiplexingInstance",void 0),d(this,"_pageLevelInfo",void 0),d(this,"_origin",void 0),d(this,"_ids",void 0),d(this,"_publisherTrueLinkId",void 0),d(this,"_gpId",void 0),d(this,"_consents",void 0),this.config=e,this._metrics=t,this._log=new l("Id5Instance:",i),this._multiplexingInstance=r,this._userIdAvailablePromise=new Promise(e=>{this._userIdAvailablePromiseResolver=e}),this._pageLevelInfo=s,this._unregisterTargets=new Ke(this._multiplexingInstance,this._metrics),this._origin=n,Qe.register(this),this._registrationProperties=o}bootstrap(){const n=Ee();var e=this.config.getOptions();const o=this._ref();this._multiplexingInstance.on(Ge.USER_ID_READY,(e,t)=>{const i=o.deref();if(i){try{var r=null!=t&&t.tags?h({},t.tags):{},s=i._metrics;null!=t&&t.timestamp&&s.timer("userid.provisioning.delivery.delay",r).record(Date.now()-t.timestamp),n.record(function(e,t,i){return e.timer("userid.provisioning.delay",h({cachedResponseUsed:t},2<arguments.length&&void 0!==i?i:{}))}(s,e.isFromCache,h(h({},r),{},{isUpdate:i._userIdAvailable,hasOnAvailable:void 0!==i._availableCallback,hasOnRefresh:void 0!==i._refreshCallback,hasOnUpdate:void 0!==i._updateCallback,provisioner:(null==t?void 0:t.provisioner)||"leader",hasChanged:i._userId!==e.responseObj.universal_uid})))}catch(e){i._log.error("Failed to measure provisioning metrics",e)}i._setUserId(e.responseObj,e.isFromCache,e.willBeRefreshed),i._consents=e.consents}}).on(Ge.USER_ID_FETCH_CANCELED,e=>{const t=o.deref();t&&t._log.info("ID5 User ID fetch canceled:",e.reason)}),this._log.info("bootstrapped for partner ".concat(e.partnerId," with referer ").concat(null===(e=this._pageLevelInfo)||void 0===e?void 0:e.refererInfo," and options"),this.getProvidedOptions())}init(){const i=this.config.getOptions();return this._gatherFetchIdData().then(e=>{var t;this._multiplexingInstance.register(h({source:this._origin,sourceVersion:qe,sourceConfiguration:{options:i},fetchIdData:e,singletonMode:!0===(null===i||void 0===i||null===(t=i.multiplexing)||void 0===t?void 0:t._disabled),forceAllowLocalStorageGrant:this.config.isForceAllowLocalStorageGrant(),storageExpirationDays:i.storageExpirationDays},this._registrationProperties))})}_gatherFetchIdData(){var t=this;return s(function*(){var e=t.config.getOptions();return Promise.resolve({partnerId:e.partnerId,refererInfo:t._pageLevelInfo.refererInfo,origin:t._origin,originVersion:t._pageLevelInfo.apiVersion,isUsingCdn:t._pageLevelInfo.isUsingCdn,abTesting:e.abTesting,provider:e.provider,refreshInSeconds:e.refreshInSeconds,providedRefreshInSeconds:t.getProvidedOptions().refreshInSeconds,trace:L,consentSource:ne.none,segments:e.segments,invalidSegmentsCount:t.getInvalidSegments()})})()}_ref(){return new("undefined"!=typeof WeakRef?WeakRef:$e)(this)}_setUserId(e,t){var i=2<arguments.length&&void 0!==arguments[2]&&arguments[2],r=e.universal_uid;if(this._isExposed=!0,J(e.ab_testing))switch(e.ab_testing.result){case"normal":break;default:case"error":this._log.error("There was an error with A/B Testing. Make sure controlGroupRatio is a number >= 0 and <= 1");break;case"control":this._isExposed=!1,this._log.info("User is in control group!")}var s=this._userId!==r||!1===ee(this._ext,e.ext)||!1===ee(this._ids,e.ids);this._userIdAvailable=!0,this._userId=r,this._gpId=e.gp,this._ids=e.ids,this._userIdAvailablePromiseResolver(r),this._ext=e.ext,this._publisherTrueLinkId=e.publisherTrueLinkId,this._fromCache=t,this._signature=e.signature,this._log.info("User id updated",{hasChanged:s,fromCache:t}),this._availableCallback&&this._availableCallback.triggerNow(),this._isRefreshing&&this._refreshCallback&&(!1===t||!1===this._isRefreshingWithFetch)&&this._refreshCallback.triggerNow(),s&&this._fireOnUpdate(),this.getOptions().allowGCReclaim!==Be.AFTER_UID_SET||t&&i||Qe.releaseInstance(this)}_fireOnUpdate(){setTimeout(()=>{W(this._updateCallback)&&(this._log.debug("Firing onUpdate"),this._updateCallback(this))},0)}}class et{static gatherUaHints(e,t){return s(function*(){if(E(window.navigator.userAgentData)&&!e){let e;try{e=yield window.navigator.userAgentData.getHighEntropyValues(["architecture","fullVersionList","model","platformVersion"])}catch(e){return void t.error("Error while calling navigator.userAgentData.getHighEntropyValues()",e)}return et.filterUaHints(e)}})()}static filterUaHints(e){if(E(e)){const t=/[()-.:;=?_/]/g;return y(e.brands)&&(e.brands=e.brands.filter(e=>S(e.brand)&&e.brand.search(t)<0)),y(e.fullVersionList)&&(e.fullVersionList=e.fullVersionList.filter(e=>S(e.brand)&&e.brand.search(t)<0)),e}}}function tt(e,t,i){return e.timer("fetch.call.time",h({status:t},2<arguments.length&&void 0!==i?i:{}))}function it(e,t,i,r){return e.timer("extensions.call.time",h({extensionType:t,status:i?"success":"fail"},3<arguments.length&&void 0!==r?r:{}))}function rt(e,t,i){return e.counter("instance.lateJoin.count",h({instanceId:t},2<arguments.length&&void 0!==i?i:{}))}const st="*";class nt{constructor(e,t,i,r,s,n){var o=6<arguments.length&&void 0!==arguments[6]?arguments[6]:void 0;d(this,"_isId5Message",!0),d(this,"id",void 0),d(this,"timestamp",void 0),d(this,"type",void 0),d(this,"src",void 0),d(this,"dst",void 0),d(this,"request",void 0),d(this,"payload",void 0),this.id=r,this.timestamp=e,this.src=t,this.dst=i,this.type=n,this.request=o,this.payload=s}}class ot{constructor(e){d(this,"_senderId",void 0),d(this,"_messageSeqNb",0),this._senderId=e,this._messageSeqNb=0}createBroadcastMessage(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:e.constructor.name;return new nt(Date.now(),this._senderId,void 0,++this._messageSeqNb,e,t||e.constructor.name)}createResponse(e,t){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:t.constructor.name;return new nt(Date.now(),this._senderId,e.src,++this._messageSeqNb,t,i||t.constructor.name,e)}createUnicastMessage(e,t){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:t.constructor.name;return new nt(Date.now(),this._senderId,e,++this._messageSeqNb,t,i||t.constructor.name)}}class at{constructor(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1],i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:void 0;d(this,"instance",void 0),d(this,"instanceState",void 0),d(this,"isResponse",void 0),this.instance=e,this.instanceState=i,this.isResponse=t}}d(at,"TYPE","HelloMessage");const ct=Object.freeze({LEADER:"leader",FOLLOWER:"follower",STORAGE:"storage"});class lt{constructor(e,t,i){d(this,"target",void 0),d(this,"methodName",void 0),d(this,"methodArguments",void 0),this.target=e,this.methodName=t,this.methodArguments=i}}d(lt,"TYPE","RemoteMethodCallMessage");class dt{constructor(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:p;d(this,"_targets",{}),d(this,"_log",void 0),this._log=e}registerTarget(e,t){return this._targets[e]=t,this}_handle(t){const e=this._targets[t.target];if(e)try{e[t.methodName](...t.methodArguments)}catch(e){this._log.error("Error while handling method call ",t,e)}}}class ht{constructor(e,t){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:p,r=3<arguments.length?arguments[3]:void 0;d(this,"_id",void 0),d(this,"_messageFactory",void 0),d(this,"_log",void 0),d(this,"_metrics",void 0),d(this,"_onMessageCallBackFunction",void 0),this._id=e,this._messageFactory=new ot(this._id),this._log=i,this._window=t,this._handlers={},this._metrics=r,this._register()}_register(){const s=this;s._abortController="undefined"!=typeof AbortController?new AbortController:void 0;var e=null===(e=s._abortController)||void 0===e?void 0:e.signal;s._window.addEventListener("message",i=>{let r=i.data;if(void 0!==i.data&&i.data._isId5Message&&i.data.src!==s._id&&(void 0===i.data.dst||i.data.dst===s._id))try{[st,r.type].forEach(e=>{let t=s._handlers[e];t&&t.forEach(e=>e(r,i.source))})}catch(e){s._log.error("Error while handling message",r,e)}},{capture:!1,signal:e})}unregister(){this._abortController&&this._abortController.abort()}onAnyMessage(e){return this.onMessage(st,e)}onMessage(e,t){const i=this._handlers[e];return i?i.push(t):this._handlers[e]=[t],this}broadcastMessage(e,t){this._log.debug("Broadcasting message",t,e),this._postMessage(this._messageFactory.createBroadcastMessage(e,t))}sendResponseMessage(e,t){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:t.constructor.name;this._log.debug("Sending response message",e,i,t),this._postMessage(this._messageFactory.createResponse(e,t,i))}unicastMessage(e,t){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:t.constructor.name;this._log.debug("Sending message to",e,i,t),this._postMessage(this._messageFactory.createUnicastMessage(e,t,i))}_postToWindow(e,t){try{e.postMessage(t,"*")}catch(e){this._log.error("Could not post message to window",e)}}_postMessage(r){let s=this;(function t(e){try{s._postToWindow(e,r);var i=e.frames;if(i)for(let e=0;e<i.length;e++)t(i[e])}catch(e){s._log.error("Could not broadcast message",e)}})(s._window.top)}callProxyMethod(e,t,i,r){this._log.debug("Calling ProxyMethodCall",{target:t,name:i,args:r}),this.unicastMessage(e,new lt(t,i,r),lt.TYPE)}onProxyMethodCall(t){return this.onMessage(lt.TYPE,e=>void 0===e.dst?(this._countInvalidMessage(e,"no-destination-proxy"),void this._log.error("Received invalid RemoteMethodCallMessage message",JSON.stringify(e),"Ignoring it....")):void t._handle(u(new lt,e.payload)))}_countInvalidMessage(e,t){var i=e=>null!=e;void 0!==this._metrics&&!function(e,t){return e.counter("instance.message.invalid.count",1<arguments.length&&void 0!==t?t:{})}(this._metrics,{reason:t,hasDestination:i(e.dst),hasSource:i(e.src),hasPayload:i(e.payload),hasRequest:i(e.request),hasTimestamp:i(e.timestamp)}).inc()}}const ut=Object.freeze({STORAGE_CONFIG:{ID5:{name:"id5id",expiresDays:90},ID5_V2:{name:"id5id_v2",expiresDays:15},LAST:{name:"id5id_last",expiresDays:90},CONSENT_DATA:{name:"id5id_cached_consent_data",expiresDays:30},PRIVACY:{name:"id5id_privacy",expiresDays:30},EXTENSIONS:{name:"id5id_extensions",expiresDays:8/24}},LEGACY_COOKIE_NAMES:["id5.1st","id5id.1st"],PRIVACY:{JURISDICTIONS:{gdpr:!0,ccpa:!1,lgpd:!0,other:!1}},ID5_EIDS_SOURCE:"id5-sync.com"});class gt{constructor(e,t){this.name=e,this.expiresDays=t}withNameSuffixed(){let e=this.name;for(var t=arguments.length,i=new Array(t),r=0;r<t;r++)i[r]=arguments[r];for(var s=0,n=i;s<n.length;s++){var o=n[s];e+="_".concat(o)}return new gt(e,this.expiresDays)}}class pt{constructor(){let i=0<arguments.length&&void 0!==arguments[0]?arguments[0]:void 0;function e(e){var t=void 0!==i?Math.max(1,i):e.expiresDays;return new gt(e.name,t)}var t=ut.STORAGE_CONFIG;this.ID5=e(t.ID5),this.ID5_V2=e(t.ID5_V2),this.LAST=e(t.LAST),this.CONSENT_DATA=e(t.CONSENT_DATA),this.PRIVACY=e(t.PRIVACY),this.EXTENSIONS=new gt(t.EXTENSIONS.name,t.EXTENSIONS.expiresDays)}}d(pt,"DEFAULT",new pt);class _t{constructor(e,t){d(this,"_clientStore",void 0),d(this,"_trueLinkAdapter",void 0),this._clientStore=e,this._trueLinkAdapter=t}hasConsentChanged(e){return e&&!this._clientStore.storedConsentDataMatchesConsentData(e)}storeConsent(e){this._clientStore.putHashedConsentData(e)}incNb(e){this._clientStore.incNbV2(e,1<arguments.length&&void 0!==arguments[1]?arguments[1]:1)}updateNbs(e){var t,i=f(e);try{for(i.s();!(t=i.n()).done;){var r=g(t.value,2),s=r[0],n=r[1],o=null==n?void 0:n.nb;0<o&&this.incNb(s,-o)}}catch(e){i.e(e)}finally{i.f()}}storeResponse(e,i,r){this._clientStore.putResponseV1(i.getGenericResponse()),this._clientStore.setResponseDateTimeV1(new Date(i.timestamp).toUTCString());const s=new Set;e.forEach(e=>{var t=e.cacheId;s.has(t)||(e=i.getResponseFor(e.integrationId))&&(this._clientStore.storeResponseV2(t,e,i.timestamp,r),s.add(t))}),this._trueLinkAdapter.setPrivacy(null===(e=i.getGenericResponse())||void 0===e?void 0:e.privacy)}clearAll(e){this._clientStore.clearResponse(),this._clientStore.clearDateTime(),e.forEach(e=>{e=e.cacheId;this._clientStore.clearResponseV2(e)}),this._clientStore.clearHashedConsentData(),this._trueLinkAdapter.clearPrivacy(),this._clientStore.clearExtensions()}getCachedResponse(e){e=this._clientStore.getStoredResponseV2(e);if(e)return new vt(e.response,e.responseTimestamp,e.nb,e.consents)}getCachedExtensions(){return this._clientStore.getExtensions()}storeExtensions(e){var t=b(e.ttl)?e.ttl/86400:pt.DEFAULT.EXTENSIONS.expiresDays,t=new gt(pt.DEFAULT.EXTENSIONS.name,t);return this._clientStore.storeExtensions(e,t)}}class vt{constructor(e,t){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:0,r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:void 0;d(this,"response",void 0),d(this,"timestamp",void 0),d(this,"nb",void 0),d(this,"consents",void 0),this.response=e,this.timestamp=t,this.nb=i,this.consents=r}isExpired(){var e=this.getMaxAge();return!(b(e)&&0<e)||this._isOlderThanSec(e)}_isOlderThanSec(e){return this.timestamp<=0||this.getAgeSec()>e}isStale(){return!this.timestamp||this._isOlderThanSec(1209600)}isResponseComplete(){return D(this.response)&&S(this.response.universal_uid)&&S(this.response.signature)}isValid(){return this.isResponseComplete()&&!this.isStale()}getMaxAge(){var e,t;return null===(e=this.response)||void 0===e||null===(t=e.cache_control)||void 0===t?void 0:t.max_age_sec}getAgeSec(){return(Date.now()-this.timestamp)/1e3|0}}const ft="_exp";class mt{constructor(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:p;d(this,"storage",void 0),d(this,"_log",void 0),this.storage=e,this._log=t}getItem(e){try{return this.storage.getItem(e)}catch(e){}}setItem(e,t){try{this.storage.setItem(e,t)}catch(e){}}removeItem(e){try{this.storage.removeItem(e)}catch(e){}}removeExpiredObjectWithPrefix(t){var i=1<arguments.length&&void 0!==arguments[1]&&arguments[1];this._log.info("Check, prefix",t);try{var r=this.storage.getKeysWithPrefix(t);let e=0;var s,n=f(r);try{for(n.s();!(s=n.n()).done;){var o,a,c=s.value;i?(this._log.info("Found",c," remove it"),this.removeItem(c)):(a=null==(o=this.getObjectWithExpiration({name:c}))?void 0:o.expireAt)&&a<Date.now()&&(this._log.info("Found expired object",c,"expiration time",a,"It will be removed"),this.removeItem(c),e+=1)}}catch(e){n.e(e)}finally{n.f()}return{all:r.length,expired:e}}catch(e){}}getItemWithExpiration(e){var t=e.name,e=this.getItem(t+ft);return!e||new Date(e).getTime()-Date.now()<=0?(this.removeItemWithExpiration({name:t}),null):this.getItem(t)}setItemWithExpiration(e,t){var i=e.name,e=e.expiresDays,e=Date.now()+864e5*e,e=new Date(e).toUTCString();this.setItem(i+ft,e),this.setItem(i,t)}removeItemWithExpiration(e){e=e.name;this.removeItem(e),this.removeItem(e+ft)}setObjectWithExpiration(e,t){var i=e.name,e=e.expiresDays,e=Date.now()+864e5*e;this.setItem(i,JSON.stringify({data:t,expireAt:e}))}getObjectWithExpiration(t){t=t.name;try{var e=JSON.parse(this.getItem(t));if(null!=e&&e.expireAt&&0<e.expireAt-Date.now())return e.data;null!=e&&e.expireAt&&this.removeItem(t)}catch(e){this._log.error("Error while getting ",t,"object from storage",e)}}updateObjectWithExpiration(e,t){var i=e.name,e=e.expiresDays;try{var r=t(this.getObjectWithExpiration({name:i}));return this.setObjectWithExpiration({name:i,expiresDays:e},r),r}catch(e){this._log.error("Error while updating object with ",i,e)}}}class It{getItem(){}removeItem(){}setItem(){}getKeysWithPrefix(){return[]}}const wt=new It;class Ct extends It{constructor(e){var t=!(1<arguments.length&&void 0!==arguments[1])||arguments[1];super(),d(this,"_writingEnabled",void 0),d(this,"_underlying",void 0),this._writingEnabled=t;try{this._underlying=e.localStorage}catch(e){}}getItem(e){try{return this._underlying.getItem(e)}catch(e){}}removeItem(e){try{this._underlying.removeItem(e)}catch(e){}}setItem(e,t){try{this._writingEnabled&&this._underlying.setItem(e,t)}catch(e){}}getKeysWithPrefix(t){try{var i=this._underlying.length;if(this._writingEnabled){const r=[];for(let e=0;e<i;e++){const s=this._underlying.key(e);s&&s.startsWith(t)&&r.push(s)}return r}}catch(e){}}static checkIfAccessible(){var e="__id5test";try{return window.localStorage.setItem(e,e),window.localStorage.removeItem(e),!0}catch(e){return!1}}}class St{constructor(e){d(this,"_replicas",[]),d(this,"_lastKeyOperation",{}),d(this,"_primaryStorage",void 0),this._primaryStorage=e}getItem(e){return this._primaryStorage.getItem(e)}removeItem(t){this._primaryStorage.removeItem(t);var e=e=>{e.removeItem(t)};this._replicas.forEach(e),this._lastKeyOperation[t]=e}setItem(t,i){this._primaryStorage.setItem(t,i);var e=e=>{e.setItem(t,i)};this._replicas.forEach(e),this._lastKeyOperation[t]=e}addReplica(t){Object.values(this._lastKeyOperation).forEach(e=>e(t)),this._replicas.push(t)}getKeysWithPrefix(e){return this._primaryStorage.getKeysWithPrefix(e)}}class yt{constructor(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:Date.now();d(this,"timestamp",void 0),d(this,"response",void 0),this.response=e,this.timestamp=t}getGenericResponse(){return this.response.generic}getResponseFor(e){var t,i;if(null!==(t=this.response)&&void 0!==t&&t.responses&&null!==(i=this.response)&&void 0!==i&&i.responses[e])return h(h({},this.response.generic),this.response.responses[e])}}class bt{constructor(e,t,i){d(this,"_extensionsProvider",void 0),d(this,"_metrics",void 0),d(this,"_log",void 0),this._extensionsProvider=i,this._metrics=e,this._log=t}fetchId(e,l,d){return this._extensionsProvider.gather(e).then(n=>{const s=e.map(e=>{const t=e.cacheData;var i=null===t||void 0===t||null===(s=t.response)||void 0===s?void 0:s.signature,r=null===t||void 0===t?void 0:t.nb,s=null===t||void 0===t?void 0:t.getMaxAge();return this._createRequest(l,e,i,r,s,n,d)}),o=this._log,a=this._metrics,c=this;return new Promise((t,i)=>{const r=Ee();var e="".concat("https://id5-sync.com").concat("/gm/v3");o.info("Fetching ID5 ID from:",e,s),O(e,{success:function(e){o.info("Success at fetch call:",e),r.record(function(e,t){return tt(e,"success",1<arguments.length&&void 0!==t?t:{})}(a));try{t(new yt(c._validateResponse(e)))}catch(e){i(e)}},error:function(e){r.record(function(e,t){return tt(e,"fail",1<arguments.length&&void 0!==t?t:{})}(a)),i(e)}},JSON.stringify({requests:s}),{method:"POST",withCredentials:!0},o)})})}_validateResponse(e){if(!e||!S(e)||e.length<1)throw new Error('Empty fetch response from ID5 servers: "'.concat(e,'"'));var t=JSON.parse(e);if(!D(t.generic))throw new Error("Server response failed to validate: ".concat(e));return this._log.info("Valid json response from ID5 received",t),t}_createRequest(e,i,t,r,s,n,o){var a,c;this._log.info("Create request data for",{fetchIdData:i,consentData:e,signature:t,nbValue:r,refreshInSecondUsed:s,extensions:n});var l=i.partnerId;const d={requestId:i.integrationId,requestCount:i.requestCount,role:i.role,cacheId:i.cacheId,refresh:i.refresh,source:i.source,sourceVersion:i.sourceVersion,partner:l,v:i.originVersion,o:i.origin,tml:null===(l=i.refererInfo)||void 0===l?void 0:l.topmostLocation,ref:null===(l=i.refererInfo)||void 0===l?void 0:l.ref,cu:null===(l=i.refererInfo)||void 0===l?void 0:l.canonicalUrl,u:(null===(l=i.refererInfo)||void 0===l||null===(a=l.stack)||void 0===a?void 0:a[0])||(null===(c=window.location)||void 0===c?void 0:c.href),top:null!==(c=i.refererInfo)&&void 0!==c&&c.reachedTop?1:0,localStorage:!0===o?1:0,nbPage:r,id5cdn:i.isUsingCdn,ua:window.navigator.userAgent,att:i.att};r=e.gdprApplies;E(r)&&(d.gdpr=r?1:0);r=e.consentString;E(r)&&(d.gdpr_consent=r),E(i.allowedVendors)?d.allowed_vendors=i.allowedVendors:E(e.allowedVendors)&&(d.allowed_vendors=e.allowedVendors),E(e.gppData)&&(d.gpp_string=e.gppData.gppString,d.gpp_sid=e.gppData.applicableSections.join(",")),E(t)&&(d.s=t);t=i.uaHints;E(t)&&(d.ua_hints=t),E(e.ccpaString)&&""!==e.ccpaString&&(d.us_privacy=e.ccpaString),Object.entries({pd:"pd",partnerUserId:"puid",provider:"provider",segments:"segments",trueLink:"true_link"}).forEach(e=>{var t=g(e,2),e=t[0],t=t[1];E(i[e])&&(d[t]=i[e])});e=i.abTesting;e&&!0===e.enabled&&(d.ab_testing={enabled:!0,control_group_pct:e.controlGroupPct});e=i.invalidSegmentsCount;return e&&0<e&&(d._invalid_segments=e),i.trace&&(d._trace=!0),d.provided_options={refresh_in_seconds:i.providedRefreshInSeconds},d.used_refresh_in_seconds=s,d.extensions=n,d}}const Dt=Object.freeze({DIRECT_METHOD:"direct_method",POST_MESSAGE:"post_message"}),Et=Object.freeze({STANDARD:"follower",PASSIVE:"follower-passive"});class At{constructor(e,t,i){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:p,s=4<arguments.length&&void 0!==arguments[4]?arguments[4]:Et.STANDARD;d(this,"_instanceProperties",void 0),d(this,"_log",void 0),d(this,"callType",void 0),d(this,"_instanceWindow",void 0),this._instanceWindow=t,this._instanceProperties=i,this._log=r,this.callType=e,this.type=s}getId(){return this._instanceProperties.id}getFetchIdData(){return this._instanceProperties.fetchIdData}updateFetchIdData(e){u(this._instanceProperties.fetchIdData,e)}getCacheId(){var e=this._instanceProperties.fetchIdData,e={partnerId:e.partnerId,att:e.att,pd:e.pd,provider:e.provider,abTesting:e.abTesting,segments:JSON.stringify(e.segments),providedRefresh:e.providedRefreshInSeconds,trueLink:null===(e=e.trueLink)||void 0===e?void 0:e.id};return R(JSON.stringify(e))}getDeclaredConsentSource(){return this._instanceProperties.fetchIdData.consentSource||ne.cmp}getSourceVersion(){return this._instanceProperties.sourceVersion}getSource(){return this._instanceProperties.source}notifyUidReady(){}notifyFetchUidCanceled(){}notifyCascadeNeeded(){}canDoCascade(){return!0===this._instanceProperties.canDoCascade}getStorage(){return wt}getWindow(){return this._instanceWindow}}class Tt extends At{constructor(e,t,i){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:p,s=4<arguments.length?arguments[4]:void 0;super(Dt.DIRECT_METHOD,e,t,r,Et.STANDARD),d(this,"_dispatcher",void 0),d(this,"_provisionedUids",void 0),d(this,"_metrics",void 0),this._dispatcher=i,this._metrics=s,this._provisionedUids=new Map}notifyUidReady(e,t){var i,r=null==e||null===(i=e.responseObj)||void 0===i?void 0:i.universal_uid;r&&(this._provisionedUids.has(r)?(i=this._provisionedUids.get(r),function(e,t){return e.timer("userid.provisioning.duplicate",h({},1<arguments.length&&void 0!==t?t:{}))}(this._metrics,{provisioner:t.provisioner,firstProvisioner:i.provisioner}).record(performance.now()-i.time)):(this._provisionedUids.set(r,{provisioner:t.provisioner,time:performance.now()}),this._dispatcher.emit(Ge.USER_ID_READY,e,t)))}notifyFetchUidCanceled(e){this._dispatcher.emit(Ge.USER_ID_FETCH_CANCELED,e)}notifyCascadeNeeded(e){this._dispatcher.emit(Ge.CASCADE_NEEDED,e)}}class Pt{constructor(e,t,i,r){d(this,"_store",void 0),d(this,"_log",void 0),d(this,"_provisioner",void 0),d(this,"_meter",void 0),this._provisioner=e,this._store=t,this._log=i,this._meter=r}provisionFromCache(t){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:void 0;try{const o=this._log;var r=t.getCacheId();const a=this._store.getCachedResponse(r);var s,n=!a||!a.isValid()||a.isExpired();let e=!1;return a&&(s=a.getAgeSec(),function(e,t){return e.summary("userid.cached.age",h({},1<arguments.length&&void 0!==t?t:{}))}(this._meter,{expired:a.isExpired(),valid:a.isValid(),provisioner:this._provisioner,maxAge:a.getMaxAge()}).record(isNaN(s)?0:s)),a&&a.isValid()?(o.info("Found valid cached response for instance ",JSON.stringify({id:t.getId(),cacheId:t.getCacheId(),provisioner:this._provisioner,responseFromCache:a})),t.notifyUidReady({timestamp:a.timestamp,responseObj:a.response,isFromCache:!0,consents:a.consents,willBeRefreshed:!!n},{timestamp:Date.now(),provisioner:this._provisioner,tags:h({callType:t.callType},i)}),e=!0):o.info("Couldn't find response for cacheId",t.getCacheId()),{cacheId:r,responseFromCache:a,refreshRequired:n,provisioned:e}}catch(e){return this._log.error("Cached UserId provisioning failure",e),{refreshRequired:!0,provisioned:!1}}}}class Ot{constructor(){var e=0<arguments.length&&void 0!==arguments[0]&&arguments[0],t=1<arguments.length&&void 0!==arguments[1]&&arguments[1];d(this,"lateJoiner",!1),d(this,"uniqueLateJoiner",!1),this.lateJoiner=e,this.uniqueLateJoiner=t}}class Rt{updateConsent(){}updateFetchIdData(){}refreshUid(){}addFollower(){}getProperties(){}}class xt extends Rt{constructor(e,t,i,r,s,n){var o=6<arguments.length&&void 0!==arguments[6]?arguments[6]:p,a=7<arguments.length?arguments[7]:void 0;super(),d(this,"_followers",void 0),d(this,"_followersRequests",{}),d(this,"_refreshRequired",{}),d(this,"_fetcher",void 0),d(this,"_log",void 0),d(this,"_consentManager",void 0),d(this,"_inProgressFetch",void 0),d(this,"_queuedRefreshArgs",void 0),d(this,"_metrics",void 0),d(this,"_leaderStorage",void 0),d(this,"_store",void 0),this._followers=[],this._fetcher=a,this._properties=t,this._consentManager=s,this._metrics=n,this._window=e,this._leaderStorage=i,this._log=o,this._store=r,this._firstFetchTriggered=!1,this._cachedIdProvider=new Pt("leader",this._store,this._log,this._metrics)}_handleRefreshResult(e,t,i,r){const s=this._log,n=this._consentManager,o=this._store;if(void 0!==r){var a;n.setStoredPrivacy(null===(a=r.getGenericResponse())||void 0===a?void 0:a.privacy);const p=n.localStorageGrant("fetcher-after-response");p.isDefinitivelyAllowed()?(s.info("Storing ID and request hashes in cache"),o.updateNbs(t),o.storeResponse(e,r,null==i?void 0:i.toConsents())):(s.info("Cannot use local storage to cache ID",p),o.clearAll(e));var c,l=f(e);try{for(l.s();!(c=l.n()).done;){var d=c.value.integrationId;this._followersRequests[d]=(this._followersRequests[d]||0)+1}}catch(e){l.e(e)}finally{l.f()}const _=[];var h,u=f(this._followers);try{for(u.s();!(h=u.n()).done;){const v=h.value;var g=r.getResponseFor(v.getId());void 0!==g&&(this._log.debug("Notify uid ready.","Follower:",v.getId(),"Uid:",g),this._refreshRequired[v.getId()]=!1,this._notifyUidReady(v,{timestamp:r.timestamp,responseObj:g,isFromCache:!1,consents:null==i?void 0:i.toConsents()}),!0===g.cascade_needed&&_.push(v.getId()))}}catch(e){u.e(e)}finally{u.f()}void 0!==i&&0<_.length&&this._consentManager.localStorageGrant("leader-before-cascade").isDefinitivelyAllowed()&&this._handleCascade(_,r,i)}}_notifyUidReady(e,t){var i={timestamp:Date.now(),provisioner:"leader",tags:{callType:e.callType}};e.notifyUidReady(t,i)}_handleCascade(e,t,i){var r,s=this._followers.filter(t=>void 0!==e.find(e=>t.getId()===e)&&t.canDoCascade()).sort((e,t)=>{function i(e){var t;return(null===(e=e.getFetchIdData().refererInfo)||void 0===e||null===(t=e.stack)||void 0===t?void 0:t.length)||Number.MAX_SAFE_INTEGER}return i(e)-i(t)});if(0<s.length){const n=s[0];n.notifyCascadeNeeded({partnerId:n.getFetchIdData().partnerId,userId:t.getResponseFor(n.getId()).universal_uid,gdprApplies:i.gdprApplies,consentString:i.consentString,gppString:null===(t=i.gppData)||void 0===t?void 0:t.gppString,gppSid:null===(i=i.gppData)||void 0===i||null===(r=i.applicableSections)||void 0===r?void 0:r.join(",")})}else this._log.error("Couldn't find cascade eligible follower")}_handleCancel(e){var t,i=f(this._followers);try{for(i.s();!(t=i.n()).done;){const r=t.value;r.notifyFetchUidCanceled({reason:e})}}catch(e){i.e(e)}finally{i.f()}}_getId(){let n=0<arguments.length&&void 0!==arguments[0]&&arguments[0];const o=this._log;this._waitForConsent().then(t=>{const e=this._consentManager.localStorageGrant("fetch-before-request");if(o.info("Local storage grant",e),e.allowed){var i=this._store.hasConsentChanged(t);e.isDefinitivelyAllowed()&&this._store.storeConsent(t);var r=Ct.checkIfAccessible();const c=new Map;let a=n;const s=this._followers.map(e=>{var t=e.getId(),i=(this._followersRequests[t]||0)+1,r=this._properties.id,s=!0===this._refreshRequired[e.getId()];a=a||s;var n,o=e.getCacheId();return c.has(o)||(n=this._store.getCachedResponse(o))&&c.set(o,n),h(h({},e.getFetchIdData()),{},{integrationId:t,requestCount:i,refresh:s,role:r===e.getId()?"leader":e.type||"follower",cacheId:o,cacheData:c.get(o),sourceVersion:e.getSourceVersion(),source:e.getSource()})});i||a?(o.info("Decided to fetch a fresh ID5 ID",{consentHasChanged:i,shouldRefresh:a}),o.info("Fetching ID5 ID (forceFetch:".concat(n,")")),this._inProgressFetch=!0,this._firstFetchTriggered=!0,this._fetcher.fetchId(s,t,r).then(e=>{this._handleRefreshResult(s,c,t,e),this._handleFetchCompleted()}).catch(e=>{this._handleFailed(e),this._handleFetchCompleted()})):(o.info("Not decided to refresh ID5 ID",{consentHasChanged:i,shouldRefresh:a}),this._handleFetchCompleted())}else o.info("No legal basis to use ID5",t),this._store.clearAll(this._followers.map(e=>({cacheId:e.getCacheId()}))),this._handleCancel("No legal basis to use ID5")})}_waitForConsent(){const t=this._log,e=this._consentManager,i=this._metrics;t.info("Waiting for consent");const r=i.timer("fetch.consent.wait.time");return e.getConsentData().then(e=>(t.info("Consent received",e),r&&r.recordNow(),e))}start(){!0!==this._started&&(this._getId(!1),this._started=!0)}refreshUid(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};let t=1<arguments.length?arguments[1]:void 0;var i=!0===e.forceFetch;t&&(i?this._refreshRequired[t]=!0:(i=this._followers.find(e=>e.getId()===t))&&this._provisionFromCache(i)),function(e,t,i){return e.counter("refresh.call.count",h({target:t},2<arguments.length&&void 0!==i?i:{}))}(this._metrics,"leader",{overwrites:void 0===this._queuedRefreshArgs}).inc(),this._callRefresh(e,t)}_callRefresh(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=1<arguments.length?arguments[1]:void 0;this._inProgressFetch?this._queuedRefreshArgs=[e,t]:(!0===e.resetConsent&&(this._consentManager.resetConsentData(!0===e.forceAllowLocalStorageGrant),this._awaitedConsentFrom=t),this._getId(!0===e.forceFetch))}updateConsent(e,t){if(this._consentManager.hasConsentSet())this._handleIgnoredConsentUpdate(e);else{const s=new Set(this._followers.map(e=>e.getDeclaredConsentSource()).filter(e=>e!==ne.none));var i=e.source||ne.cmp,r=1===s.size&&s.has(ne.partner);this._awaitedConsentFrom?this._awaitedConsentFrom===t?(this._consentManager.setConsentData(e),this._awaitedConsentFrom=void 0):this._handleIgnoredConsent(e,"awaited"):i!==ne.partner||r?this._consentManager.setConsentData(e):this._handleIgnoredConsent(e,"partner")}}_handleIgnoredConsentUpdate(e){try{const r=this._consentManager._consentDataHolder.getValue();if(r){const s={},n=le.createFrom(e);Object.values(se).forEach(e=>{var t,i;n.apiTypes.includes(e)&&r.apiTypes.includes(e)?(t=JSON.stringify(r.getApiTypeData(e)),i=JSON.stringify(n.getApiTypeData(e)),s[e]=t===i?"same":"different"):n.apiTypes.includes(e)?s[e]="added":r.apiTypes.includes(e)&&(s[e]="missed")}),function(e,t){return e.counter("leader.consent.change.count",1<arguments.length&&void 0!==t?t:{})}(this._metrics,s).inc()}}catch(e){this._log.error(e)}}_handleIgnoredConsent(e,t){try{const i={reason:t,source:e.source};e.apiTypes.forEach(e=>i[e]=!0),function(e,t){return e.counter("leader.consent.ignore.count",1<arguments.length&&void 0!==t?t:{})}(this._metrics,i).inc()}catch(e){this._log.error(e)}}updateFetchIdData(t,e){const i=this._followers.find(e=>e.getId()===t);var r=i.getCacheId();i.updateFetchIdData(e);e=i.getCacheId();e!==r&&(this._log.info("Follower",i.getId(),"cacheId changed from",r," to",e,"required refresh"),this._refreshRequired[i.getId()]=!0)}addFollower(t){const e=this._log;var i=void 0===this._followers.find(e=>e.getCacheId()===t.getCacheId());this._followers.push(t),e.debug("Added follower",t.getId(),"cacheId",t.getCacheId()),this._window!==t.getWindow()&&(r=t.getStorage(),e.debug("Adding follower's",t.getId(),"storage as replica"),this._leaderStorage.addReplica(r));var r=this._provisionFromCache(t);let s=new Ot;return!0===this._firstFetchTriggered&&(s.lateJoiner=!0,s.uniqueLateJoiner=i,r&&this._callRefresh({forceFetch:!0})),s}_provisionFromCache(e){var t=this._cachedIdProvider.provisionFromCache(e);return this._refreshRequired[e.getId()]=t.refreshRequired,t.provisioned&&this._store.incNb(t.cacheId),t.refreshRequired}getProperties(){return this._properties}_handleFetchCompleted(){this._inProgressFetch=void 0,this._queuedRefreshArgs&&(this._callRefresh(...this._queuedRefreshArgs),this._queuedRefreshArgs=void 0)}_handleFailed(e){this._log.error("Fetch id failed",e);var t,i=f(this._followers);try{for(i.s();!(t=i.n()).done;){const r=t.value;r.notifyFetchUidCanceled({reason:"error"})}}catch(e){i.e(e)}finally{i.f()}}}class Lt extends Rt{constructor(e,t){super(),d(this,"_messenger",void 0),d(this,"_leaderInstanceProperties",void 0),this._messenger=e,this._leaderInstanceProperties=t}_sendToLeader(e,t){this._messenger.callProxyMethod(this._leaderInstanceProperties.id,ct.LEADER,e,t)}updateConsent(e,t){this._sendToLeader("updateConsent",[e,t])}refreshUid(e,t){this._sendToLeader("refreshUid",[e,t])}updateFetchIdData(e,t){this._sendToLeader("updateFetchIdData",[e,t])}getProperties(){return this._leaderInstanceProperties}}class Ft extends Rt{constructor(){super(...arguments),d(this,"_callsQueue",[]),d(this,"_assignedLeader",void 0)}updateConsent(e,t){this._callOrBuffer("updateConsent",[e,t])}updateFetchIdData(e,t){this._callOrBuffer("updateFetchIdData",[e,t])}refreshUid(e,t){this._callOrBuffer("refreshUid",[e,t])}addFollower(e){return this._callOrBuffer("addFollower",[e])}getProperties(){if(this._assignedLeader)return this._assignedLeader.getProperties()}assignLeader(e){this._assignedLeader=e;var t,i=f(this._callsQueue);try{for(i.s();!(t=i.n()).done;){var r=t.value;this._callAssignedLeader(r.name,r.args)}}catch(e){i.e(e)}finally{i.f()}this._callsQueue=[]}_callOrBuffer(e,t){if(this._assignedLeader)return this._callAssignedLeader(e,t);this._callsQueue.push({name:e,args:t})}_callAssignedLeader(e,t){return this._assignedLeader[e](...t)}}class Nt extends It{constructor(e,t){super(),d(this,"_messenger",void 0),d(this,"_destinationId",void 0),this._messanger=e,this._destinationId=t}getItem(){}removeItem(e){this._remoteCall("removeItem",[e])}setItem(e,t){this._remoteCall("setItem",[e,t])}_remoteCall(e,t){this._messanger.callProxyMethod(this._destinationId,ct.STORAGE,e,t)}}class Ut extends At{constructor(e,t){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:p,r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:Et.STANDARD;super(Dt.POST_MESSAGE,e.getWindow(),e.properties,i,r),d(this,"_messenger",void 0),this._messenger=t}_callProxy(e,t){this._messenger.callProxyMethod(this.getId(),ct.FOLLOWER,e,t)}notifyUidReady(e,t){this._callProxy("notifyUidReady",[e,t])}notifyFetchUidCanceled(e){this._callProxy("notifyFetchUidCanceled",[e])}notifyCascadeNeeded(e){this._callProxy("notifyCascadeNeeded",[e])}getStorage(){return new Nt(this._messenger,this.getId())}}class kt{constructor(){d(this,"_valuePromise",void 0),d(this,"_value",void 0),d(this,"_resolve",void 0),d(this,"_hasValue",void 0),this.reset()}reset(){this._value=void 0,this._hasValue=!1,this._valuePromise=new Promise(e=>{this._resolve=e})}set(e){this._hasValue?this._valuePromise=Promise.resolve(e):(this._hasValue=!0,this._resolve(e)),this._value=e}getValuePromise(){return this._valuePromise}hasValue(){return this._hasValue}getValue(){return this._value}}class Vt extends class{getConsentData(){}localStorageGrant(){}setStoredPrivacy(){}}{constructor(e,t,i,r,s){super(),d(this,"_consentDataHolder",void 0),d(this,"storedPrivacyData",void 0),d(this,"localStorage",void 0),d(this,"_forceAllowLocalStorageGrant",void 0),this._log=r,this.localStorage=e,this.storageConfig=t,this._consentDataHolder=new kt,this._forceAllowLocalStorageGrant=i,this._metrics=s}isForceAllowLocalStorageGrant(){return this._forceAllowLocalStorageGrant}resetConsentData(e){this._consentDataHolder.reset(),this.storedPrivacyData=void 0,this._forceAllowLocalStorageGrant=e}localStorageGrant(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"unknown",t=this._getLocalStorageGrant();return null!==(e=function(e,t){return e.counter("consent.lsg.count",1<arguments.length&&void 0!==t?t:{})}(this._metrics,h(h({allowed:t.allowed,grantType:t.grantType,lsgContext:e,consentSet:null===(e=this._consentDataHolder)||void 0===e?void 0:e.hasValue()},t.api),t._debugInfo)))&&void 0!==e&&e.inc(),t}_getLocalStorageGrant(){const e=this._log;if(!0===this._forceAllowLocalStorageGrant)return e.warn("cmpApi: Local storage access granted by configuration override, consent will not be checked"),new he(!0,de.FORCE_ALLOWED_BY_CONFIG);if(this._consentDataHolder.hasValue()&&0!==this._consentDataHolder.getValue().apiTypes.length)return this._consentDataHolder.getValue().localStorageGrant();if(D(this.storedPrivacyData)||(t=this.localStorage.getItemWithExpiration(this.storageConfig.PRIVACY),this.storedPrivacyData=t&&JSON.parse(t),e.info("cmpApi: Loaded stored privacy data from local storage",this.storedPrivacyData)),this.storedPrivacyData&&!0===this.storedPrivacyData.id5_consent)return new he(!0,de.ID5_CONSENT);if(!this.storedPrivacyData||!E(this.storedPrivacyData.jurisdiction))return new he(!0,de.PROVISIONAL);var t=this.storedPrivacyData.jurisdiction,t=t in ut.PRIVACY.JURISDICTIONS&&ut.PRIVACY.JURISDICTIONS[t];return new he(!1===t,de.JURISDICTION)}setStoredPrivacy(e){const t=this._log;try{D(e)?(this.storedPrivacyData=e,this.localStorage.setItemWithExpiration(this.storageConfig.PRIVACY,JSON.stringify(e))):t.error("cmpApi: Cannot store privacy data if it is not an object",e)}catch(e){t.error("cmpApi: Error while storing privacy data",e)}}setConsentData(e){this._log.debug("Set consent data",e);e=le.createFrom(e);this._consentDataHolder.set(e)}getConsentData(){return this._consentDataHolder.getValuePromise()}hasConsentSet(){return this._consentDataHolder.hasValue()}}class Gt{constructor(e,t,i,r){d(this,"localStorageGrantChecker",void 0),d(this,"localStorage",void 0),d(this,"_log",void 0),this.localStorageGrantChecker=e,this.localStorage=t,this.storageConfig=i,this._log=r}get(e){const t=this._log;try{const r=this.localStorageGrant();if(r.isDefinitivelyAllowed()){var i=this.localStorage.getItemWithExpiration(e);return t.info("Local storage get key=".concat(e.name," value=").concat(i)),i}t.warn("clientStore.get() has been called without definitive grant",r)}catch(e){t.error(e)}}_getObject(e){const t=this._log;try{const r=this.localStorageGrant();if(r.isDefinitivelyAllowed()){var i=this.localStorage.getObjectWithExpiration(e);return t.info("Local storage get key=".concat(e.name," value=").concat(i)),i}t.warn("clientStore.get() has been called without definitive grant",r)}catch(e){t.error(e)}}clear(e){const t=this._log;try{this.localStorage.removeItemWithExpiration(e)}catch(e){t.error(e)}}scheduleGC(t){const i=this.localStorageGrant(),r=this.localStorage,s=this.storageConfig.ID5_V2.name;setTimeout(function(){var e;i.isDefinitivelyAllowed()&&(e=r.removeExpiredObjectWithPrefix(s),function(e,t){return e.summary("storage.keys.all.count",1<arguments.length&&void 0!==t?t:{})}(t).record((null==e?void 0:e.all)||0),function(e,t){return e.summary("storage.keys.expired.count",1<arguments.length&&void 0!==t?t:{})}(t).record((null==e?void 0:e.expired)||0))},0)}_clearObject(e){const t=this._log;try{this.localStorage.removeItem(e.name)}catch(e){t.error(e)}}_put(e,t){const i=this._log;try{const r=this.localStorageGrant();r.isDefinitivelyAllowed()?(i.info("Local storage put key=".concat(e.name," value=").concat(t)),this.localStorage.setItemWithExpiration(e,t)):i.warn("clientStore._put() has been called without definitive grant",r)}catch(e){i.error(e)}}_updateObject(e,t){const i=this._log;try{const r=this.localStorageGrant();if(r.isDefinitivelyAllowed())return this.localStorage.updateObjectWithExpiration(e,t);i.warn("clientStore._updateObject() has been called without definitive grant",r)}catch(e){i.error(e)}}localStorageGrant(){return this.localStorageGrantChecker()}getResponse(){var e=this.get(this.storageConfig.ID5);return e&&JSON.parse(decodeURIComponent(e))}clearResponse(){this.clear(this.storageConfig.ID5)}clearResponseV2(e){this._clearObject(this.storageConfig.ID5_V2.withNameSuffixed(e))}putResponseV1(e){this._put(this.storageConfig.ID5,encodeURIComponent(S(e)?e:JSON.stringify(e)))}getHashedConsentData(){return this.get(this.storageConfig.CONSENT_DATA)}clearHashedConsentData(){this.clear(this.storageConfig.CONSENT_DATA)}putHashedConsentData(e){e!==new le&&this._put(this.storageConfig.CONSENT_DATA,e.hashCode())}clearDateTime(){this.clear(this.storageConfig.LAST)}setResponseDateTimeV1(e){this._put(this.storageConfig.LAST,e)}storeResponseV2(e,t){let i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:Date.now(),r=3<arguments.length?arguments[3]:void 0;return this._updateObject(this.storageConfig.ID5_V2.withNameSuffixed(e),e=>h(h({},e),{},{response:t,responseTimestamp:i,consents:r}))}getStoredResponseV2(e){return this._getObject(this.storageConfig.ID5_V2.withNameSuffixed(e))}incNbV2(e){let i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:1;return this._updateObject(this.storageConfig.ID5_V2.withNameSuffixed(e),e=>{var t=Math.max(0,b(null==e?void 0:e.nb)?Math.round(e.nb)+i:i);return h(h({},e),{},{nb:t})})}static storedDataMatchesCurrentData(e,t){return null==e||e===t}storedConsentDataMatchesConsentData(e){return Gt.storedDataMatchesCurrentData(this.getHashedConsentData(),e.hashCode())}getExtensions(){return this._getObject(this.storageConfig.EXTENSIONS)}storeExtensions(e,t){return this._updateObject(t,()=>e)}clearExtensions(){return this.clear(this.storageConfig.EXTENSIONS)}}class Mt{constructor(e,t,i){d(this,"_metrics",void 0),d(this,"_log",void 0),d(this,"_store",void 0),this._metrics=e,this._log=t,this._store=i}static getChunkUrl(e,t){return"https://d".concat(e,".eu-").concat(t,"-id5-sync.com")}submitExtensionCall(t,i){var e=2<arguments.length&&void 0!==arguments[2]?arguments[2]:void 0;let r=Ee();return fetch(t,e).then(e=>{if(e.ok)return r.record(it(this._metrics,i,!0)),e.json();r.record(it(this._metrics,i,!1));e="The call to get extensions at ".concat(t," was not ok, status: ").concat(e.status,", statusText: ").concat(e.statusText);return this._log.warn(e),Promise.reject(new Error(e))}).catch(e=>(r.record(it(this._metrics,i,!1)),this._log.warn("Got error from ".concat(t," endpoint"),e),{}))}gatherChunks(e,i){if(e){let t=Ee();return Promise.all(Array.from({length:i.length},(e,t)=>{t=Mt.getChunkUrl(t,i.urlVersion);return fetch(t).then(e=>{if(e.ok)return e.text();throw new Error("The call to get ".concat(i.name," was not ok, status: ").concat(e.status,", statusText: ").concat(e.statusText))})})).then(e=>(t.record(it(this._metrics,i.name,!0)),{[i.name]:e,[i.name+"Version"]:"".concat(i.version)})).catch(e=>(t.record(it(this._metrics,i.name,!1)),this._log.warn("Got error when getting ".concat(i.name),e),{}))}return Promise.resolve({})}gather(e){var t=this._store.getCachedExtensions();if(void 0!==t)return Promise.resolve(t);let i=Ee(),r=this._submitBounce(e),s=this._submitLbs();return this.submitExtensionCall("https://lb.eu-1-id5-sync.com/lb/v1","lb").then(e=>{var t=this.getChunksEnabled(e);return Promise.allSettled([Promise.resolve(e),this.gatherChunks(t,Mt.CHUNKS_CONFIGS.devChunks),this.gatherChunks(t,Mt.CHUNKS_CONFIGS.groupChunks),r,s])}).then(e=>{i.record(it(this._metrics,"all",!0));let t=Mt.DEFAULT_RESPONSE;return e.forEach(e=>{e.value&&(t=h(h({},t),e.value))}),this._store.storeExtensions(t),t}).catch(e=>(i.record(it(this._metrics,"all",!1)),this._log.error("Got error ".concat(e," when gathering extensions data")),Mt.DEFAULT_RESPONSE))}_submitLbs(){const e=new AbortController,t=setTimeout(()=>e.abort(),3e3);let i=this.submitExtensionCall("https://lbs.eu-1-id5-sync.com/lbs/v1","lbs",{signal:e.signal});return i.finally(()=>{clearTimeout(t)})}_submitBounce(e){return e.some(e=>{return E(null===(e=e.cacheData)||void 0===e?void 0:e.signature)})?Promise.resolve({}):this.submitExtensionCall("https://id5-sync.com/bounce","bounce",{credentials:"include"})}getChunksEnabled(e){e=null==e?void 0:e.chunks;return 0!==e&&e}}d(Mt,"CHUNKS_CONFIGS",Object.freeze({devChunks:{name:"devChunks",urlVersion:3,length:8,version:4},groupChunks:{name:"groupChunks",urlVersion:4,length:8,version:4}})),d(Mt,"DEFAULT_RESPONSE",{lbCDN:"%%LB_CDN%%"});const jt={createExtensions:function(e,t,i){return new Mt(e,t,i)}},Wt=Object.freeze({UNKNOWN:"unknown",LEADER:"leader",FOLLOWER:"follower"}),Ht=Object.freeze({MULTIPLEXING:"multiplexing",SINGLETON:"singleton",MULTIPLEXING_PASSIVE:"multiplexing-passive"}),qt=Object.freeze({AWAITING_SCHEDULE:"awaiting_schedule",SKIPPED:"skipped",SCHEDULED:"scheduled",COMPLETED:"completed",CANCELED:"canceled"});class Bt{constructor(e,t,i){d(this,"properties",void 0),d(this,"knownState",void 0),d(this,"_joinTime",void 0),d(this,"_window",void 0),this.properties=e,this.knownState=t,this._window=i,this._joinTime=performance.now()}getId(){return this.properties.id}isMultiplexingPartyAllowed(){var e=null===(e=this.knownState)||void 0===e?void 0:e.operatingMode;return e===Ht.MULTIPLEXING||e===Ht.MULTIPLEXING_PASSIVE}isLeaderCapable(){var e;return(null===(e=this.knownState)||void 0===e?void 0:e.operatingMode)===Ht.MULTIPLEXING}isPassive(){var e;return(null===(e=this.knownState)||void 0===e?void 0:e.operatingMode)===Ht.MULTIPLEXING_PASSIVE}getInstanceMultiplexingLeader(){var e,t;if((null===(e=this.knownState)||void 0===e?void 0:e.operatingMode)===Ht.MULTIPLEXING)return null===(e=this.knownState)||void 0===e||null===(t=e.multiplexing)||void 0===t?void 0:t.leader}getWindow(){return this._window}}class Jt{constructor(e,t,i,r){d(this,"properties",void 0),d(this,"_messenger",void 0),d(this,"_knownInstances",new Map),d(this,"role",void 0),d(this,"_mode",void 0),d(this,"_metrics",void 0),d(this,"_logger",void 0),d(this,"_window",void 0);var s=E(globalThis)&&E(globalThis.crypto)&&C(globalThis.crypto.randomUUID)?globalThis.crypto.randomUUID():"".concat(1e6*Math.random()|0);this.properties=u({id:s,version:"1.0.40",href:null===(s=e.location)||void 0===s?void 0:s.href,domain:null===(s=e.location)||void 0===s?void 0:s.hostname},t),this.role=Wt.UNKNOWN,this._metrics=i,this._loadTime=performance.now(),this._logger=new zt(r,this),this._window=e,this._dispatcher=new We(this._logger),this._followerRole=new Tt(this._window,this.properties,this._dispatcher,this._logger,this._metrics)}updateConfig(e){u(this.properties,e)}register(e){try{this.updateConfig(e),this.init(),this._messenger.broadcastMessage(this._createHelloMessage(!1),at.TYPE)}catch(e){this._logger.error("Failed to register integration instance",e)}return this}init(){let r=this,s=r._window;r._mode=Ht.MULTIPLEXING_PASSIVE,r._messenger=new ht(r.properties.id,s,r._logger,r._metrics),r._messenger.onAnyMessage((e,t)=>{var i=Date.now()-e.timestamp|0;!function(e,t){return e.timer("instance.message.delivery.time",1<arguments.length&&void 0!==t?t:{})}(r._metrics,{messageType:e.type,sameWindow:s===t}).record(i),r._logger.debug("Message received",e),r._doFireEvent(Me.ID5_MESSAGE_RECEIVED,e)}).onMessage(at.TYPE,(e,t)=>{let i=u(new at,e.payload);void 0===i.isResponse&&(i.isResponse=void 0!==e.dst),r._handleHelloMessage(i,e,t)})}_handleHelloMessage(e,t,i){var r=e.isResponse;const s=new Bt(e.instance,e.instanceState,i);this._knownInstances.get(s.getId())?this._logger.debug("Instance already known",s.getId()):(this._knownInstances.set(s.getId(),s),r||this._messenger.sendResponseMessage(t,this._createHelloMessage(!0),at.TYPE),this._logger.debug("Instance joined",s.getId()),this._doFireEvent(Me.ID5_INSTANCE_JOINED,s.properties),this._onInstanceDiscovered(e,s))}unregister(){this._logger.info("Unregistering"),this._messenger&&this._messenger.unregister()}on(e,t){return this._dispatcher.on(e,t),this}_onInstanceDiscovered(){}_createHelloMessage(){var e=0<arguments.length&&void 0!==arguments[0]&&arguments[0],t={operatingMode:this._mode,knownInstances:Array.from(this._knownInstances.values()).map(e=>e.properties)};return new at(this.properties,e,t)}_doFireEvent(e){for(var t=arguments.length,i=new Array(1<t?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];this._dispatcher.emit(e,...i)}getId(){return this.properties.id}}class zt extends e{constructor(e,t){super(),d(this,"_instance",void 0),this._delegate=e||p,this._instance=t}_prefix(){return"Instance(id=".concat(this._instance.getId(),", role=").concat(this._instance.role,")")}debug(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];this._delegate.debug((new Date).toISOString(),this._prefix(),...t)}info(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];this._delegate.info((new Date).toISOString(),this._prefix(),...t)}warn(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];this._delegate.warn((new Date).toISOString(),this._prefix(),...t)}error(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];this._delegate.error((new Date).toISOString(),this._prefix(),...t)}}class Xt{constructor(e){d(this,"_knownValues",[]),d(this,"_counter",void 0),this._counter=e}add(e){e&&-1===this._knownValues.indexOf(e)&&(this._counter.inc(),this._knownValues.push(e))}}class Yt{constructor(e,t){d(this,"_instancesCounter",void 0),d(this,"_domainsCounter",void 0),d(this,"_windowsCounter",void 0),d(this,"_partnersCounter",void 0);var i=t.id;this._instancesCounter=function(e,t,i){return e.counter("instance.count",h({instanceId:t},2<arguments.length&&void 0!==i?i:{}))}(e,t.id),this._windowsCounter=new Xt(function(e,t,i){return e.counter("instance.windows.count",h({instanceId:t},2<arguments.length&&void 0!==i?i:{}))}(e,i)),this._partnersCounter=new Xt(function(e,t,i){return e.counter("instance.partners.count",h({instanceId:t},2<arguments.length&&void 0!==i?i:{}))}(e,i)),this._domainsCounter=new Xt(function(e,t,i){return e.counter("instance.domains.count",h({instanceId:t},2<arguments.length&&void 0!==i?i:{}))}(e,i))}addInstance(e){var t,i,r;this._instancesCounter.inc(),this._partnersCounter.add((null==e||null===(t=e.fetchIdData)||void 0===t?void 0:t.partnerId)|(null==e||null===(i=e.sourceConfiguration)||void 0===i||null===(r=i.options)||void 0===r?void 0:r.partnerId)),this._domainsCounter.add(null==e?void 0:e.domain),this._windowsCounter.add(null==e?void 0:e.href)}}class Kt{constructor(e){d(this,"_scheduleTime",void 0),d(this,"_closeTime",void 0),d(this,"_timeoutId",void 0),d(this,"_state",qt.AWAITING_SCHEDULE),d(this,"_delayMs",void 0),d(this,"_instance",void 0),this._instance=e}schedule(e){const t=this;t._delayMs=e,this._timeoutId=setTimeout(()=>{t._timeoutId&&(t._timeoutId=void 0,t._instance._doElection(),t._closeWithState(qt.COMPLETED))},t._delayMs),t._state=qt.SCHEDULED,t._scheduleTime=performance.now()}skip(){this._closeWithState(qt.SKIPPED)}cancel(){this._timeoutId&&(clearTimeout(this._timeoutId),this._timeoutId=void 0),this._closeWithState(qt.CANCELED)}_closeWithState(e){this._state=e,this._closeTime=performance.now()}}class Qt extends Jt{constructor(e,t,i,r,s,n,o){super(e,t,r,s),d(this,"_lastJoinedInstance",void 0),d(this,"_leader",void 0),d(this,"_remoteCallsToLeaderHandler",void 0),d(this,"_instanceCounters",void 0),d(this,"_election",void 0),d(this,"_storage",void 0),d(this,"_trueLinkAdapter",void 0),d(this,"_cachedIdProvider",void 0),this._leader=new Ft,this._remoteCallsToLeaderHandler=new Ft,this._instanceCounters=new Yt(r,this.properties),this._storage=i,this._trueLinkAdapter=n,this._cachedIdProvider=new Pt("self",new _t(o,n),this._logger,this._metrics),this._election=new Kt(this)}init(){super.init();var e,t=this;t._mode=!0===t.properties.singletonMode?Ht.SINGLETON:Ht.MULTIPLEXING,t._instanceCounters.addInstance(t.properties),function(i){const r=i._metrics;[100,200,500,1e3,2e3,3e3,5e3].forEach(t=>{setTimeout(()=>{var e=((null===(e=i._knownInstances)||void 0===e?void 0:e.size)||0)+1;r.summary("instance.partySize",{after:t,electionState:i._election._state}).record(e)},t)})}(t),t._messenger.onProxyMethodCall(new dt(t._logger).registerTarget(ct.LEADER,t._remoteCallsToLeaderHandler).registerTarget(ct.FOLLOWER,t._followerRole).registerTarget(ct.STORAGE,t._storage)),t._mode===Ht.SINGLETON?(t._election.skip(),t._onLeaderElected(t.properties)):t._mode===Ht.MULTIPLEXING&&(e=t.properties.electionDelayMSec||500,t._election.schedule(e))}_onInstanceDiscovered(e,t){e=e.isResponse;this._lastJoinedInstance=t,this._instanceCounters.addInstance(t.properties),function(e,t){return e.timer("instance.join.delay.time",1<arguments.length&&void 0!==t?t:{})}(this._metrics,{election:this._election._state}).record(performance.now()-this._loadTime|0),e?(e=t.getInstanceMultiplexingLeader(),this._mode===Ht.MULTIPLEXING&&this.role===Wt.UNKNOWN&&void 0!==e&&(this._logger.info("Joined late, elected leader is",e),this._election.cancel(),this._onLeaderElected(e))):this._mode===Ht.MULTIPLEXING&&this.role!==Wt.UNKNOWN&&this._handleLateJoiner(t)}_createHelloMessage(){var e;let t=super._createHelloMessage(0<arguments.length&&void 0!==arguments[0]&&arguments[0]);return t.instanceState.multiplexing=h(h({},t.instanceState.multiplexing),{},{role:this.role,electionState:null===(e=this._election)||void 0===e?void 0:e._state,leader:this._leader.getProperties()}),t}_handleLateJoiner(e){this._logger.info("Late joiner detected",e.properties);var t=rt(this._metrics,this.properties.id,{scope:"party"}).inc();!function(e,t){return e.timer("instance.lateJoin.delay",h({},1<arguments.length&&void 0!==t?t:{}))}(this._metrics,{election:this._election._state,isFirst:1===t}).record(performance.now()-this._election._closeTime),!e.isMultiplexingPartyAllowed()||this.role!==Wt.LEADER||!0===(null==(e=this._leader.addFollower(new Ut(e,this._messenger,this._logger)))?void 0:e.lateJoiner)&&rt(this._metrics,this.properties.id,{scope:"leader",unique:!0===(null==e?void 0:e.uniqueLateJoiner)}).inc()}_doFireEvent(e){for(var t=arguments.length,i=new Array(1<t?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];this._dispatcher.emit(e,...i)}_actAsLeader(){var e=this.properties;const t=this._logger;var i=this._metrics,r=new St(this._storage),s=new mt(r),n=new pt(e.storageExpirationDays);const o=new Vt(s,n,e.forceAllowLocalStorageGrant,t,i);s=new _t(new Gt(()=>o.localStorageGrant("client-store"),s,n,t),this._trueLinkAdapter),n=new bt(i,t,jt.createExtensions(i,t,s));const a=new xt(this._window,e,r,s,o,i,t,n);a.addFollower(this._followerRole),this._leader.assignLeader(a),this._remoteCallsToLeaderHandler.assignLeader(a),this._mode===Ht.MULTIPLEXING&&Array.from(this._knownInstances.values()).filter(e=>e.isMultiplexingPartyAllowed()).map(e=>a.addFollower(new Ut(e,this._messenger,t,e.isPassive()?Et.PASSIVE:Et.STANDARD))),a.start()}_followRemoteLeader(e){this._leader.assignLeader(new Lt(this._messenger,e)),this._remoteCallsToLeaderHandler.assignLeader(new $t(e.id,this._logger,this._metrics)),this._logger.info("Following remote leader ",e)}updateConsent(e){this._leader.updateConsent(e,this.properties.id)}updateFetchIdData(e){this._leader.updateFetchIdData(this.properties.id,e),u(this.properties.fetchIdData,e)}refreshUid(e){this._leader.refreshUid(e,this.properties.id)}_doElection(){var e=this._election;const t=this._knownInstances;let i=Array.from(t.values()).filter(e=>e.isMultiplexingPartyAllowed()&&e.isLeaderCapable()).map(e=>e.properties);i.push(this.properties),this._onLeaderElected(function(e){if(!e||0===e.length)return;e=e.sort((e,t)=>{let i=-(0|o(e.version,t.version));var r,s,n;return 0===i&&(i=e.source.localeCompare(t.source),0===i&&(i=-(0|o(e.sourceVersion,t.sourceVersion))),0===i&&(s=(null===(s=e.fetchIdData)||void 0===s||null===(r=s.refererInfo)||void 0===r?void 0:r.numIframes)||Number.MAX_SAFE_INTEGER,n=(null===(r=t.fetchIdData)||void 0===r||null===(n=r.refererInfo)||void 0===n?void 0:n.numIframes)||Number.MAX_SAFE_INTEGER,i=s-n),0===i&&(i=e.id.localeCompare(t.id))),i});return e[0]}(i));var r=this._lastJoinedInstance;r&&!function(e,t){return e.timer("instance.lastJoin.delay",h({},1<arguments.length&&void 0!==t?t:{}))}(this._metrics).record(Math.max(r._joinTime-e._scheduleTime,0))}_onLeaderElected(e){var t=this;t.role=e.id===t.properties.id?Wt.LEADER:Wt.FOLLOWER,t.role===Wt.LEADER?t._actAsLeader():t.role===Wt.FOLLOWER&&t._followRemoteLeader(e),t._logger.debug("Leader elected",e.id,"my role",t.role),t._doFireEvent(Me.ID5_LEADER_ELECTED,t.role,t._leader.getProperties())}lookupForCachedId(){return this._logger.info("Self lookup for cachedId triggered"),this._cachedIdProvider.provisionFromCache(this._followerRole)}}class $t extends Rt{constructor(e,t,i){super(),d(this,"_metrics",void 0),d(this,"_log",void 0),d(this,"_actualLeaderId",void 0),this._log=t,this._metrics=i,this._actualLeaderId=e}_handleMethod(e,t){try{this._log.warn("Received unexpected call method",e," call to leader from instance",t),function(e,t){e.counter("instance.message.unexpected.count",1<arguments.length&&void 0!==t?t:{})}(this._metrics,{target:"remoteLeader",methodName:e,callFromLeader:this._actualLeaderId===t})}catch(e){}}updateConsent(e,t){this._handleMethod("updateConsent",t)}refreshUid(e,t){this._handleMethod("refreshUid",t)}updateFetchIdData(e){this._handleMethod("updateFetchIdData",e)}}const Zt=new class{createInstance(e,t,i,r,s,n){return new Qt(e,{},r,i,t,s,n)}};class ei extends Ze{getGpId(){return!1===this._isExposed?void 0:this._gpId}getPublisherTrueLinkId(){return!1===this._isExposed?void 0:this._publisherTrueLinkId}getLinkType(){return!1===this._isExposed?0:this.getExt().linkType}getExt(){var e=!1===this._isExposed?{}:this._ext;return u({abTestingControlGroup:!this.exposeUserId()},e)}getUserIdAsEid(){var e,t;return null!==(t=null===(e=this._ids)||void 0===e||null===(t=e.id5id)||void 0===t?void 0:t.eid)&&void 0!==t?t:{source:ut.ID5_EIDS_SOURCE,uids:[{atype:1,id:this.getUserId(),ext:this.getExt()}]}}getUserIdsAsEids(){return void 0===this._ids?[this.getUserIdAsEid()]:Object.values(this._ids).map(e=>e.eid)}onRefresh(e,t){if(!W(e))throw new Error("onRefresh expects a function");this._refreshCallback&&this._refreshCallback.disableWatchdog();var i=!0===this._isRefreshing&&!1===this._isRefreshingWithFetch&&this._userIdAvailable;return this._refreshCallback=new He("onRefresh",this._log,this._metrics,e,t,i,()=>{this._isRefreshing=!1,this._isRefreshingWithFetch=!1},this),this}collectEvent(t,i){const r=e=>{e=new Request("https://id5-sync.com/event",{method:"POST",mode:"no-cors",body:JSON.stringify({partnerId:this.config.getOptions().partnerId,id5id:e,eventType:t,metadata:i})});return this._log.info("Sending event",e),fetch(e).catch(e=>this._log.error("Error while sending event to ID5 of type "+t,e))};return this._userIdAvailable?r(this._userId):this._userIdAvailablePromise.then(e=>r(e))}constructor(e,t,i,r,s,n,o,a,c){super(e,r,n,o,a,"api",{canDoCascade:!e.hasCreativeRestrictions()}),d(this,"clientStore",void 0),d(this,"consentManagement",void 0),d(this,"_consentDataProvider",void 0),d(this,"_trueLinkAdapter",void 0),this.clientStore=t,this.consentManagement=i,this._consentDataProvider=s,this._trueLinkAdapter=c}bootstrap(){super.bootstrap();const i=this._ref();this._multiplexingInstance.on(Ge.CASCADE_NEEDED,e=>{const t=i.deref();t&&t._doCascade(e)})}init(){const e=super.init(),t=this._submitRefreshConsent().then(e=>{e&&this.consentManagement.setConsentData(e)});return e.then(()=>{this._multiplexingInstance.lookupForCachedId().provisioned||(this._log.info("Couldn't find cached userId. Will try again when consent is resolved"),t.then(()=>{this._log.info("Consent resolved. Looking for cached id again"),this._multiplexingInstance.lookupForCachedId()}))}),Promise.allSettled([e,t])}refreshId(t,e){let i,r;this._log.info("ID refresh requested (force=".concat(t,") with additional options "),e);try{this._isRefreshing=!0,this._isRefreshingWithFetch=t,this.config.updOptions(e);var s=this.config.getOptions();const n=s.allowLocalStorageWithoutConsentApi||s.debugBypassConsent;i=this._gatherFetchIdData().then(e=>{this._multiplexingInstance.updateFetchIdData(e),this._multiplexingInstance.refreshUid({resetConsent:!0,forceAllowLocalStorageGrant:n,forceFetch:t}),r=this._submitRefreshConsent()}),function(e,t,i){return e.counter("refresh.call.count",h({target:t},2<arguments.length&&void 0!==i?i:{}))}(this._metrics,"public-api").inc()}catch(e){return this._log.error("Exception caught from refreshId()",e),Promise.reject(e)}return Promise.allSettled([i,r])}_doCascade(e){var t,i=this.config.getOptions();if(e.partnerId===i.partnerId&&0<=i.maxCascades&&!this.config.hasCreativeRestrictions()){var r=i.partnerUserId&&0<i.partnerUserId.length,s=new URL("/".concat(r?"s":"i","/").concat(i.partnerId,"/").concat(i.maxCascades,".gif"),"https://id5-sync.com");const n=s.searchParams;n.set("o","api"),n.set("id5id",e.userId),n.set("gdpr_consent",e.consentString),n.set("gdpr",e.gdprApplies),e.gppString&&(n.set("gpp",e.gppString),n.set("gpp_sid",e.gppSid)),r&&n.set("puid",i.partnerUserId),this._log.info("Opportunities to cascade available",s.href),t=s.href,"loading"!==document.readyState?Q(t):document.addEventListener("DOMContentLoaded",function(){Q(t)})}}_gatherFetchIdData(){var i=()=>super._gatherFetchIdData,r=this;return s(function*(){var e=r.config.getOptions(),t=yield et.gatherUaHints(e.disableUaHints,r._log);return h(h({},yield i().call(r)),{},{uaHints:t,pd:e.pd,partnerUserId:e.partnerUserId,allowedVendors:null===(t=e.consentData)||void 0===t?void 0:t.allowedVendors,consentSource:"iab"===e.cmpApi&&!0!==e.debugBypassConsent?ne.cmp:ne.partner,trueLink:r._trueLinkAdapter.getTrueLink(),att:e.att})})()}_submitRefreshConsent(){var n=this;return s(function*(){var e=n.config.getOptions();let t=Ee();var i=e.debugBypassConsent?"bypass":e.cmpApi;let r;try{r=yield n._consentDataProvider.refreshConsentData(e.debugBypassConsent,e.cmpApi,e.consentData);const s={};r.apiTypes.forEach(e=>s[e]=!0),t.record(Oe(n._metrics,i,h({success:!0},s))),n._multiplexingInstance.updateConsent(r)}catch(e){n._log.error("Couldn't get consent data",e),t.record(Oe(n._metrics,i,{success:!1,error:e.message}))}return r})()}localStorageGrant(){return this.clientStore.localStorageGrant()}getSignature(){return this._signature}}class ti{isBooted(){return D(window.id5Bootstrap)}getTrueLink(){return this.isBooted()?window.id5Bootstrap.getTrueLinkInfo():{booted:!1}}setPrivacy(e){this.isBooted()&&window.id5Bootstrap.setPrivacy&&window.id5Bootstrap.setPrivacy(e)}clearPrivacy(){this.setPrivacy(void 0)}}const ii=(ve=window,function(){try{var i=ri(),r=i.length-1,s=null!==i[r].location||0<r&&null!==i[r-1].referrer,n=function(e){let t=[];var i,r=null;let s=null,n=null,o=null,a=null,c;for(c=e.length-1;0<=c;c--){try{s=e[c].location}catch(e){}if(s)t.push(s),a=a||s;else if(0!==c){i=e[c-1];try{n=i.referrer,o=i.ancestor}catch(e){}n?(t.push(n),a=a||n):o?(t.push(o),a=a||o):t.push(r)}else t.push(r)}return{stack:t,detectedRefererUrl:a}}(i);let e,t;i[i.length-1].canonicalUrl&&(e=i[i.length-1].canonicalUrl);try{t=ve.top.document.referrer}catch(e){}return{topmostLocation:n.detectedRefererUrl,ref:t||null,reachedTop:s,numIframes:r,stack:n.stack,canonicalUrl:e}}catch(e){}});function ri(){let i=function(){let t=[],i;do{try{i=i?i.parent:ve;try{var r=i===ve.top;let e={referrer:i.document.referrer||null,location:i.location.href||null,isTop:r};r&&(e=u(e,{canonicalUrl:function(e){try{var t=e.querySelector("link[rel='canonical']");if(null!==t)return t.href}catch(e){}return null}(i.document)})),t.push(e)}catch(e){t.push({referrer:null,location:null,isTop:i===ve.top})}}catch(e){return t.push({referrer:null,location:null,isTop:!1}),t}}while(i!==ve.top);return t}();var r=function(){try{return ve.location.ancestorOrigins?ve.location.ancestorOrigins:void 0}catch(e){}}();if(r)for(let e=0,t=r.length;e<t;e++)i[e].ancestor=r[e];return i}class si{get version(){return this._version}set debug(e){e=e,N=!!e}get debug(){return V()}static assignApiStandard(e,r){e.init=function(e){return r.init(e)},e.refreshId=function(e,t,i){return r.refreshId(e,t,i)},e.ApiStandardLoaded=!0}static assignApiLite(e,t){e.initLite=function(e){return t.init(e)},e.ApiLiteLoaded=!0}constructor(){d(this,"loaded",!1),d(this,"_isUsingCdn",!1),d(this,"_referer",!1),d(this,"_version",qe),d(this,"versions",{}),d(this,"invocationId",0),d(this,"ApiStandardLoaded",!1),d(this,"init",void 0),d(this,"refreshId",void 0),d(this,"ApiLiteLoaded",!1),d(this,"initLite",void 0),this.loaded=!0,this._isUsingCdn=!!(document&&document.currentScript&&document.currentScript.src&&0===document.currentScript.src.indexOf("https://cdn.id5-sync.com")),this._referer=ii(),this.versions[qe]=!0}}const ni=new si;new class{constructor(e){d(this,"_id5Api",void 0),this._id5Api=e,si.assignApiStandard(e,this)}init(e){const t=this._id5Api;t.invocationId+=1;const i=new G("api",t.invocationId);try{i.info("ID5 API version ".concat(t._version,". Invoking init()"),e);const g=new ze(e,i);var r=g.getOptions(),s=this._configureDiagnostics(r,i);s&&(!function(e,t){return e.timer("instance.load.delay",1<arguments.length&&void 0!==t?t:{})}(s).recordNow(),function(e,t){return e.summary("invocation.count",1<arguments.length&&void 0!==t?t:{})}(s).record(t.invocationId,{fenced:"function"==typeof window.Fence&&window.fence instanceof window.Fence}));var n=new Ct(window,!g.hasCreativeRestrictions()),o=new mt(n,i),a=g.isForceAllowLocalStorageGrant(),c=new pt(r.storageExpirationDays);const p=new Vt(o,c,a,i,s);const _=new Gt(()=>p.localStorageGrant("instance-client-store"),o,c,i);var l=new ti,d=Zt.createInstance(window,i,s,n,l,_),h=new Ve(s,i),u=new Ye(t._referer,t._version,t._isUsingCdn);const v=new ei(g,_,p,s,h,i,d,u,l);return v.bootstrap(),v.init(),_.scheduleGC(s),v}catch(e){i.error("Exception caught during init()",e)}}refreshId(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1],i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};if(!z(t))throw new Error("Invalid usage of refreshId(): second parameter must be a boolean");return e.refreshId(t,i),e}_configureDiagnostics(e,t){try{var i=this._id5Api,r=e.partnerId;const a=new Te("api",qe);a.addCommonTags(h(h({},Ae(r)),{},{tml:i._referer.topmostLocation,provider:e.provider||"default"}));var s=e.diagnostics;if(null==s||!s.publishingDisabled){const c=new pe(a,(n=s.publishingSampleRatio,Math.random()<n&&ue?(e,t)=>new ge(o,{sampling:n}).publish(e,t):e=>e));null!=s&&s.publishAfterLoadInMsec&&0<s.publishAfterLoadInMsec&&c.schedulePublishAfterMsec(s.publishAfterLoadInMsec),null!=s&&s.publishBeforeWindowUnload&&c.schedulePublishBeforeUnload()}return a}catch(e){return void t.error("Failed to configure diagnostics",e)}var n,o}}(ni);const oi=window.googletag=window.googletag||{};oi.encryptedSignalProviders=oi.encryptedSignalProviders||[],oi.encryptedSignalProviders.push({id:"id5-sync.com",collectorFunction:()=>{const s="https://id5-sync.com/api/esp/increment?counter=";return new Promise((t,i)=>{if(!J(window.ID5EspConfig)){var r=()=>i(new Error("No ID5 config"));return U("esp","Expected window.ID5EspConfig to be an Object with the necessary configuration! Cannot invoke ID5 fetch."),void K(s+"no-config",{success:r,error:r})}window.ID5EspConfig.provider=window.ID5EspConfig.provider||"g-esp";try{ni.init(window.ID5EspConfig).onAvailable(e=>{t(e.getUserId())})}catch(e){r=()=>i(e);U("esp","Exception while initializing ID5 within googletag ESP! Cannot invoke ID5 fetch."),K(s+"exception",{success:r,error:r})}})}})}();