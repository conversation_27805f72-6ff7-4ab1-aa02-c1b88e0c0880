"use strict";(self.__VM_WPK_C_LR__m=self.__VM_WPK_C_LR__m||[]).push([[56466],{56342:(e,r,n)=>{n.d(r,{Cf:()=>a,S3:()=>i,Tb:()=>s,WR:()=>u,e4:()=>l,pS:()=>v,qN:()=>f,yB:()=>g,zt:()=>t});var o,t=["request","imp","bidResponse","response"],c=["default","pbs"],i=t[0],s=t[1],u=t[2],a=t[3],f=c[0],l=c[1],d=new Set(t),y=(o={},{registerOrtbProcessor:function(e){var r=e.type,n=e.name,c=e.fn,i=e.priority,s=void 0===i?0:i,u=e.dialects,a=void 0===u?[f]:u;if(!d.has(r))throw new Error("ORTB processor type must be one of: ".concat(t.join(", ")));a.forEach((function(e){o.hasOwnProperty(e)||(o[e]={}),o[e].hasOwnProperty(r)||(o[e][r]={}),o[e][r][n]={priority:s,fn:c}}))},getProcessors:function(e){return o[e]||{}}}),v=y.registerOrtbProcessor,g=y.getProcessors},58922:(e,r,n)=>{n.r(r),n.d(r,{addBidResponseHook:()=>P,currencyRates:()=>N,currencySupportEnabled:()=>w,requestBidsHook:()=>L,resetCurrency:()=>W,responseReady:()=>E,setConfig:()=>Y,setOrtbCurrency:()=>H});var o,t,c=n(80296),i=n(82284),s=n(79675),u=n(90869),a=n(63172),f=n(62539),l=n(366),d=n(6898),y=n(35563),v=n(26461),g=n(56342),p=n(8316),h=n(63761),C=n(45018),b=n(65394),R="https://cdn.jsdelivr.net/gh/prebid/currency-file@1/latest.json?date=$$TODAY$$",m=4,O=[],S={},T=!1,_=!0,I="USD",w=!1,N={},A={},E=(0,v.v6)(),D=(0,b.L)(),U=0;function Y(e){if(o=R,null!==e.rates&&"object"===(0,i.A)(e.rates)&&(N.conversions=e.rates,T=!0,_=!1),T||null===e.defaultRates||"object"!==(0,i.A)(e.defaultRates)||(t=e.defaultRates,N.conversions=t,T=!0),"string"==typeof e.adServerCurrency){U=e.auctionDelay,(0,u.logInfo)("enabling currency support",arguments),I=e.adServerCurrency,e.conversionRateFile&&((0,u.logInfo)("currency using override conversionRateFile:",e.conversionRateFile),o=e.conversionRateFile);var r=o.indexOf("$$TODAY$$");if(-1!==r){var n=new Date,c="".concat(n.getMonth()+1),a="".concat(n.getDate());c.length<2&&(c="0".concat(c)),a.length<2&&(a="0".concat(a));var l="".concat(n.getFullYear()).concat(c).concat(a);o="".concat(o.substring(0,r)).concat(l).concat(o.substring(r+9,o.length))}S={},w||(w=!0,(0,s.m)().convertCurrency=function(e,r,n){return parseFloat(e)*M(r,n)},(0,y.Yn)("addBidResponse").before(P,100),(0,y.Yn)("responsesReady").before(q),C.w.before(x),(0,y.Yn)("requestBids").before(L,50),(0,h.on)(f.qY.AUCTION_TIMEOUT,$),(0,h.on)(f.qY.AUCTION_INIT,F),F())}else U=0,(0,u.logInfo)("disabling currency support"),W();"object"===(0,i.A)(e.bidderCurrencyDefault)&&(A=e.bidderCurrencyDefault)}function k(e){t?((0,u.logWarn)(e),(0,u.logWarn)("Currency failed loading rates, falling back to currency.defaultRates")):(0,u.logError)(e)}function F(){_?(_=!1,T=!1,(0,l.RD)(o,{success:function(e){try{N=JSON.parse(e),(0,u.logInfo)("currencyRates set to "+JSON.stringify(N)),S={},T=!0,B(),D.resume()}catch(r){k("Failed to parse currencyRates response: "+e)}},error:function(){k.apply(void 0,arguments),T=!0,B(),D.resume(),_=!0}})):B()}function W(){w&&((0,y.Yn)("addBidResponse").getHooks({hook:P}).remove(),(0,y.Yn)("responsesReady").getHooks({hook:q}).remove(),C.w.getHooks({hook:x}).remove(),(0,y.Yn)("requestBids").getHooks({hook:L}).remove(),(0,h.off)(f.qY.AUCTION_TIMEOUT,$),(0,h.off)(f.qY.AUCTION_INIT,F),delete(0,s.m)().convertCurrency,I="USD",S={},w=!1,T=!1,_=!0,N={},A={},E=(0,v.v6)())}function q(e,r){e(r.then((function(){return E.promise})))}d.$W.getConfig("currency",(function(e){return Y(e.currency)}));var P=(0,p.NL)("currency",(function(e,r,n,o){if(!n)return e.call(this,r,n,o);var t=n.bidderCode||n.bidder;if(A[t]){var c=A[t];n.currency&&c!==n.currency?(0,u.logWarn)("Currency default '".concat(t,": ").concat(c,"' ignored. adapter specified '").concat(n.currency,"'")):n.currency=c}if(n.currency||((0,u.logWarn)('Currency not specified on bid.  Defaulted to "USD"'),n.currency="USD"),n.getCpmInNewCurrency=function(e){return(parseFloat(this.cpm)*M(this.currency,e)).toFixed(3)},n.currency===I)return e.call(this,r,n,o);O.push([e,this,r,n,o]),w&&!T||B()}));function $(e){var r=e.auctionId;O=O.filter((function(e){var n=(0,c.A)(e,5),o=(n[0],n[1],n[2],n[3]),t=n[4];if(o.auctionId!==r)return!0;t(f.Tf.CANNOT_CONVERT_CURRENCY)}))}function B(){for(;O.length>0;){var e=O.shift(),r=(0,c.A)(e,5),n=r[0],o=r[1],t=r[2],i=r[3],s=r[4];if(void 0!==i&&"currency"in i&&"cpm"in i){var a=i.currency;try{var l=M(a);1!==l&&(i.cpm=(parseFloat(i.cpm)*l).toFixed(4),i.currency=I)}catch(e){(0,u.logWarn)("getCurrencyConversion threw error: ",e),s(f.Tf.CANNOT_CONVERT_CURRENCY);continue}}n.call(o,t,i,s)}E.resolve()}function M(e){var r,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:I,o=null,t="".concat(e,"->").concat(n);if(t in S)o=S[t],(0,u.logMessage)("Using conversionCache value "+o+" for "+t);else if(!1===w){if("USD"!==e)throw new Error("Prebid currency support has not been enabled and fromCurrency is not USD");o=1}else if(e===n)o=1;else if(e in N.conversions){if(!(n in(r=N.conversions[e])))throw new Error("Specified adServerCurrency in config '"+n+"' not found in the currency rates file");o=r[n],(0,u.logInfo)("getCurrencyConversion using direct "+e+" to "+n+" conversionRate "+o)}else if(n in N.conversions){if(!(e in(r=N.conversions[n])))throw new Error("Specified fromCurrency '"+e+"' not found in the currency rates file");o=j(1/r[e],m),(0,u.logInfo)("getCurrencyConversion using reciprocal "+e+" to "+n+" conversionRate "+o)}else{var c=Object.keys(N.conversions)[0];if(!(e in N.conversions[c]))throw new Error("Specified fromCurrency '"+e+"' not found in the currency rates file");var i=1/N.conversions[c][e];if(!(n in N.conversions[c]))throw new Error("Specified adServerCurrency in config '"+n+"' not found in the currency rates file");o=j(i*N.conversions[c][n],m),(0,u.logInfo)("getCurrencyConversion using intermediate "+e+" thru "+c+" to "+n+" conversionRate "+o)}return t in S||((0,u.logMessage)("Adding conversionCache value "+o+" for "+t),S[t]=o),o}function j(e,r){for(var n=1,o=0;o<r;o++)n+="0";return Math.round(e*n)/n}function H(e,r,n){w&&(e.cur=e.cur||[n.currency||I])}function x(e,r){return e(r.then((function(e){return(0,a.J)(e,"ext.prebid.adServerCurrency",I),e})))}(0,g.pS)({type:g.S3,name:"currency",fn:H});var L=(0,p.Ak)("currency",(function(e,r){var n,o=(n=this,function(){return e.call(n,r)});!T&&U>0?D.submit(U,o,(function(){(0,u.logWarn)("".concat("currency",": Fetch attempt did not return in time for auction ").concat(r.auctionId)),o()})):o()}));(0,s.E)("currency")},65394:(e,r,n)=>{n.d(r,{L:()=>t});var o=n(80296);function t(){var e=[];return{submit:function(r,n,o){var t=[n,setTimeout((function(){e.splice(e.indexOf(t),1),o()}),r)];e.push(t)},resume:function(){for(;e.length;){var r=e.shift(),n=(0,o.A)(r,2),t=n[0],c=n[1];clearTimeout(c),t()}}}}}}]);