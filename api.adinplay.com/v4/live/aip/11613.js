"use strict";(self.__VM_WPK_C_LR__m=self.__VM_WPK_C_LR__m||[]).push([[11613],{20439:(n,i,e)=>{e.r(i),e.d(i,{init:()=>O,isSchainObjectValid:()=>S,isValidSchainConfig:()=>m,makeBidRequestsHook:()=>v,setOrtbSourceExtSchain:()=>P});var o=e(79675),t=e(6898),r=e(83038),s=e(90869),c=e(70433),a=e(63172),f=e(56342),d="Invalid schain object found: ",h=" should be a string",u=" should be an Integer",g=" should be an object",l=" should be an Array",p={STRICT:"strict",RELAXED:"relaxed",OFF:"off"},b=[];function S(n,i){var e="Detected something wrong within an schain config:",o="";function t(n){o+="\n"+n}function r(){!0===i?(0,s.logError)(e,n,o):(0,s.logWarn)(e,n,o)}if(!(0,s.isPlainObject)(n)&&(t("schain.config"+g),r(),i))return!1;if((0,s.isNumber)(n.complete)&&(0,s.isInteger)(n.complete)||t("schain.config.complete"+u),(0,s.isStr)(n.ver)||t("schain.config.ver"+h),n.hasOwnProperty("ext")&&((0,s.isPlainObject)(n.ext)||t("schain.config.ext"+g)),(0,s.isArray)(n.nodes))n.nodes.forEach((function(n,i){(0,s.isStr)(n.asi)||t("schain.config.nodes[".concat(i,"].asi")+h),(0,s.isStr)(n.sid)||t("schain.config.nodes[".concat(i,"].sid")+h),(0,s.isNumber)(n.hp)&&(0,s.isInteger)(n.hp)||t("schain.config.nodes[".concat(i,"].hp")+u),n.hasOwnProperty("rid")&&((0,s.isStr)(n.rid)||t("schain.config.nodes[".concat(i,"].rid")+h)),n.hasOwnProperty("name")&&((0,s.isStr)(n.name)||t("schain.config.nodes[".concat(i,"].name")+h)),n.hasOwnProperty("domain")&&((0,s.isStr)(n.domain)||t("schain.config.nodes[".concat(i,"].domain")+h)),n.hasOwnProperty("ext")&&((0,s.isPlainObject)(n.ext)||t("schain.config.nodes[".concat(i,"].ext")+g))}));else if(t("schain.config.nodes"+l),r(),i)return!1;return!(o.length>0&&(r(),i))}function m(n){return!(void 0===n||!(0,s.isPlainObject)(n)&&((0,s.logError)(d+"the following schain config will not be used as schain is not an object.",n),1))}function v(n,i){var e=t.$W.getConfig("schain"),o=t.$W.getBidderConfig();i.forEach((function(n){var i=n.bidderCode,t=function(n){return o[n]&&o[n].schain||e}(i);n.bids.forEach((function(n){var e=function(n,i){var e=p.STRICT;if(m(n)){if((0,s.isStr)(n.validation)&&-1!=b.indexOf(n.validation)&&(e=n.validation),e===p.OFF)return n.config;if(S(n.config,!(e!==p.STRICT)))return n.config;(0,s.logError)(d+"due to the 'strict' validation setting, this schain config will not be passed to bidder '".concat(i,"'.  See above error for details."))}return null}(t,i);e&&(n.schain=(0,s.deepClone)(e))}))})),n(i)}function O(){r.Ay.makeBidRequests.after(v)}function P(n,i,e){if(!(0,c.A)(n,"source.ext.schain")){var o=(0,c.A)(e,"bidRequests.0.schain");o&&(0,a.J)(n,"source.ext.schain",o)}}(0,s._each)(p,(function(n){return b.push(n)})),O(),(0,f.pS)({type:f.S3,name:"sourceExtSchain",fn:P}),(0,o.E)("schain")},56342:(n,i,e)=>{e.d(i,{Cf:()=>f,S3:()=>s,Tb:()=>c,WR:()=>a,e4:()=>h,pS:()=>l,qN:()=>d,yB:()=>p,zt:()=>t});var o,t=["request","imp","bidResponse","response"],r=["default","pbs"],s=t[0],c=t[1],a=t[2],f=t[3],d=r[0],h=r[1],u=new Set(t),g=(o={},{registerOrtbProcessor:function(n){var i=n.type,e=n.name,r=n.fn,s=n.priority,c=void 0===s?0:s,a=n.dialects,f=void 0===a?[d]:a;if(!u.has(i))throw new Error("ORTB processor type must be one of: ".concat(t.join(", ")));f.forEach((function(n){o.hasOwnProperty(n)||(o[n]={}),o[n].hasOwnProperty(i)||(o[n][i]={}),o[n][i][e]={priority:c,fn:r}}))},getProcessors:function(n){return o[n]||{}}}),l=g.registerOrtbProcessor,p=g.getProcessors}}]);