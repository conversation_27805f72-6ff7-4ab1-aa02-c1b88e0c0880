"use strict";(self.__VM_WPK_C_LR__m=self.__VM_WPK_C_LR__m||[]).push([[73469,77511],{48168:(e,n,r)=>{r.r(n),r.d(n,{PBJS_USER_ID_OPTOUT_NAME:()=>M,addIdData:()=>oe,addUserIdsHook:()=>le,attachIdSystem:()=>Ce,auctionDelay:()=>T,coreStorage:()=>q,deleteStoredValue:()=>z,dep:()=>G,enrichEids:()=>te,getConsentHash:()=>Ie,getValidSubmoduleConfigs:()=>De,init:()=>Te,requestDataDeletion:()=>_e,resetUserIds:()=>Ne,setStoredValue:()=>Y,setSubmoduleRegistry:()=>K,startAuctionHook:()=>ce,syncDelay:()=>U});var t=r(45458),o=r(82284),i=r(80296),a=r(79675),u=r(93787),c=r(6898),l=r(63761),s=r(83038),d=r(62539),f=r(35563),g=r(71877),m=r(39012),v=r(90869),b=r(63172),h=r(84738),y=r(26461),p=r(8316),I=r(21455),S=r(38830),E=r(60807),w=r(60249),D=r(88441),A=r(14051),O=r(59424),k=r(92824);function j(e,n){(null==n||n>e.length)&&(n=e.length);for(var r=0,t=Array(n);r<n;r++)t[r]=e[r];return t}var _,C,U,T,N,x,P,F="User ID",L=m.X0,W=m.qk,M="_pbjs_id_optout",q=(0,m.CK)("userId"),G={isAllowed:w.io},R=[],H=[],J={},V=[],B=function(){return null==P&&(P=(0,p.K7)()),P};function $(e){return B().fork().renameWith((function(n){return["userId.mod.".concat(n),"userId.mods.".concat(e,".").concat(n)]}))}function K(e){V=e,ke(e)}function Q(e,n){n=n||e.storageMgr;var r="function"==typeof e.submodule.domainOverride?e.submodule.domainOverride():null,t=e.config.storage.name;return function(e,o,i){n.setCookie(t+(e||""),o,i,"Lax",r)}}function Y(e,n){var r=e.config.storage;try{var t=new Date(Date.now()+864e5*r.expires).toUTCString(),o=(0,v.isPlainObject)(n)?JSON.stringify(n):n;e.enabledStorageTypes.forEach((function(n){switch(n){case L:!function(e,n,r){var t=e.config.storage,o=Q(e);o(null,n,r),o("_cst",Ie(),r),"number"==typeof t.refreshInSeconds&&o("_last",(new Date).toUTCString(),r)}(e,o,t);break;case W:!function(e,n,r){var t=e.config.storage,o=e.storageMgr;o.setDataInLocalStorage("".concat(t.name,"_exp"),r),o.setDataInLocalStorage("".concat(t.name,"_cst"),Ie()),o.setDataInLocalStorage(t.name,encodeURIComponent(n)),"number"==typeof t.refreshInSeconds&&o.setDataInLocalStorage("".concat(t.name,"_last"),(new Date).toUTCString())}(e,o,t)}}))}catch(e){(0,v.logError)(e)}}function z(e){Oe(e),e.enabledStorageTypes.forEach((function(n){switch(n){case L:!function(e){var n=Q(e,q),r=new Date(Date.now()-864e5).toUTCString();["","_last","_cst"].forEach((function(e){try{n(e,"",r)}catch(e){(0,v.logError)(e)}}))}(e);break;case W:!function(e){["","_last","_exp","_cst"].forEach((function(n){try{q.removeDataFromLocalStorage(e.config.storage.name+n)}catch(e){(0,v.logError)(e)}}))}(e)}}))}function X(e){var n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,t=e.config.storage,o=r?"".concat(t.name,"_").concat(r):t.name;try{e.enabledStorageTypes.find((function(r){switch(r){case L:n=function(e,n){return e.storageMgr.getCookie(n)}(e,o);break;case W:n=function(e,n){var r=e.storageMgr,t=e.config.storage,o=r.getDataFromLocalStorage("".concat(t.name,"_exp"));return""===o?r.getDataFromLocalStorage(n):o&&new Date(o).getTime()-Date.now()>0?decodeURIComponent(r.getDataFromLocalStorage(n)):void 0}(e,o)}return!!n})),"string"==typeof n&&"{"===n.trim().charAt(0)&&(n=JSON.parse(n))}catch(e){(0,v.logError)(e)}return n}function Z(e,n,r){n=B().fork().startTiming("userId.callbacks.total").stopBefore(n);var t=(0,v.delayExecution)((function(){clearTimeout(C),n()}),e.length);e.forEach((function(e){var n=$(e.submodule.name).startTiming("callback").stopBefore(t);try{e.callback((function(t){t?(e.config.storage&&Y(e,t),e.idObj=e.submodule.decode(t,e.config),r.refresh(),Se(r)):(0,v.logInfo)("".concat(F,": ").concat(e.submodule.name," - request id responded with an empty value")),n()}),X.bind(null,e))}catch(r){(0,v.logError)("Error in userID module '".concat(e.submodule.name,"':"),r),n()}e.callback=void 0}))}function ee(e){return Object.fromEntries(Object.entries(e).map((function(e){var n,r=(0,i.A)(e,2),t=r[0];return[t,null===(n=(0,r[1])())||void 0===n||null===(n=n.idObj)||void 0===n?void 0:n[t]]})).filter((function(e){var n=(0,i.A)(e,2);return n[0],null!=n[1]})))}function ne(e,n,r){var t={};return e.forEach((function(e){var o=r(e),a=function(e){var n;if(e.primaryIds)return e.primaryIds;var r=Object.keys(null!==(n=e.eids)&&void 0!==n?n:{});if(r.length>1)throw new Error("ID submodule ".concat(e.name," can provide multiple IDs, but does not specify 'primaryIds'"));return r}(o);n(e).forEach((function(n){var r,u,c,l=t[n]=null!==(r=t[n])&&void 0!==r?r:[],s=null!==(u=null===(c=J[n])||void 0===c?void 0:c.indexOf(o.name))&&void 0!==u?u:a.includes(n)?0:-1,d=l.findIndex((function(e){return(0,i.A)(e,1)[0]<s}));l.splice(-1===d?l.length:d,0,[s,e])}))})),Object.fromEntries(Object.entries(t).map((function(e){var n=(0,i.A)(e,2);return[n[0],n[1].map((function(e){var n=(0,i.A)(e,2);return n[0],n[1]}))]})))}function re(){var e={submodules:[],global:{},bidder:{},combined:{},refresh:function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],r=new Set(n.map((function(e){return e.submodule})));e.submodules=e.submodules.filter((function(e){return!r.has(e.submodule)})).concat(n),function(){var n=ne(e.submodules,(function(e){var n;return Object.keys(null!==(n=e.idObj)&&void 0!==n?n:{})}),(function(e){return e.submodule})),r={},t={};function o(e,n,r){return function(){var t,o=function(e,n){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,n){if(e){if("string"==typeof e)return j(e,n);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?j(e,n):void 0}}(e))||n&&e&&"number"==typeof e.length){r&&(e=r);var t=0,o=function(){};return{s:o,n:function(){return t>=e.length?{done:!0}:{done:!1,value:e[t++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){u=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw i}}}}(r);try{for(o.s();!(t=o.n()).done;){var i,a,u=t.value,c=u.allowed,l=u.bidders,s=u.module;if(G.isAllowed(D.yl,(0,A.s)(E.fW,null==s||null===(i=s.config)||void 0===i?void 0:i.name,{init:!1}))&&null!=(null===(a=s.idObj)||void 0===a?void 0:a[e])){if(c)return s;if(n){var d=function(e){return e.map((function(e){return e.module.submodule.name})).join(", ")};return(0,v.logWarn)("userID modules ".concat(d(r)," provide the same ID ('").concat(e,"'); ").concat(s.submodule.name," is the preferred source, but it's configured only for some bidders, unlike ").concat(d(r.filter((function(e){return null==e.bidders}))),'. Other bidders will not see the "').concat(e,'" ID.')),null}if(null==l)return null}}}catch(e){o.e(e)}finally{o.f()}return null}}Object.entries(n).forEach((function(e){var n=(0,i.A)(e,2),a=n[0],u=n[1],c=!0,l=new Set;u=u.map((function(e){var n=null;return Array.isArray(e.config.bidders)&&e.config.bidders.length>0?(n=e.config.bidders).forEach((function(e){return l.add(e)})):c=!1,{module:e,bidders:n}})),c||(r[a]=o(a,!0,u.map((function(e){var n=e.bidders;return{allowed:null==n,bidders:n,module:e.module}})))),l.forEach((function(e){var n;t[e]=null!==(n=t[e])&&void 0!==n?n:{},t[e][a]=o(a,!1,u.map((function(n){var r=n.bidders,t=n.module;return{allowed:null==r?void 0:r.includes(e),bidders:r,module:t}})))}))}));var a=Object.values(t).concat([r]).reduce((function(e,n){return Object.assign(e,n)}),{});Object.assign(e,{global:r,bidder:t,combined:a})}()}};return e}function te(e){var n,r,t=e.global,o=e.bidder,a=_,u=a.global,c=a.bidder,l=(0,g.getEids)(u);return l.length>0&&(0,b.J)(t,"user.ext.eids",(null!==(n=null===(r=t.user)||void 0===r||null===(r=r.ext)||void 0===r?void 0:r.eids)&&void 0!==n?n:[]).concat(l)),Object.entries(c).forEach((function(e){var n,r,t=(0,i.A)(e,2),a=t[0],u=t[1],c=(0,g.getEids)(u);c.length>0&&(0,b.J)(o,"".concat(a,".user.ext.eids"),(null!==(n=null===(r=o[a])||void 0===r||null===(r=r.user)||void 0===r||null===(r=r.ext)||void 0===r?void 0:r.eids)&&void 0!==n?n:[]).concat(c))})),e}function oe(e){var n,r=e.adUnits,t=e.ortb2Fragments;if(te(t=null!=t?t:{global:{},bidder:{}}),![r].some((function(e){return!Array.isArray(e)||!e.length}))){var o=ee(_.global),i=(null===(n=t.global.user)||void 0===n||null===(n=n.ext)||void 0===n?void 0:n.eids)||[];r.forEach((function(e){e.bids&&(0,v.isArray)(e.bids)&&e.bids.forEach((function(e){var n,r,a=Object.assign({},o,ee(null!==(n=_.bidder[e.bidder])&&void 0!==n?n:{})),u=i.concat((null===(r=t.bidder)||void 0===r||null===(r=r[e.bidder])||void 0===r||null===(r=r.user)||void 0===r||null===(r=r.ext)||void 0===r?void 0:r.eids)||[]);Object.keys(a).length>0&&(e.userId=a),u.length>0&&(e.userIdAsEids=u)}))}))}}var ie,ae={};function ue(){var e,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:fe()||[],r=N&&n.find((function(e){return e.source===N}));if(r&&"string"==typeof(null==r||null===(e=r.uids)||void 0===e||null===(e=e[0])||void 0===e?void 0:e.id)){var t=r.uids[0].id.replace(/[\W_]/g,"");if(t.length>=32&&t.length<=150)return t;(0,v.logWarn)("User ID - Googletag Publisher Provided ID for ".concat(N," is not between 32 and 150 characters - ").concat(t))}}var ce=(0,p.Ak)("userId",(function(e,n){var r=this,t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=t.mkDelay,i=void 0===o?y.cb:o,a=t.getIds,u=void 0===a?pe:a;y.U9.race([u().catch((function(){return null})),i(T)]).then((function(){oe(n),B().join((0,p.BO)(n.metrics),{propagate:!1,includeGroups:!0}),e.call(r,n)}))})),le=(0,p.Ak)("userId",(function(e,n){oe(n),e.call(this,n)}));function se(){return!!k.gH.getHooks({hook:ce}).length}function de(){return ee(_.combined)}function fe(){return(0,g.getEids)(_.combined)}function ge(e){return fe().filter((function(n){return n.source===e}))[0]}function me(e,n,r){return he().then((function(){var t={};if((0,v.isFn)(r)){(0,v.logInfo)("".concat(F," - Getting encrypted signal from custom function : ").concat(r.name," & source : ").concat(e," "));var o=r(e);t[e]=o?ve(o):null}else{var i=ge(e);(0,v.logInfo)("".concat(F," - Getting encrypted signal for eids :").concat(JSON.stringify(i))),(0,v.isEmpty)(i)||(t[i.source]=!0===n?ve(i):i.uids[0].id)}return(0,v.logInfo)("".concat(F," - Fetching encrypted eids: ").concat(t[e])),t[e]}))}function ve(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r="";return 1===n&&(r="object"===(0,o.A)(e)?window.btoa(JSON.stringify(e)):window.btoa(e)),"".concat(n,"||").concat(r)}function be(){if((0,v.isGptPubadsDefined)()){window.googletag.secureSignalProviders=window.googletag.secureSignalProviders||[];var e=c.$W.getConfig("userSync.encryptedSignalSources");if(e){var n=e.registerDelay||0;setTimeout((function(){e.sources&&e.sources.forEach((function(e){var n=e.source,r=e.encrypt,t=e.customFunc;n.forEach((function(e){window.googletag.secureSignalProviders.push({id:e,collectorFunction:function(){return me(e,r,t)}})}))}))}),n)}else(0,v.logWarn)("".concat(F," - ESP : encryptedSignalSources config not defined under userSync Object"))}}function he(e){return ie(e).then((function(){return de()}),(function(e){return e===ae?Promise.resolve().then(pe):((0,v.logError)("Error initializing userId",e),y.U9.reject(e))}))}function ye(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).submoduleNames,n=arguments.length>1?arguments[1]:void 0;return he({refresh:!0,submoduleNames:e}).then((function(e){return n&&(0,v.isFn)(n)&&n(),e}))}function pe(){return he()}function Ie(){for(var e=Number(S.SL.hash),n=[];e>0;)n.push(String.fromCharCode(255&e)),e>>>=8;return btoa(n.join())}function Se(e){var n=(0,g.getEids)(e.combined);if(n.length&&N){var r=ue(n);r&&((0,v.isGptPubadsDefined)()?window.googletag.pubads().setPublisherProvidedId(r):(window.googletag=window.googletag||{},window.googletag.cmd=window.googletag.cmd||[],window.googletag.cmd.push((function(){window.googletag.pubads().setPublisherProvidedId(r)}))))}}function Ee(e,n){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return B().fork().measureTime("userId.init.modules",(function(){if(!n.length)return[];if(n.forEach((function(e){return Oe(e)})),!(n=n.filter((function(e){return(!e.config.storage||!!e.enabledStorageTypes.length)&&G.isAllowed(D.yl,(0,A.s)(E.fW,e.config.name))}))).length)return(0,v.logWarn)("".concat(F," - no ID module configured")),[];var t=n.reduce((function(e,n){return $(n.submodule.name).measureTime("init",(function(){try{(function(e,n){var r=S.SL.getConsentData();if(e.config.storage){var t,o=X(e),i=!1;if("number"==typeof e.config.storage.refreshInSeconds){var a=new Date(X(e,"last"));i=a&&Date.now()-a.getTime()>1e3*e.config.storage.refreshInSeconds}if(!o||i||n||function(e){var n=X(e,"cst");return!n||n!==Ie()}(e)){var u=Object.assign({enabledStorageTypes:e.enabledStorageTypes},e.config);t=e.submodule.getId(u,r,o)}else"function"==typeof e.submodule.extendId&&(t=e.submodule.extendId(e.config,r,o));(0,v.isPlainObject)(t)&&(t.id&&(Y(e,t.id),o=t.id),"function"==typeof t.callback&&(e.callback=t.callback)),o&&(e.idObj=e.submodule.decode(o,e.config))}else if(e.config.value)e.idObj=e.config.value;else{var c=e.submodule.getId(e.config,r);(0,v.isPlainObject)(c)&&("function"==typeof c.callback&&(e.callback=c.callback),c.id&&(e.idObj=e.submodule.decode(c.id,e.config)))}})(n,r),e.push(n)}catch(e){(0,v.logError)("Error in userID module '".concat(n.submodule.name,"':"),e)}return e}))}),[]);return e.refresh(t),Se(e),t}))}function we(e){var n;return(null==e||null===(n=e.storage)||void 0===n||null===(n=n.type)||void 0===n?void 0:n.trim().split(/\s*&\s*/))||[]}function De(e){function n(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),t=1;t<n;t++)r[t-1]=arguments[t];v.logWarn.apply(void 0,["Invalid userSync.userId config: ".concat(e)].concat(r))}return Array.isArray(e)?e.filter((function(e){if(null==e||!e.name)return n('must specify "name"',e);if(e.storage){if(!e.storage.name||!e.storage.type)return n('must specify "storage.name" and "storage.type"',e);if(!function(e){return we(e).every((function(e){return Ae.has(e)}))}(e))return n('invalid "storage.type"',e);["expires","refreshInSeconds"].forEach((function(r){var t=e.storage[r];null!=t&&"number"!=typeof t&&(t=Number(t),isNaN(t)?(n("storage.".concat(r," must be a number and will be ignored"),e),delete e.storage[r]):e.storage[r]=t)}))}return!0})):(null!=e&&n("must be an array",e),[])}var Ae=new Set([W,L]);function Oe(e){if(!e.enabledStorageTypes){var n=we(e.config);e.enabledStorageTypes=n.filter((function(n){switch(n){case W:return function(e){return!(!e.storageMgr.localStorageIsEnabled()||q.getDataFromLocalStorage(M)&&((0,v.logInfo)("".concat(F," - opt-out localStorage found, storage disabled")),1))}(e);case L:return function(e){return!(!e.storageMgr.cookiesAreEnabled()||q.getCookie(M)&&((0,v.logInfo)("".concat(F," - opt-out cookie found, storage disabled")),1))}(e)}return!1}))}}function ke(e){g.EID_CONFIG.clear(),Object.entries(ne(e,(function(e){return Object.keys(e.eids||{})}),(function(e){return e}))).forEach((function(e){var n=(0,i.A)(e,2),r=n[0],t=n[1];return g.EID_CONFIG.set(r,t[0].eids[r])}))}function je(){ke(V);var e=De(H);if(e.length){var n=V.filter((function(e){return!(0,u.I6)(R,(function(n){return n.name===e.name}))}));R.splice(0,R.length),n.map((function(n){var r=(0,u.I6)(e,(function(e){return e.name&&(e.name.toLowerCase()===n.name.toLowerCase()||n.aliasName&&e.name.toLowerCase()===n.aliasName.toLowerCase())}));return r&&n.name!==r.name&&(r.name=n.name),r?{submodule:n,config:r,callback:void 0,idObj:void 0,storageMgr:(0,m.vM)({moduleType:E.fW,moduleName:r.name})}:null})).filter((function(e){return null!==e})).forEach((function(e){return R.push(e)})),R.length&&(se()||(k.gH.getHooks({hook:le}).remove(),k.gH.before(ce,100),s.Ay.callDataDeletionRequest.before(_e),h.Q.after((function(e){return e(ue())}))),(0,v.logInfo)("".concat(F," - usersync config updated for ").concat(R.length," submodules: "),R.map((function(e){return e.submodule.name}))))}}function _e(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),t=1;t<n;t++)r[t-1]=arguments[t];(0,v.logInfo)("UserID: received data deletion request; deleting all stored IDs..."),R.forEach((function(e){if("function"==typeof e.submodule.onDataDeletionRequest)try{var n;(n=e.submodule).onDataDeletionRequest.apply(n,[e.config,e.idObj].concat(r))}catch(n){(0,v.logError)("Error calling onDataDeletionRequest for ID submodule ".concat(e.submodule.name),n)}z(e)})),e.apply(this,r)}function Ce(e){e.findRootDomain=I.S,(0,u.I6)(V,(function(n){return n.name===e.name}))||(V.push(e),S.o2.register(E.fW,e.name,e.gvlid),je(),ie({refresh:!0,submoduleNames:[e.name]}))}function Ue(e){return function(){return Promise.resolve(e.apply(this,arguments))}}function Te(e){var n=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).mkDelay,r=void 0===n?y.cb:n;N=void 0,R=[],H=[],_=re(),ie=function(){var e,n,r=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).mkDelay,t=void 0===r?y.cb:r,o=(0,y.v6)(),i=(0,y.v6)(),a=!1;function u(r){return n=B().fork(),null!=e&&e.reject(ae),e=(0,y.v6)(),y.U9.race([r,e.promise]).finally(n.startTiming("userId.total"))}var c=_,s=R;function g(e){return function(){if(c===_&&s===R)return e.apply(void 0,arguments)}}function m(){return S.SL.promise.finally(n.startTiming("userId.init.consent"))}var b=u(y.U9.all([f.Gc,o.promise]).then(m).then(g((function(){Ee(c,s)}))).then((function(){return i.promise.finally(n.startTiming("userId.callbacks.pending"))})).then(g((function(){var e=c.submodules.filter((function(e){return(0,v.isFn)(e.callback)}));if(e.length)return new y.U9((function(n){return Z(e,n,c)}))}))));return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.refresh,r=void 0!==n&&n,f=e.submoduleNames,v=void 0===f?null:f,h=e.ready;return void 0!==h&&h&&!a&&(a=!0,o.resolve(),T>0?i.resolve():l.on(d.qY.AUCTION_END,(function e(){l.off(d.qY.AUCTION_END,e),t(U).then(i.resolve)}))),r&&a&&(b=u(b.catch((function(){return null})).then(m).then(g((function(){var e=Ee(c,s.filter((function(e){return null==v||v.includes(e.submodule.name)})),!0).filter((function(e){return null!=e.callback}));if(e.length)return new y.U9((function(n){return Z(e,n,c)}))}))))),b}}({mkDelay:r}),null!=x&&x(),V=[],x=e.getConfig("userSync",(function(e){var n=e.userSync;n&&(N=n.ppid,n.userIds&&(H=n.userIds,U=(0,v.isNumber)(n.syncDelay)?n.syncDelay:O.qh.syncDelay,T=(0,v.isNumber)(n.auctionDelay)?n.auctionDelay:O.qh.auctionDelay,je(),function(e,n){if(e){var r={},o=new Map(n.map((function(e){return e.aliasName?[e.aliasName,e.name]:[]})));Object.keys(e).forEach((function(n){var i=(0,v.isArray)(e[n])?(0,t.A)(e[n]).reverse():[];r[n]=i.map((function(e){return o.has(e)?o.get(e):e}))})),J=r}else J={};_.refresh(),ke(n)}(n.idPriority,V),ie({ready:!0})))})),(0,a.m)().getUserIds=de,(0,a.m)().getUserIdsAsEids=fe,(0,a.m)().getEncryptedEidsForSource=Ue(me),(0,a.m)().registerSignalSources=be,(0,a.m)().refreshUserIds=Ue(ye),(0,a.m)().getUserIdsAsync=Ue(pe),(0,a.m)().getUserIdsAsEidBySource=ge,se()||k.gH.before(le,100)}function Ne(){c.$W.setConfig({userSync:{}}),Te(c.$W)}Te(c.$W),(0,f.xG)("userId",Ce,{postInstallAllowed:!0}),(0,a.E)("userId")},71877:(e,n,r)=>{r.r(n),r.d(n,{EID_CONFIG:()=>a,createEidsArray:()=>u,getEids:()=>c});var t=r(80296),o=r(45458),i=r(90869),a=new Map;function u(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a,r={};function u(e){var n,t,i=JSON.stringify([null===(n=e.source)||void 0===n?void 0:n.toLowerCase()].concat((0,o.A)(Object.keys(e).filter((function(e){return!["uids","source"].includes(e)})).sort().map((function(n){return e[n]})))));r.hasOwnProperty(i)?(t=r[i].uids).push.apply(t,(0,o.A)(e.uids)):r[i]=e}return Object.entries(e).forEach((function(e){var r=(0,t.A)(e,2),o=r[0],a=r[1];a=Array.isArray(a)?a:[a];var c,l=n.get(o);if("pubProvidedId"===o)c=(0,i.deepClone)(a);else if("function"==typeof l)try{c=l(a),Array.isArray(c)||(c=[c]),c.forEach((function(e){return e.uids=e.uids.filter((function(e){var n=e.id;return(0,i.isStr)(n)}))})),c=c.filter((function(e){var n=e.uids;return(null==n?void 0:n.length)>0}))}catch(e){(0,i.logError)('Could not generate EID for "'.concat(o,'"'),e)}else c=a.map((function(e){return function(e,n,r){if(r&&e){var t={};t.source=(0,i.isFn)(r.getSource)?r.getSource(e):r.source;var o=(0,i.isFn)(r.getValue)?r.getValue(e):e;if((0,i.isStr)(o)){var a={id:o,atype:r.atype};if((0,i.isFn)(r.getUidExt)){var u=r.getUidExt(e);u&&(a.ext=u)}if(t.uids=[a],(0,i.isFn)(r.getEidExt)){var c=r.getEidExt(e);c&&(t.ext=c)}return t}}return null}(e,0,l)}));Array.isArray(c)&&c.filter((function(e){return null!=e})).forEach(u)})),Object.values(r)}function c(e){var n=new Map,r={};return Object.entries(e).forEach((function(e){var o,i=(0,t.A)(e,2),a=i[0],u=(0,i[1])();if(u){var c;r[a]=u.idObj[a];var l=null===(c=u.submodule.eids)||void 0===c?void 0:c[a];"function"==typeof l&&(o=l,l=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return o.apply(void 0,n.concat([u.config]))}),n.set(a,l)}})),u(r,n)}},84738:(e,n,r)=>{r.d(n,{Q:()=>t});var t=(0,r(35563).A_)("sync",(function(){}))}}]);