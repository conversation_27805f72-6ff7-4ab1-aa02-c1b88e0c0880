"use strict";(self.__VM_WPK_C_LR__m=self.__VM_WPK_C_LR__m||[]).push([[45496],{33904:(o,e,n)=>{n.r(e),n.d(e,{sharedIdSystemSubmodule:()=>_,storage:()=>s});var t=n(82284),i=n(79675),r=n(90869),d=n(35563),a=n(39012),u=n(38830),l=n(60807),c=n(53285),s=(0,a.vM)({moduleType:l.fW,moduleName:"sharedId"}),m="cookie",f="html5",v="_pubcid_optout",g="PublisherCommonId";function p(o,e){if(e===m)return s.getCookie(o);if(e===f&&s.hasLocalStorage()){var n=s.getDataFromLocalStorage("".concat(o,"_exp"));if(!n)return s.getDataFromLocalStorage(o);if(new Date(n).getTime()-Date.now()>0)return s.getDataFromLocalStorage(o)}}function I(o,e){return function(n,t){e?h(e,o,(function(){n(t()||o)}))():n(o)}}function h(o){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2?arguments[2]:void 0;if(o){var t=(0,r.parseUrl)(o);t.search.id=encodeURIComponent("pubcid:"+e);var i=(0,r.buildUrl)(t);return function(){(0,r.triggerPixel)(i,n)}}}function b(){return!!(s.cookiesAreEnabled()&&p(v,m)||s.hasLocalStorage()&&p(v,f))}var _={name:"sharedId",aliasName:"pubCommonId",gvlid:u.B1,decode:function(o,e){if(!b())return(0,r.logInfo)(" Decoded value PubCommonId "+o),{pubcid:o};(0,r.logInfo)("PubCommonId decode: Has opted-out")},getId:function(){var o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0;if(b())(0,r.logInfo)("PubCommonId: Has opted-out");else{if(null==e||!e.coppa){var i=o.params,d=void 0===i?{}:i,a=d.create,u=void 0===a||a,l=d.pixelUrl,c=n;if(!c){try{"object"===(0,t.A)(window[g])&&(c=window[g].getId())}catch(o){}c||(c=u&&(0,r.hasDeviceAccess)()?(0,r.generateUUID)():void 0)}return{id:c,callback:I(c,l)}}(0,r.logInfo)("PubCommonId: IDs not provided for coppa requests, exiting PubCommonId")}},extendId:function(){var o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0;if(b())return(0,r.logInfo)("PubCommonId: Has opted-out"),{id:void 0};if(null!=e&&e.coppa)(0,r.logInfo)("PubCommonId: IDs not provided for coppa requests, exiting PubCommonId");else{var t=o.params,i=void 0===t?{}:t,d=i.extend,a=void 0!==d&&d,u=i.pixelUrl;if(a)return u?{callback:h(u,n)}:{id:n}}},domainOverride:(0,c.w)(s,"sharedId"),eids:{pubcid:function(o,e){var n,t={source:"pubcid.org",uids:o.map((function(o){return{id:o,atype:1}}))};return null!=(null==e||null===(n=e.params)||void 0===n?void 0:n.inserter)&&(t.inserter=e.params.inserter),t}}};(0,d.bz)("userId",_),(0,i.E)("sharedIdSystem")},53285:(o,e,n)=>{function t(o,e){return function(){for(var n,t,i=document.domain.split("."),r="_gd".concat(Date.now(),"_").concat(e),d=0;d<i.length;d++){var a=i.slice(d).join(".");if(o.setCookie(r,"1",void 0,void 0,a),t=o.getCookie(r),o.setCookie(r,"","Thu, 01 Jan 1970 00:00:01 GMT",void 0,a),"1"!==t)return n;n=a}}}n.d(e,{w:()=>t})}}]);