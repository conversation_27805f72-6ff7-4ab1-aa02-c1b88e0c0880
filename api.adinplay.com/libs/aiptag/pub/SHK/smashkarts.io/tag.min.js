(()=>{var e={23:(e,t,n)=>{"use strict";var r=n(6254),i=Function.prototype,o=i.apply,a=i.call;e.exports="object"==typeof Reflect&&Reflect.apply||(r?a.bind(o):function(){return a.apply(o,arguments)})},134:(e,t,n)=>{"use strict";var r=n(8042),i=n(8401);e.exports=Object.keys||function(e){return r(e,i)}},238:(e,t,n)=>{"use strict";var r={};r[n(4557)("toStringTag")]="z",e.exports="[object z]"===String(r)},410:(e,t,n)=>{"use strict";var r=n(23),i=n(5435),o=n(1950),a=n(1282),s=n(4685),l=n(8385),c=n(2167),u=n(5196),d=n(4353),p=n(8904),h=n(1353),m=n(1388),f=n(1839),g=n(6216),v=n(9560),b=n(8880),y=n(4557)("replace"),w=Math.max,x=Math.min,A=o([].concat),E=o([].push),k=o("".indexOf),I=o("".slice),S="$0"==="a".replace(/./,"$0"),P=!!/./[y]&&""===/./[y]("a","$0");a("replace",(function(e,t,n){var o=P?"$":"$0";return[function(e,n){var r=m(this),o=u(e)?g(e,y):void 0;return o?i(o,e,r,n):i(t,h(r),e,n)},function(e,i){var a=l(this),s=h(e);if("string"==typeof i&&-1===k(i,o)&&-1===k(i,"$<")){var u=n(t,a,s,i);if(u.done)return u.value}var m=c(i);m||(i=h(i));var g,y=a.global;y&&(g=a.unicode,a.lastIndex=0);for(var S,P=[];null!==(S=b(a,s))&&(E(P,S),y);)""===h(S[0])&&(a.lastIndex=f(s,p(a.lastIndex),g));for(var C,T="",M=0,O=0;O<P.length;O++){for(var R,_=h((S=P[O])[0]),L=w(x(d(S.index),s.length),0),N=[],D=1;D<S.length;D++)E(N,void 0===(C=S[D])?C:String(C));var j=S.groups;if(m){var B=A([_],N,L,s);void 0!==j&&E(B,j),R=h(r(i,void 0,B))}else R=v(_,s,L,N,j,i);L>=M&&(T+=I(s,M,L)+R,M=L+_.length)}return T+I(s,M)}]}),!!s((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")}))||!S||P)},457:(e,t,n)=>{"use strict";var r=n(4685),i=n(5502).RegExp;e.exports=r((function(){var e=i(".","s");return!(e.dotAll&&e.test("\n")&&"s"===e.flags)}))},463:(e,t,n)=>{"use strict";var r=n(5874),i=n(923),o=n(540),a=n(8385),s=n(2139),l=TypeError,c=Object.defineProperty,u=Object.getOwnPropertyDescriptor,d="enumerable",p="configurable",h="writable";t.f=r?o?function(e,t,n){if(a(e),t=s(t),a(n),"function"==typeof e&&"prototype"===t&&"value"in n&&h in n&&!n[h]){var r=u(e,t);r&&r[h]&&(e[t]=n.value,n={configurable:p in n?n[p]:r[p],enumerable:d in n?n[d]:r[d],writable:!1})}return c(e,t,n)}:c:function(e,t,n){if(a(e),t=s(t),a(n),i)try{return c(e,t,n)}catch(e){}if("get"in n||"set"in n)throw new l("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},497:(e,t,n)=>{"use strict";var r=n(1208),i=n(8277);r({target:"RegExp",proto:!0,forced:/./.exec!==i},{exec:i})},534:(e,t,n)=>{"use strict";var r=n(4685),i=n(2167),o=/#|\.prototype\./,a=function(e,t){var n=l[s(e)];return n===u||n!==c&&(i(t)?r(t):!!t)},s=a.normalize=function(e){return String(e).replace(o,".").toLowerCase()},l=a.data={},c=a.NATIVE="N",u=a.POLYFILL="P";e.exports=a},540:(e,t,n)=>{"use strict";var r=n(5874),i=n(4685);e.exports=r&&i((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},659:(e,t,n)=>{"use strict";var r=n(5874),i=n(540),o=n(463),a=n(8385),s=n(8323),l=n(134);t.f=r&&!i?Object.defineProperties:function(e,t){a(e);for(var n,r=s(t),i=l(t),c=i.length,u=0;c>u;)o.f(e,n=i[u++],r[n]);return e}},703:(e,t,n)=>{"use strict";var r=n(1388),i=Object;e.exports=function(e){return i(r(e))}},738:(e,t,n)=>{"use strict";var r=n(1477)("span").classList,i=r&&r.constructor&&r.constructor.prototype;e.exports=i===Object.prototype?void 0:i},790:(e,t,n)=>{"use strict";var r=n(2167),i=n(463),o=n(7757),a=n(9867);e.exports=function(e,t,n,s){s||(s={});var l=s.enumerable,c=void 0!==s.name?s.name:t;if(r(n)&&o(n,c,s),s.global)l?e[t]=n:a(t,n);else{try{s.unsafe?e[t]&&(l=!0):delete e[t]}catch(e){}l?e[t]=n:i.f(e,t,{value:n,enumerable:!1,configurable:!s.nonConfigurable,writable:!s.nonWritable})}return e}},829:e=>{"use strict";e.exports=function(e){return e[1]}},877:(e,t,n)=>{"use strict";var r=n(6117),i=n(1950),o=n(2850),a=n(5447),s=n(8385),l=i([].concat);e.exports=r("Reflect","ownKeys")||function(e){var t=o.f(s(e)),n=a.f;return n?l(t,n(e)):t}},897:(e,t,n)=>{var r=n(829),i=n(1150)(r);i.push([e.id,".mI58Gi15PAe8fqsHDOMI *{line-height:1.4em;vertical-align:middle}.mI58Gi15PAe8fqsHDOMI{display:flex;align-items:center;background-color:#f7fcff;box-shadow:0 0 3px rgba(0, 0, 0, 0.2);padding:0;border-radius:9px;position:fixed;z-index:999994;top:unset;bottom:5px;right:unset;left:5px;transition:background-color .3s ease,box-shadow .3s ease}.oSAXb5qPKj78_Sp8Oz9A{text-decoration:none;display:flex;align-items:center;padding:3px;border-radius:3px;transition:color .3s ease,transform .3s ease}.ciHgxN1orEDdqQlZUIRO{display:none;font-family:Arial,sans-serif;padding-right:5px;padding-left:2px;font-size:12px}.oSAXb5qPKj78_Sp8Oz9A:hover .ciHgxN1orEDdqQlZUIRO{display:inline;color:#3276ae}.uHNm6AHskWg7q6_iq6RI{top:unset;bottom:5px;right:5px;left:unset}.jbj_rARHnqU1Oj35bbgQ{top:unset;bottom:5px;right:unset;left:5px}.tXT909jFBi0yQpy3ku0Q{top:unset;bottom:5px;right:unset;left:50%;transform:translateX(-50%)}.ELiWH9h2FSasygiFrUsU{top:5px;bottom:unset;right:unset;left:50%;transform:translateX(-50%)}.EJLCLzR3Dm648i6W4F6I{top:5px;bottom:unset;right:5px;left:unset}.ou122XCMg9DfGPTD9R3w{top:5px;bottom:unset;right:unset;left:5px}",""]),i.locals={"cmp-persistent-link":"mI58Gi15PAe8fqsHDOMI","cmp-persistent-link-a":"oSAXb5qPKj78_Sp8Oz9A","cmp-persistent-link-txt":"ciHgxN1orEDdqQlZUIRO","bottom-right":"uHNm6AHskWg7q6_iq6RI","bottom-left":"jbj_rARHnqU1Oj35bbgQ","bottom-center":"tXT909jFBi0yQpy3ku0Q","top-center":"ELiWH9h2FSasygiFrUsU","top-right":"EJLCLzR3Dm648i6W4F6I","top-left":"ou122XCMg9DfGPTD9R3w"},e.exports=i},923:(e,t,n)=>{"use strict";var r=n(5874),i=n(4685),o=n(1477);e.exports=!r&&!i((function(){return 7!==Object.defineProperty(o("div"),"a",{get:function(){return 7}}).a}))},1080:(e,t,n)=>{"use strict";var r=n(5502),i=n(2167),o=r.WeakMap;e.exports=i(o)&&/native code/.test(String(o))},1113:e=>{"use strict";e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}},1131:(e,t,n)=>{"use strict";var r=n(5502),i=n(1358),o=n(738),a=n(7078),s=n(4513),l=n(4073),c=n(4557)("iterator"),u=a.values,d=function(e,t){if(e){if(e[c]!==u)try{s(e,c,u)}catch(t){e[c]=u}if(l(e,t,!0),i[t])for(var n in a)if(e[n]!==a[n])try{s(e,n,a[n])}catch(t){e[n]=a[n]}}};for(var p in i)d(r[p]&&r[p].prototype,p);d(o,"DOMTokenList")},1150:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n="",r=void 0!==t[5];return t[4]&&(n+="@supports (".concat(t[4],") {")),t[2]&&(n+="@media ".concat(t[2]," {")),r&&(n+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),n+=e(t),r&&(n+="}"),t[2]&&(n+="}"),t[4]&&(n+="}"),n})).join("")},t.i=function(e,n,r,i,o){"string"==typeof e&&(e=[[null,e,void 0]]);var a={};if(r)for(var s=0;s<this.length;s++){var l=this[s][0];null!=l&&(a[l]=!0)}for(var c=0;c<e.length;c++){var u=[].concat(e[c]);r&&a[u[0]]||(void 0!==o&&(void 0===u[5]||(u[1]="@layer".concat(u[5].length>0?" ".concat(u[5]):""," {").concat(u[1],"}")),u[5]=o),n&&(u[2]?(u[1]="@media ".concat(u[2]," {").concat(u[1],"}"),u[2]=n):u[2]=n),i&&(u[4]?(u[1]="@supports (".concat(u[4],") {").concat(u[1],"}"),u[4]=i):u[4]="".concat(i)),t.push(u))}},t}},1201:(e,t,n)=>{"use strict";var r=n(2167),i=n(5196),o=n(6429);e.exports=function(e,t,n){var a,s;return o&&r(a=t.constructor)&&a!==n&&i(s=a.prototype)&&s!==n.prototype&&o(e,s),e}},1208:(e,t,n)=>{"use strict";var r=n(5502),i=n(6397).f,o=n(4513),a=n(790),s=n(9867),l=n(3826),c=n(534);e.exports=function(e,t){var n,u,d,p,h,m=e.target,f=e.global,g=e.stat;if(n=f?r:g?r[m]||s(m,{}):r[m]&&r[m].prototype)for(u in t){if(p=t[u],d=e.dontCallGetSet?(h=i(n,u))&&h.value:n[u],!c(f?u:m+(g?".":"#")+u,e.forced)&&void 0!==d){if(typeof p==typeof d)continue;l(p,d)}(e.sham||d&&d.sham)&&o(p,"sham",!0),a(n,u,p,e)}}},1282:(e,t,n)=>{"use strict";n(497);var r=n(5435),i=n(790),o=n(8277),a=n(4685),s=n(4557),l=n(4513),c=s("species"),u=RegExp.prototype;e.exports=function(e,t,n,d){var p=s(e),h=!a((function(){var t={};return t[p]=function(){return 7},7!==""[e](t)})),m=h&&!a((function(){var t=!1,n=/a/;return"split"===e&&((n={}).constructor={},n.constructor[c]=function(){return n},n.flags="",n[p]=/./[p]),n.exec=function(){return t=!0,null},n[p](""),!t}));if(!h||!m||n){var f=/./[p],g=t(p,""[e],(function(e,t,n,i,a){var s=t.exec;return s===o||s===u.exec?h&&!a?{done:!0,value:r(f,t,n,i)}:{done:!0,value:r(e,n,t,i)}:{done:!1}}));i(String.prototype,e,g[0]),i(u,p,g[1])}d&&l(u[p],"sham",!0)}},1339:(e,t,n)=>{"use strict";var r=n(4557),i=n(4934),o=n(463).f,a=r("unscopables"),s=Array.prototype;void 0===s[a]&&o(s,a,{configurable:!0,value:i(null)}),e.exports=function(e){s[a][e]=!0}},1353:(e,t,n)=>{"use strict";var r=n(3389),i=String;e.exports=function(e){if("Symbol"===r(e))throw new TypeError("Cannot convert a Symbol value to a string");return i(e)}},1358:e=>{"use strict";e.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},1388:(e,t,n)=>{"use strict";var r=n(7755),i=TypeError;e.exports=function(e){if(r(e))throw new i("Can't call method on "+e);return e}},1477:(e,t,n)=>{"use strict";var r=n(5502),i=n(5196),o=r.document,a=i(o)&&i(o.createElement);e.exports=function(e){return a?o.createElement(e):{}}},1645:e=>{"use strict";var t=String;e.exports=function(e){try{return t(e)}catch(e){return"Object"}}},1724:(e,t,n)=>{"use strict";var r=n(2167),i=n(1645),o=TypeError;e.exports=function(e){if(r(e))return e;throw new o(i(e)+" is not a function")}},1738:(e,t,n)=>{"use strict";var r=n(1950),i=r({}.toString),o=r("".slice);e.exports=function(e){return o(i(e),8,-1)}},1839:(e,t,n)=>{"use strict";var r=n(8893).charAt;e.exports=function(e,t,n){return t+(n?r(e,t).length:1)}},1950:(e,t,n)=>{"use strict";var r=n(6254),i=Function.prototype,o=i.call,a=r&&i.bind.bind(o,o);e.exports=r?a:function(e){return function(){return o.apply(e,arguments)}}},2023:(e,t,n)=>{"use strict";var r=n(4685),i=n(5502).RegExp,o=r((function(){var e=i("a","y");return e.lastIndex=2,null!==e.exec("abcd")})),a=o||r((function(){return!i("a","y").sticky})),s=o||r((function(){var e=i("^r","gy");return e.lastIndex=2,null!==e.exec("str")}));e.exports={BROKEN_CARET:s,MISSED_STICKY:a,UNSUPPORTED_Y:o}},2139:(e,t,n)=>{"use strict";var r=n(6935),i=n(9587);e.exports=function(e){var t=r(e,"string");return i(t)?t:t+""}},2167:e=>{"use strict";var t="object"==typeof document&&document.all;e.exports=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(e){return"function"==typeof e}},2297:(e,t,n)=>{"use strict";var r,i,o=n(5502),a=n(5097),s=o.process,l=o.Deno,c=s&&s.versions||l&&l.version,u=c&&c.v8;u&&(i=(r=u.split("."))[0]>0&&r[0]<4?1:+(r[0]+r[1])),!i&&a&&(!(r=a.match(/Edge\/(\d+)/))||r[1]>=74)&&(r=a.match(/Chrome\/(\d+)/))&&(i=+r[1]),e.exports=i},2312:(e,t,n)=>{"use strict";var r=n(3731),i=String,o=TypeError;e.exports=function(e){if(r(e))return e;throw new o("Can't set "+i(e)+" as a prototype")}},2604:(e,t,n)=>{"use strict";var r=n(4685),i=n(5502).RegExp;e.exports=r((function(){var e=i("(?<a>b)","g");return"b"!==e.exec("b").groups.a||"bc"!=="b".replace(e,"$<a>c")}))},2818:(e,t,n)=>{"use strict";var r=n(5129);e.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},2850:(e,t,n)=>{"use strict";var r=n(8042),i=n(8401).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return r(e,i)}},3025:(e,t,n)=>{"use strict";var r=n(3127),i=n(2167),o=n(703),a=n(5053),s=n(3749),l=a("IE_PROTO"),c=Object,u=c.prototype;e.exports=s?c.getPrototypeOf:function(e){var t=o(e);if(r(t,l))return t[l];var n=t.constructor;return i(n)&&t instanceof n?n.prototype:t instanceof c?u:null}},3127:(e,t,n)=>{"use strict";var r=n(1950),i=n(703),o=r({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return o(i(e),t)}},3198:(e,t,n)=>{"use strict";var r=n(8990),i=Math.floor,o=function(e,t){var n=e.length;if(n<8)for(var a,s,l=1;l<n;){for(s=l,a=e[l];s&&t(e[s-1],a)>0;)e[s]=e[--s];s!==l++&&(e[s]=a)}else for(var c=i(n/2),u=o(r(e,0,c),t),d=o(r(e,c),t),p=u.length,h=d.length,m=0,f=0;m<p||f<h;)e[m+f]=m<p&&f<h?t(u[m],d[f])<=0?u[m++]:d[f++]:m<p?u[m++]:d[f++];return e};e.exports=o},3251:(e,t,n)=>{"use strict";var r=n(5443);e.exports=function(e,t){return r[e]||(r[e]=t||{})}},3389:(e,t,n)=>{"use strict";var r=n(238),i=n(2167),o=n(1738),a=n(4557)("toStringTag"),s=Object,l="Arguments"===o(function(){return arguments}());e.exports=r?o:function(e){var t,n,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(e){}}(t=s(e),a))?n:l?o(t):"Object"===(r=o(t))&&i(t.callee)?"Arguments":r}},3411:e=>{"use strict";var t=Math.ceil,n=Math.floor;e.exports=Math.trunc||function(e){var r=+e;return(r>0?n:t)(r)}},3434:(e,t,n)=>{"use strict";var r=n(463).f;e.exports=function(e,t,n){n in e||r(e,n,{configurable:!0,get:function(){return t[n]},set:function(e){t[n]=e}})}},3441:e=>{"use strict";e.exports=!1},3462:e=>{"use strict";e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},3676:(e,t,n)=>{"use strict";var r=n(1208),i=n(4260).trim;r({target:"String",proto:!0,forced:n(5096)("trim")},{trim:function(){return i(this)}})},3688:(e,t,n)=>{"use strict";var r=n(1645),i=TypeError;e.exports=function(e,t){if(!delete e[t])throw new i("Cannot delete property "+r(t)+" of "+r(e))}},3731:(e,t,n)=>{"use strict";var r=n(5196);e.exports=function(e){return r(e)||null===e}},3749:(e,t,n)=>{"use strict";var r=n(4685);e.exports=!r((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype}))},3826:(e,t,n)=>{"use strict";var r=n(3127),i=n(877),o=n(6397),a=n(463);e.exports=function(e,t,n){for(var s=i(t),l=a.f,c=o.f,u=0;u<s.length;u++){var d=s[u];r(e,d)||n&&r(n,d)||l(e,d,c(t,d))}}},3887:(e,t,n)=>{"use strict";var r=n(6117);e.exports=r("document","documentElement")},3938:(e,t,n)=>{"use strict";var r=n(5874),i=n(5502),o=n(1950),a=n(534),s=n(1201),l=n(4513),c=n(4934),u=n(2850).f,d=n(9651),p=n(9062),h=n(1353),m=n(4460),f=n(2023),g=n(3434),v=n(790),b=n(4685),y=n(3127),w=n(5175).enforce,x=n(8463),A=n(4557),E=n(457),k=n(2604),I=A("match"),S=i.RegExp,P=S.prototype,C=i.SyntaxError,T=o(P.exec),M=o("".charAt),O=o("".replace),R=o("".indexOf),_=o("".slice),L=/^\?<[^\s\d!#%&*+<=>@^][^\s!#%&*+<=>@^]*>/,N=/a/g,D=/a/g,j=new S(N)!==N,B=f.MISSED_STICKY,z=f.UNSUPPORTED_Y;if(a("RegExp",r&&(!j||B||E||k||b((function(){return D[I]=!1,S(N)!==N||S(D)===D||"/a/i"!==String(S(N,"i"))}))))){for(var W=function(e,t){var n,r,i,o,a,u,f=d(P,this),g=p(e),v=void 0===t,b=[],x=e;if(!f&&g&&v&&e.constructor===W)return e;if((g||d(P,e))&&(e=e.source,v&&(t=m(x))),e=void 0===e?"":h(e),t=void 0===t?"":h(t),x=e,E&&"dotAll"in N&&(r=!!t&&R(t,"s")>-1)&&(t=O(t,/s/g,"")),n=t,B&&"sticky"in N&&(i=!!t&&R(t,"y")>-1)&&z&&(t=O(t,/y/g,"")),k&&(o=function(e){for(var t,n=e.length,r=0,i="",o=[],a=c(null),s=!1,l=!1,u=0,d="";r<=n;r++){if("\\"===(t=M(e,r)))t+=M(e,++r);else if("]"===t)s=!1;else if(!s)switch(!0){case"["===t:s=!0;break;case"("===t:if(i+=t,"?:"===_(e,r+1,r+3))continue;T(L,_(e,r+1))&&(r+=2,l=!0),u++;continue;case">"===t&&l:if(""===d||y(a,d))throw new C("Invalid capture group name");a[d]=!0,o[o.length]=[d,u],l=!1,d="";continue}l?d+=t:i+=t}return[i,o]}(e),e=o[0],b=o[1]),a=s(S(e,t),f?this:P,W),(r||i||b.length)&&(u=w(a),r&&(u.dotAll=!0,u.raw=W(function(e){for(var t,n=e.length,r=0,i="",o=!1;r<=n;r++)"\\"!==(t=M(e,r))?o||"."!==t?("["===t?o=!0:"]"===t&&(o=!1),i+=t):i+="[\\s\\S]":i+=t+M(e,++r);return i}(e),n)),i&&(u.sticky=!0),b.length&&(u.groups=b)),e!==x)try{l(a,"source",""===x?"(?:)":x)}catch(e){}return a},F=u(S),U=0;F.length>U;)g(W,S,F[U++]);P.constructor=W,W.prototype=P,v(i,"RegExp",W,{constructor:!0})}x("RegExp")},4073:(e,t,n)=>{"use strict";var r=n(463).f,i=n(3127),o=n(4557)("toStringTag");e.exports=function(e,t,n){e&&!n&&(e=e.prototype),e&&!i(e,o)&&r(e,o,{configurable:!0,value:t})}},4260:(e,t,n)=>{"use strict";var r=n(1950),i=n(1388),o=n(1353),a=n(7938),s=r("".replace),l=RegExp("^["+a+"]+"),c=RegExp("(^|[^"+a+"])["+a+"]+$"),u=function(e){return function(t){var n=o(i(t));return 1&e&&(n=s(n,l,"")),2&e&&(n=s(n,c,"$1")),n}};e.exports={start:u(1),end:u(2),trim:u(3)}},4292:(e,t,n)=>{"use strict";var r=n(7757),i=n(463);e.exports=function(e,t,n){return n.get&&r(n.get,t,{getter:!0}),n.set&&r(n.set,t,{setter:!0}),i.f(e,t,n)}},4353:(e,t,n)=>{"use strict";var r=n(3411);e.exports=function(e){var t=+e;return t!=t||0===t?0:r(t)}},4460:(e,t,n)=>{"use strict";var r=n(5435),i=n(3127),o=n(9651),a=n(6257),s=RegExp.prototype;e.exports=function(e){var t=e.flags;return void 0!==t||"flags"in s||i(e,"flags")||!o(s,e)?t:r(a,e)}},4513:(e,t,n)=>{"use strict";var r=n(5874),i=n(463),o=n(3462);e.exports=r?function(e,t,n){return i.f(e,t,o(1,n))}:function(e,t,n){return e[t]=n,e}},4557:(e,t,n)=>{"use strict";var r=n(5502),i=n(3251),o=n(3127),a=n(6958),s=n(5129),l=n(2818),c=r.Symbol,u=i("wks"),d=l?c.for||c:c&&c.withoutSetter||a;e.exports=function(e){return o(u,e)||(u[e]=s&&o(c,e)?c[e]:d("Symbol."+e)),u[e]}},4685:e=>{"use strict";e.exports=function(e){try{return!!e()}catch(e){return!0}}},4772:(e,t,n)=>{"use strict";var r=n(5874),i=n(3127),o=Function.prototype,a=r&&Object.getOwnPropertyDescriptor,s=i(o,"name"),l=s&&"something"===function(){}.name,c=s&&(!r||r&&a(o,"name").configurable);e.exports={EXISTS:s,PROPER:l,CONFIGURABLE:c}},4934:(e,t,n)=>{"use strict";var r,i=n(8385),o=n(659),a=n(8401),s=n(8471),l=n(3887),c=n(1477),u=n(5053),d="prototype",p="script",h=u("IE_PROTO"),m=function(){},f=function(e){return"<"+p+">"+e+"</"+p+">"},g=function(e){e.write(f("")),e.close();var t=e.parentWindow.Object;return e=null,t},v=function(){try{r=new ActiveXObject("htmlfile")}catch(e){}var e,t,n;v="undefined"!=typeof document?document.domain&&r?g(r):(t=c("iframe"),n="java"+p+":",t.style.display="none",l.appendChild(t),t.src=String(n),(e=t.contentWindow.document).open(),e.write(f("document.F=Object")),e.close(),e.F):g(r);for(var i=a.length;i--;)delete v[d][a[i]];return v()};s[h]=!0,e.exports=Object.create||function(e,t){var n;return null!==e?(m[d]=i(e),n=new m,m[d]=null,n[h]=e):n=v(),void 0===t?n:o.f(n,t)}},5053:(e,t,n)=>{"use strict";var r=n(3251),i=n(6958),o=r("keys");e.exports=function(e){return o[e]||(o[e]=i(e))}},5055:(e,t)=>{"use strict";var n={}.propertyIsEnumerable,r=Object.getOwnPropertyDescriptor,i=r&&!n.call({1:2},1);t.f=i?function(e){var t=r(this,e);return!!t&&t.enumerable}:n},5056:(e,t,n)=>{"use strict";e.exports=function(e){var t=n.nc;t&&e.setAttribute("nonce",t)}},5072:e=>{"use strict";var t=[];function n(e){for(var n=-1,r=0;r<t.length;r++)if(t[r].identifier===e){n=r;break}return n}function r(e,r){for(var o={},a=[],s=0;s<e.length;s++){var l=e[s],c=r.base?l[0]+r.base:l[0],u=o[c]||0,d="".concat(c," ").concat(u);o[c]=u+1;var p=n(d),h={css:l[1],media:l[2],sourceMap:l[3],supports:l[4],layer:l[5]};if(-1!==p)t[p].references++,t[p].updater(h);else{var m=i(h,r);r.byIndex=s,t.splice(s,0,{identifier:d,updater:m,references:1})}a.push(d)}return a}function i(e,t){var n=t.domAPI(t);return n.update(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;n.update(e=t)}else n.remove()}}e.exports=function(e,i){var o=r(e=e||[],i=i||{});return function(e){e=e||[];for(var a=0;a<o.length;a++){var s=n(o[a]);t[s].references--}for(var l=r(e,i),c=0;c<o.length;c++){var u=n(o[c]);0===t[u].references&&(t[u].updater(),t.splice(u,1))}o=l}}},5096:(e,t,n)=>{"use strict";var r=n(4772).PROPER,i=n(4685),o=n(7938);e.exports=function(e){return i((function(){return!!o[e]()||"​᠎"!=="​᠎"[e]()||r&&o[e].name!==e}))}},5097:(e,t,n)=>{"use strict";var r=n(5502).navigator,i=r&&r.userAgent;e.exports=i?String(i):""},5129:(e,t,n)=>{"use strict";var r=n(2297),i=n(4685),o=n(5502).String;e.exports=!!Object.getOwnPropertySymbols&&!i((function(){var e=Symbol("symbol detection");return!o(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&r&&r<41}))},5175:(e,t,n)=>{"use strict";var r,i,o,a=n(1080),s=n(5502),l=n(5196),c=n(4513),u=n(3127),d=n(5443),p=n(5053),h=n(8471),m="Object already initialized",f=s.TypeError,g=s.WeakMap;if(a||d.state){var v=d.state||(d.state=new g);v.get=v.get,v.has=v.has,v.set=v.set,r=function(e,t){if(v.has(e))throw new f(m);return t.facade=e,v.set(e,t),t},i=function(e){return v.get(e)||{}},o=function(e){return v.has(e)}}else{var b=p("state");h[b]=!0,r=function(e,t){if(u(e,b))throw new f(m);return t.facade=e,c(e,b,t),t},i=function(e){return u(e,b)?e[b]:{}},o=function(e){return u(e,b)}}e.exports={set:r,get:i,has:o,enforce:function(e){return o(e)?i(e):r(e,{})},getterFor:function(e){return function(t){var n;if(!l(t)||(n=i(t)).type!==e)throw new f("Incompatible receiver, "+e+" required");return n}}}},5196:(e,t,n)=>{"use strict";var r=n(2167);e.exports=function(e){return"object"==typeof e?null!==e:r(e)}},5240:(e,t,n)=>{"use strict";var r=n(5435),i=n(2167),o=n(5196),a=TypeError;e.exports=function(e,t){var n,s;if("string"===t&&i(n=e.toString)&&!o(s=r(n,e)))return s;if(i(n=e.valueOf)&&!o(s=r(n,e)))return s;if("string"!==t&&i(n=e.toString)&&!o(s=r(n,e)))return s;throw new a("Can't convert object to primitive value")}},5279:(e,t,n)=>{"use strict";var r,i,o,a=n(4685),s=n(2167),l=n(5196),c=n(4934),u=n(3025),d=n(790),p=n(4557),h=n(3441),m=p("iterator"),f=!1;[].keys&&("next"in(o=[].keys())?(i=u(u(o)))!==Object.prototype&&(r=i):f=!0),!l(r)||a((function(){var e={};return r[m].call(e)!==e}))?r={}:h&&(r=c(r)),s(r[m])||d(r,m,(function(){return this})),e.exports={IteratorPrototype:r,BUGGY_SAFARI_ITERATORS:f}},5298:(e,t,n)=>{"use strict";var r=n(1208),i=n(5435),o=n(3441),a=n(4772),s=n(2167),l=n(8700),c=n(3025),u=n(6429),d=n(4073),p=n(4513),h=n(790),m=n(4557),f=n(7275),g=n(5279),v=a.PROPER,b=a.CONFIGURABLE,y=g.IteratorPrototype,w=g.BUGGY_SAFARI_ITERATORS,x=m("iterator"),A="keys",E="values",k="entries",I=function(){return this};e.exports=function(e,t,n,a,m,g,S){l(n,t,a);var P,C,T,M=function(e){if(e===m&&N)return N;if(!w&&e&&e in _)return _[e];switch(e){case A:case E:case k:return function(){return new n(this,e)}}return function(){return new n(this)}},O=t+" Iterator",R=!1,_=e.prototype,L=_[x]||_["@@iterator"]||m&&_[m],N=!w&&L||M(m),D="Array"===t&&_.entries||L;if(D&&(P=c(D.call(new e)))!==Object.prototype&&P.next&&(o||c(P)===y||(u?u(P,y):s(P[x])||h(P,x,I)),d(P,O,!0,!0),o&&(f[O]=I)),v&&m===E&&L&&L.name!==E&&(!o&&b?p(_,"name",E):(R=!0,N=function(){return i(L,this)})),m)if(C={values:M(E),keys:g?N:M(A),entries:M(k)},S)for(T in C)(w||R||!(T in _))&&h(_,T,C[T]);else r({target:t,proto:!0,forced:w||R},C);return o&&!S||_[x]===N||h(_,x,N,{name:m}),f[t]=N,C}},5435:(e,t,n)=>{"use strict";var r=n(6254),i=Function.prototype.call;e.exports=r?i.bind(i):function(){return i.apply(i,arguments)}},5443:(e,t,n)=>{"use strict";var r=n(3441),i=n(5502),o=n(9867),a="__core-js_shared__",s=e.exports=i[a]||o(a,{});(s.versions||(s.versions=[])).push({version:"3.42.0",mode:r?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.42.0/LICENSE",source:"https://github.com/zloirock/core-js"})},5447:(e,t)=>{"use strict";t.f=Object.getOwnPropertySymbols},5502:function(e,t,n){"use strict";var r=function(e){return e&&e.Math===Math&&e};e.exports=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof n.g&&n.g)||r("object"==typeof this&&this)||function(){return this}()||Function("return this")()},5868:(e,t,n)=>{"use strict";var r=n(4685);e.exports=function(e,t){var n=[][e];return!!n&&r((function(){n.call(null,t||function(){return 1},1)}))}},5874:(e,t,n)=>{"use strict";var r=n(4685);e.exports=!r((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},6117:(e,t,n)=>{"use strict";var r=n(5502),i=n(2167);e.exports=function(e,t){return arguments.length<2?(n=r[e],i(n)?n:void 0):r[e]&&r[e][t];var n}},6216:(e,t,n)=>{"use strict";var r=n(1724),i=n(7755);e.exports=function(e,t){var n=e[t];return i(n)?void 0:r(n)}},6254:(e,t,n)=>{"use strict";var r=n(4685);e.exports=!r((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},6257:(e,t,n)=>{"use strict";var r=n(8385);e.exports=function(){var e=r(this),t="";return e.hasIndices&&(t+="d"),e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.unicodeSets&&(t+="v"),e.sticky&&(t+="y"),t}},6388:(e,t,n)=>{"use strict";var r=n(1208),i=n(1950),o=n(1724),a=n(703),s=n(7772),l=n(3688),c=n(1353),u=n(4685),d=n(3198),p=n(5868),h=n(6787),m=n(7633),f=n(2297),g=n(8841),v=[],b=i(v.sort),y=i(v.push),w=u((function(){v.sort(void 0)})),x=u((function(){v.sort(null)})),A=p("sort"),E=!u((function(){if(f)return f<70;if(!(h&&h>3)){if(m)return!0;if(g)return g<603;var e,t,n,r,i="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(r=0;r<47;r++)v.push({k:t+r,v:n})}for(v.sort((function(e,t){return t.v-e.v})),r=0;r<v.length;r++)t=v[r].k.charAt(0),i.charAt(i.length-1)!==t&&(i+=t);return"DGBEFHACIJK"!==i}}));r({target:"Array",proto:!0,forced:w||!x||!A||!E},{sort:function(e){void 0!==e&&o(e);var t=a(this);if(E)return void 0===e?b(t):b(t,e);var n,r,i=[],u=s(t);for(r=0;r<u;r++)r in t&&y(i,t[r]);for(d(i,function(e){return function(t,n){return void 0===n?-1:void 0===t?1:void 0!==e?+e(t,n)||0:c(t)>c(n)?1:-1}}(e)),n=s(i),r=0;r<n;)t[r]=i[r++];for(;r<u;)l(t,r++);return t}})},6397:(e,t,n)=>{"use strict";var r=n(5874),i=n(5435),o=n(5055),a=n(3462),s=n(8323),l=n(2139),c=n(3127),u=n(923),d=Object.getOwnPropertyDescriptor;t.f=r?d:function(e,t){if(e=s(e),t=l(t),u)try{return d(e,t)}catch(e){}if(c(e,t))return a(!i(o.f,e,t),e[t])}},6429:(e,t,n)=>{"use strict";var r=n(7276),i=n(5196),o=n(1388),a=n(2312);e.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{(e=r(Object.prototype,"__proto__","set"))(n,[]),t=n instanceof Array}catch(e){}return function(n,r){return o(n),a(r),i(n)?(t?e(n,r):n.__proto__=r,n):n}}():void 0)},6787:(e,t,n)=>{"use strict";var r=n(5097).match(/firefox\/(\d+)/i);e.exports=!!r&&+r[1]},6935:(e,t,n)=>{"use strict";var r=n(5435),i=n(5196),o=n(9587),a=n(6216),s=n(5240),l=n(4557),c=TypeError,u=l("toPrimitive");e.exports=function(e,t){if(!i(e)||o(e))return e;var n,l=a(e,u);if(l){if(void 0===t&&(t="default"),n=r(l,e,t),!i(n)||o(n))return n;throw new c("Can't convert object to primitive value")}return void 0===t&&(t="number"),s(e,t)}},6958:(e,t,n)=>{"use strict";var r=n(1950),i=0,o=Math.random(),a=r(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+a(++i+o,36)}},7078:(e,t,n)=>{"use strict";var r=n(8323),i=n(1339),o=n(7275),a=n(5175),s=n(463).f,l=n(5298),c=n(9819),u=n(3441),d=n(5874),p="Array Iterator",h=a.set,m=a.getterFor(p);e.exports=l(Array,"Array",(function(e,t){h(this,{type:p,target:r(e),index:0,kind:t})}),(function(){var e=m(this),t=e.target,n=e.index++;if(!t||n>=t.length)return e.target=null,c(void 0,!0);switch(e.kind){case"keys":return c(n,!1);case"values":return c(t[n],!1)}return c([n,t[n]],!1)}),"values");var f=o.Arguments=o.Array;if(i("keys"),i("values"),i("entries"),!u&&d&&"values"!==f.name)try{s(f,"name",{value:"values"})}catch(e){}},7186:(e,t,n)=>{var r=n(829),i=n(1150)(r);i.push([e.id,'.It9xAeq8bjIwRQ6F6LVq,.It9xAeq8bjIwRQ6F6LVq:before,.It9xAeq8bjIwRQ6F6LVq:after{border-radius:50%;width:2.5em;height:2.5em;animation-fill-mode:both;animation:yzIMaOqXQt0Qw0opdSpX 1.8s infinite ease-in-out}.It9xAeq8bjIwRQ6F6LVq{color:#9e0515;font-size:7px;position:absolute;text-indent:-9999em;transform:translateZ(0);animation-delay:-0.16s;top:calc(50% - 3.75em);left:calc(50% - 1.25em);display:block}.It9xAeq8bjIwRQ6F6LVq:before,.It9xAeq8bjIwRQ6F6LVq:after{content:"";position:absolute;top:0}.It9xAeq8bjIwRQ6F6LVq:before{left:-3.5em;animation-delay:-0.32s}.It9xAeq8bjIwRQ6F6LVq:after{left:3.5em}@keyframes yzIMaOqXQt0Qw0opdSpX{0%,80%,100%{box-shadow:0 2.5em 0 -1.3em}40%{box-shadow:0 2.5em 0 0}}',""]),i.locals={"pr-spinner-loader":"It9xAeq8bjIwRQ6F6LVq",prSpinnerFadInOut:"yzIMaOqXQt0Qw0opdSpX"},e.exports=i},7275:e=>{"use strict";e.exports={}},7276:(e,t,n)=>{"use strict";var r=n(1950),i=n(1724);e.exports=function(e,t,n){try{return r(i(Object.getOwnPropertyDescriptor(e,t)[n]))}catch(e){}}},7633:(e,t,n)=>{"use strict";var r=n(5097);e.exports=/MSIE|Trident/.test(r)},7659:e=>{"use strict";var t={};e.exports=function(e,n){var r=function(e){if(void 0===t[e]){var n=document.querySelector(e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}t[e]=n}return t[e]}(e);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(n)}},7693:(e,t,n)=>{"use strict";var r=n(1950),i=n(4685),o=n(1738),a=Object,s=r("".split);e.exports=i((function(){return!a("z").propertyIsEnumerable(0)}))?function(e){return"String"===o(e)?s(e,""):a(e)}:a},7755:e=>{"use strict";e.exports=function(e){return null==e}},7757:(e,t,n)=>{"use strict";var r=n(1950),i=n(4685),o=(n(2167),n(3127)),a=n(5874),s=n(4772).CONFIGURABLE,l=(n(9928),n(5175)),c=l.enforce,u=(l.get,String),d=Object.defineProperty,p=r("".slice),h=r("".replace),m=r([].join),f=a&&!i((function(){return 8!==d((function(){}),"length",{value:8}).length})),g=String(String).split("String");e.exports=function(e,t,n){"Symbol("===p(u(t),0,7)&&(t="["+h(u(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),n&&n.getter&&(t="get "+t),n&&n.setter&&(t="set "+t),(!o(e,"name")||s&&e.name!==t)&&(a?d(e,"name",{value:t,configurable:!0}):e.name=t),f&&n&&o(n,"arity")&&e.length!==n.arity&&d(e,"length",{value:n.arity});try{n&&o(n,"constructor")&&n.constructor?a&&d(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var r=c(e);return o(r,"source")||(r.source=m(g,"string"==typeof t?t:"")),e}},7772:(e,t,n)=>{"use strict";var r=n(8904);e.exports=function(e){return r(e.length)}},7825:e=>{"use strict";e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=e.insertStyleElement(e);return{update:function(n){!function(e,t,n){var r="";n.supports&&(r+="@supports (".concat(n.supports,") {")),n.media&&(r+="@media ".concat(n.media," {"));var i=void 0!==n.layer;i&&(r+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")),r+=n.css,i&&(r+="}"),n.media&&(r+="}"),n.supports&&(r+="}");var o=n.sourceMap;o&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(o))))," */")),t.styleTagTransform(r,e,t.options)}(t,e,n)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},7938:e=>{"use strict";e.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},7972:(e,t,n)=>{"use strict";var r=n(4353),i=Math.max,o=Math.min;e.exports=function(e,t){var n=r(e);return n<0?i(n+t,0):o(n,t)}},8042:(e,t,n)=>{"use strict";var r=n(1950),i=n(3127),o=n(8323),a=n(9943).indexOf,s=n(8471),l=r([].push);e.exports=function(e,t){var n,r=o(e),c=0,u=[];for(n in r)!i(s,n)&&i(r,n)&&l(u,n);for(;t.length>c;)i(r,n=t[c++])&&(~a(u,n)||l(u,n));return u}},8159:e=>{"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},8277:(e,t,n)=>{"use strict";var r,i,o=n(5435),a=n(1950),s=n(1353),l=n(6257),c=n(2023),u=n(3251),d=n(4934),p=n(5175).get,h=n(457),m=n(2604),f=u("native-string-replace",String.prototype.replace),g=RegExp.prototype.exec,v=g,b=a("".charAt),y=a("".indexOf),w=a("".replace),x=a("".slice),A=(i=/b*/g,o(g,r=/a/,"a"),o(g,i,"a"),0!==r.lastIndex||0!==i.lastIndex),E=c.BROKEN_CARET,k=void 0!==/()??/.exec("")[1];(A||k||E||h||m)&&(v=function(e){var t,n,r,i,a,c,u,h=this,m=p(h),I=s(e),S=m.raw;if(S)return S.lastIndex=h.lastIndex,t=o(v,S,I),h.lastIndex=S.lastIndex,t;var P=m.groups,C=E&&h.sticky,T=o(l,h),M=h.source,O=0,R=I;if(C&&(T=w(T,"y",""),-1===y(T,"g")&&(T+="g"),R=x(I,h.lastIndex),h.lastIndex>0&&(!h.multiline||h.multiline&&"\n"!==b(I,h.lastIndex-1))&&(M="(?: "+M+")",R=" "+R,O++),n=new RegExp("^(?:"+M+")",T)),k&&(n=new RegExp("^"+M+"$(?!\\s)",T)),A&&(r=h.lastIndex),i=o(g,C?n:h,R),C?i?(i.input=x(i.input,O),i[0]=x(i[0],O),i.index=h.lastIndex,h.lastIndex+=i[0].length):h.lastIndex=0:A&&i&&(h.lastIndex=h.global?i.index+i[0].length:r),k&&i&&i.length>1&&o(f,i[0],n,(function(){for(a=1;a<arguments.length-2;a++)void 0===arguments[a]&&(i[a]=void 0)})),i&&P)for(i.groups=c=d(null),a=0;a<P.length;a++)c[(u=P[a])[0]]=i[u[1]];return i}),e.exports=v},8323:(e,t,n)=>{"use strict";var r=n(7693),i=n(1388);e.exports=function(e){return r(i(e))}},8385:(e,t,n)=>{"use strict";var r=n(5196),i=String,o=TypeError;e.exports=function(e){if(r(e))return e;throw new o(i(e)+" is not an object")}},8401:e=>{"use strict";e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},8463:(e,t,n)=>{"use strict";var r=n(6117),i=n(4292),o=n(4557),a=n(5874),s=o("species");e.exports=function(e){var t=r(e);a&&t&&!t[s]&&i(t,s,{configurable:!0,get:function(){return this}})}},8471:e=>{"use strict";e.exports={}},8700:(e,t,n)=>{"use strict";var r=n(5279).IteratorPrototype,i=n(4934),o=n(3462),a=n(4073),s=n(7275),l=function(){return this};e.exports=function(e,t,n,c){var u=t+" Iterator";return e.prototype=i(r,{next:o(+!c,n)}),a(e,u,!1,!0),s[u]=l,e}},8841:(e,t,n)=>{"use strict";var r=n(5097).match(/AppleWebKit\/(\d+)\./);e.exports=!!r&&+r[1]},8880:(e,t,n)=>{"use strict";var r=n(5435),i=n(8385),o=n(2167),a=n(1738),s=n(8277),l=TypeError;e.exports=function(e,t){var n=e.exec;if(o(n)){var c=r(n,e,t);return null!==c&&i(c),c}if("RegExp"===a(e))return r(s,e,t);throw new l("RegExp#exec called on incompatible receiver")}},8893:(e,t,n)=>{"use strict";var r=n(1950),i=n(4353),o=n(1353),a=n(1388),s=r("".charAt),l=r("".charCodeAt),c=r("".slice),u=function(e){return function(t,n){var r,u,d=o(a(t)),p=i(n),h=d.length;return p<0||p>=h?e?"":void 0:(r=l(d,p))<55296||r>56319||p+1===h||(u=l(d,p+1))<56320||u>57343?e?s(d,p):r:e?c(d,p,p+2):u-56320+(r-55296<<10)+65536}};e.exports={codeAt:u(!1),charAt:u(!0)}},8904:(e,t,n)=>{"use strict";var r=n(4353),i=Math.min;e.exports=function(e){var t=r(e);return t>0?i(t,9007199254740991):0}},8990:(e,t,n)=>{"use strict";var r=n(1950);e.exports=r([].slice)},9062:(e,t,n)=>{"use strict";var r=n(5196),i=n(1738),o=n(4557)("match");e.exports=function(e){var t;return r(e)&&(void 0!==(t=e[o])?!!t:"RegExp"===i(e))}},9560:(e,t,n)=>{"use strict";var r=n(1950),i=n(703),o=Math.floor,a=r("".charAt),s=r("".replace),l=r("".slice),c=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,u=/\$([$&'`]|\d{1,2})/g;e.exports=function(e,t,n,r,d,p){var h=n+e.length,m=r.length,f=u;return void 0!==d&&(d=i(d),f=c),s(p,f,(function(i,s){var c;switch(a(s,0)){case"$":return"$";case"&":return e;case"`":return l(t,0,n);case"'":return l(t,h);case"<":c=d[l(s,1,-1)];break;default:var u=+s;if(0===u)return i;if(u>m){var p=o(u/10);return 0===p?i:p<=m?void 0===r[p-1]?a(s,1):r[p-1]+a(s,1):i}c=r[u-1]}return void 0===c?"":c}))}},9587:(e,t,n)=>{"use strict";var r=n(6117),i=n(2167),o=n(9651),a=n(2818),s=Object;e.exports=a?function(e){return"symbol"==typeof e}:function(e){var t=r("Symbol");return i(t)&&o(t.prototype,s(e))}},9651:(e,t,n)=>{"use strict";var r=n(1950);e.exports=r({}.isPrototypeOf)},9819:e=>{"use strict";e.exports=function(e,t){return{value:e,done:t}}},9867:(e,t,n)=>{"use strict";var r=n(5502),i=Object.defineProperty;e.exports=function(e,t){try{i(r,e,{value:t,configurable:!0,writable:!0})}catch(n){r[e]=t}return t}},9928:(e,t,n)=>{"use strict";var r=n(1950),i=n(2167),o=n(5443),a=r(Function.toString);i(o.inspectSource)||(o.inspectSource=function(e){return a(e)}),e.exports=o.inspectSource},9943:(e,t,n)=>{"use strict";var r=n(8323),i=n(7972),o=n(7772),a=function(e){return function(t,n,a){var s=r(t),l=o(s);if(0===l)return!e&&-1;var c,u=i(a,l);if(e&&n!=n){for(;l>u;)if((c=s[u++])!=c)return!0}else for(;l>u;u++)if((e||u in s)&&s[u]===n)return e||u||0;return!e&&-1}};e.exports={includes:a(!0),indexOf:a(!1)}}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={id:r,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.nc=void 0,(()=>{"use strict";n(1131),n(497),n(3676);const e=Object.prototype.toString;function t(e){return!(!function(e){return void 0!==e}(e)||null==e)}function r(t,n){return e.call(t)==="[object "+n+"]"}function i(e){return!!t(e)&&("string"==typeof e||r(e,"String"))}function o(e){return!!t(e)&&("function"==typeof e||r(e,"Function"))}function a(e){return!!t(e)&&("number"==typeof e||e.constructor===Number||r(e,"Number"))}function s(e,t,n){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,n)}function l(e,t){return e.get(u(e,t))}function c(e,t,n){return e.set(u(e,t),n),n}function u(e,t,n){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:n;throw new TypeError("Private element is not present on this object")}const d=setTimeout;var p=new WeakMap,h=new WeakMap,m=new WeakMap,f=new WeakMap,g=new WeakMap,v=new WeakMap;class b{constructor(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];s(this,p,void 0),s(this,h,void 0),s(this,m,void 0),s(this,f,!1),s(this,g,!1),s(this,v,!1),c(h,this,e),c(m,this,t),c(v,this,r),n&&this.execute()}get triggered(){return l(f,this)}get executing(){return t(l(p,this))&&-1!==l(p,this)}execute(){l(v,this)&&l(g,this)||(clearTimeout(l(p,this)),l(h,this)&&(c(p,this,setTimeout((()=>{c(p,this,-1),c(f,this,!0),l(h,this).call(this)}),l(m,this))),c(g,this,!0)))}cancel(){clearTimeout(l(p,this)),c(p,this,-1)}}var y=new WeakMap,w=new WeakMap;class x{constructor(e){s(this,y,void 0),s(this,w,new R),l(w,this).clearQueueBeforeFlush=!0,c(y,this,new b((()=>{l(w,this).flush()}),e))}add(e){return l(w,this).add(e),e}clear(e){t(e)&&l(w,this).remove(e,!0)}trigger(){l(y,this).executing||l(y,this).execute()}}var A=new WeakMap;function E(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];for(let r=e.length-1;r>=0;r--)if(e[r]==t&&(e.splice(r,1),n))return e;return e}function k(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function I(e,t,n){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,n)}function S(e,t,n){return e.set(C(e,t),n),n}function P(e,t){return e.get(C(e,t))}function C(e,t,n){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:n;throw new TypeError("Private element is not present on this object")}var T=new WeakMap,M=new WeakMap,O=new WeakMap;class R{constructor(){I(this,T,[]),I(this,M,void 0),I(this,O,void 0),k(this,"_ignore",!1),k(this,"clearQueueBeforeFlush",!1),k(this,"yieldStrategy","direct")}get queue(){return[...P(T,this)]}get hasPending(){return P(T,this).length>0}add(e){this._ignore||e&&P(T,this).push(e)}insert(e){this._ignore||e&&P(T,this).unshift(e)}remove(e){let n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t(e)&&E(P(T,this),e,!n)}flush(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];if(this._ignore)return;let r,i=P(T,this);this.clearQueueBeforeFlush&&(i=[...P(T,this)],this.clear());const o=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];return()=>{for(;r=e.shift();)r(...n)}},a=[];let s=!1;const l={then:(e,t)=>(s?e():a.push(e),l)},c=()=>{o(a)(),s=!0},u=o(i,...t),p=()=>{if(P(M,this))for(;r=P(M,this).shift();)r()};switch(this.yieldStrategy){case"next-individual":const e=()=>{i.length>0?(r=i.shift(),r(...t),clearTimeout(P(O,this)),S(O,this,d(e))):(p(),c())};clearTimeout(P(O,this)),S(O,this,d(e));break;case"next":clearTimeout(P(O,this)),S(O,this,d((()=>{u(),p(),c()})));break;default:u(),p(),c()}return l}addFinally(e){0===this.queue.length?e():(P(M,this)||S(M,this,[]),P(M,this).push(e))}clear(){clearTimeout(P(O,this)),P(T,this).length=0}clearAndIgnore(){this._ignore=!0,this.clear()}allow(){this._ignore=!1}}var _=new WeakMap,L=new WeakMap;class N extends R{constructor(){super(...arguments),I(this,_,!1),I(this,L,void 0)}get flushed(){return P(_,this)}add(e){this._ignore||(P(_,this)&&e?e(...P(L,this)):super.add(e))}flush(){if(!this._ignore&&!P(_,this)){S(_,this,!0);for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return S(L,this,t),super.flush(...t)}}reset(){S(_,this,!1),S(L,this,void 0)}clearAndReset(){this.clear(),this.reset()}}function D(e,t,n){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,n)}function j(e,t){return e.get(z(e,t))}function B(e,t,n){return e.set(z(e,t),n),n}function z(e,t,n){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:n;throw new TypeError("Private element is not present on this object")}var W=new WeakMap,F=new WeakMap,U=new WeakMap,G=new WeakMap,V=new WeakMap,H=new WeakMap;class q{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;D(this,W,[]),D(this,F,null),D(this,U,null),D(this,G,null),D(this,V,null),D(this,H,!1),this.win=e,B(V,this,t),this.initParents()}initParents(){let e=this.win,t=this;for(;null!=e&&e!=top&&(e=e.parent,t=new q(e,t),j(W,this).push(t),e!=top););}resolveFriendlyNess(){if(this.isIframe&&null==j(F,this)&&null==j(U,this))try{this.win.parent.location.href.length>0?(B(F,this,!0),B(U,this,!0)):(B(F,this,!1),B(U,this,Q(this.win)))}catch(e){B(F,this,!1),B(U,this,Q(this.win))}}get parent(){return null==this?void 0:this.parents[0]}get parents(){return j(W,this)}get isIframe(){return j(W,this).length>0}get isTop(){return this.win==top}get isFriendly(){return this.isIframe?(null==j(F,this)&&(this.resolveFriendlyNess(),j(F,this)&&B(F,this,j(F,this)&&j(W,this)[0].isFriendly)),j(F,this)):Q(this.win)}get isSelfFriendly(){return this.isIframe?(null==j(U,this)&&this.resolveFriendlyNess(),j(U,this)):Q(this.win)}get root(){if(!this.isIframe)return this;for(let e=j(W,this).length-1;e>=0;e--)if(j(W,this)[e].isFriendly)return j(W,this)[e];for(let e=j(W,this).length-1;e>=0;e--)if(j(W,this)[e].isSelfFriendly)return j(W,this)[e];return null}get rootOrSelf(){return this.root||this}get topWindow(){return this.isFriendly?top:null}get rootWindow(){return this.rootOrSelf.win}get isRoot(){return this.root==this}get frameElement(){return j(G,this)?j(G,this):!this.isIframe&&j(V,this)&&j(V,this).frameElement?(j(V,this),j(V,this).frameElement):this.win?this.win.frameElement:null}set frameElement(e){B(G,this,e)}}function Q(e){try{return e.location.href.length>0}catch(e){return!1}}const K=new q(self);n(3938),n(410);const X=()=>{};function Y(e){let t="";for(let n=0,r="";n<e.length;n++)r=e.charAt(n),t+=r>="A"&&r<="Z"?"-"+r.toLowerCase():r;return t}function J(e){let n;return()=>(t(n)||(n=e()),n)}String.fromCharCode(...new Array(52).fill(1).map(((e,t)=>(t<26?65:71)+t))),new Array(10).fill(1).map(((e,t)=>t)).join("");var Z,$="user-agent",ee="",te="function",ne="undefined",re="object",ie="string",oe="browser",ae="cpu",se="device",le="engine",ce="os",ue="result",de="name",pe="type",he="vendor",me="version",fe="architecture",ge="major",ve="model",be="console",ye="mobile",we="tablet",xe="smarttv",Ae="wearable",Ee="xr",ke="embedded",Ie="inapp",Se="brands",Pe="formFactors",Ce="fullVersionList",Te="platform",Me="platformVersion",Oe="bitness",Re="sec-ch-ua",_e=Re+"-full-version-list",Le=Re+"-arch",Ne=Re+"-"+Oe,De=Re+"-form-factors",je=Re+"-"+ye,Be=Re+"-"+ve,ze=Re+"-"+Te,We=ze+"-version",Fe=[Se,Ce,ye,ve,Te,Me,fe,Pe,Oe],Ue="Amazon",Ge="Apple",Ve="ASUS",He="BlackBerry",qe="Google",Qe="Huawei",Ke="Lenovo",Xe="Honor",Ye="LG",Je="Microsoft",Ze="Motorola",$e="Nvidia",et="OnePlus",tt="OPPO",nt="Samsung",rt="Sharp",it="Sony",ot="Xiaomi",at="Zebra",st="Chrome",lt="Chromium",ct="Chromecast",ut="Firefox",dt="Opera",pt="Facebook",ht="Sogou",mt="Mobile ",ft=" Browser",gt="Windows",vt=typeof window!==ne&&window.navigator?window.navigator:void 0,bt=vt&&vt.userAgentData?vt.userAgentData:void 0,yt=function(e){for(var t={},n=0;n<e.length;n++)t[e[n].toUpperCase()]=e[n];return t},wt=function(e,t){if(typeof e===re&&e.length>0){for(var n in e)if(kt(e[n])==kt(t))return!0;return!1}return!!At(e)&&-1!==kt(t).indexOf(kt(e))},xt=function(e,t){for(var n in e)return/^(browser|cpu|device|engine|os)$/.test(n)||!!t&&xt(e[n])},At=function(e){return typeof e===ie},Et=function(e){if(e){for(var t=[],n=Pt(/\\?\"/g,e).split(","),r=0;r<n.length;r++)if(n[r].indexOf(";")>-1){var i=Tt(n[r]).split(";v=");t[r]={brand:i[0],version:i[1]}}else t[r]=Tt(n[r]);return t}},kt=function(e){return At(e)?e.toLowerCase():e},It=function(e){return At(e)?Pt(/[^\d\.]/g,e).split(".")[0]:void 0},St=function(e){for(var t in e){var n=e[t];typeof n==re&&2==n.length?this[n[0]]=n[1]:this[n]=void 0}return this},Pt=function(e,t){return At(t)?t.replace(e,ee):t},Ct=function(e){return Pt(/\\?\"/g,e)},Tt=function(e,t){if(At(e))return e=Pt(/^\s\s*/,e),typeof t===ne?e:e.substring(0,500)},Mt=function(e,t){if(e&&t)for(var n,r,i,o,a,s,l=0;l<t.length&&!a;){var c=t[l],u=t[l+1];for(n=r=0;n<c.length&&!a&&c[n];)if(a=c[n++].exec(e))for(i=0;i<u.length;i++)s=a[++r],typeof(o=u[i])===re&&o.length>0?2===o.length?typeof o[1]==te?this[o[0]]=o[1].call(this,s):this[o[0]]=o[1]:3===o.length?typeof o[1]!==te||o[1].exec&&o[1].test?this[o[0]]=s?s.replace(o[1],o[2]):void 0:this[o[0]]=s?o[1].call(this,s,o[2]):void 0:4===o.length&&(this[o[0]]=s?o[3].call(this,s.replace(o[1],o[2])):void 0):this[o]=s||void 0;l+=2}},Ot=function(e,t){for(var n in t)if(typeof t[n]===re&&t[n].length>0){for(var r=0;r<t[n].length;r++)if(wt(t[n][r],e))return"?"===n?void 0:n}else if(wt(t[n],e))return"?"===n?void 0:n;return t.hasOwnProperty("*")?t["*"]:e},Rt={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},_t={embedded:"Automotive",mobile:"Mobile",tablet:["Tablet","EInk"],smarttv:"TV",wearable:"Watch",xr:["VR","XR"],"?":["Desktop","Unknown"],"*":void 0},Lt={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[me,[de,mt+"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[me,[de,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[de,me],[/opios[\/ ]+([\w\.]+)/i],[me,[de,dt+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[me,[de,dt+" GX"]],[/\bopr\/([\w\.]+)/i],[me,[de,dt]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[me,[de,"Baidu"]],[/\b(?:mxbrowser|mxios|myie2)\/?([-\w\.]*)\b/i],[me,[de,"Maxthon"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim(?:browser|boat|jet))[\/ ]?([\d\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon|otter|dooble|(?:lg |qute)browser)\/([-\w\.]+)/i,/(heytap|ovi|115|surf)browser\/([\d\.]+)/i,/(ecosia|weibo)(?:__| \w+@)([\d\.]+)/i],[de,me],[/quark(?:pc)?\/([-\w\.]+)/i],[me,[de,"Quark"]],[/\bddg\/([\w\.]+)/i],[me,[de,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[me,[de,"UCBrowser"]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[me,[de,"WeChat"]],[/konqueror\/([\w\.]+)/i],[me,[de,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[me,[de,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[me,[de,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[me,[de,"Smart "+Ke+ft]],[/(avast|avg)\/([\w\.]+)/i],[[de,/(.+)/,"$1 Secure"+ft],me],[/\bfocus\/([\w\.]+)/i],[me,[de,ut+" Focus"]],[/\bopt\/([\w\.]+)/i],[me,[de,dt+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[me,[de,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[me,[de,"Dolphin"]],[/coast\/([\w\.]+)/i],[me,[de,dt+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[me,[de,"MIUI"+ft]],[/fxios\/([\w\.-]+)/i],[me,[de,mt+ut]],[/\bqihoobrowser\/?([\w\.]*)/i],[me,[de,"360"]],[/\b(qq)\/([\w\.]+)/i],[[de,/(.+)/,"$1Browser"],me],[/(oculus|sailfish|huawei|vivo|pico)browser\/([\w\.]+)/i],[[de,/(.+)/,"$1"+ft],me],[/samsungbrowser\/([\w\.]+)/i],[me,[de,nt+" Internet"]],[/metasr[\/ ]?([\d\.]+)/i],[me,[de,ht+" Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[de,ht+" Mobile"],me],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345(?=browser|chrome|explorer))\w*[\/ ]?v?([\w\.]+)/i],[de,me],[/(lbbrowser|rekonq)/i],[de],[/ome\/([\w\.]+) \w* ?(iron) saf/i,/ome\/([\w\.]+).+qihu (360)[es]e/i],[me,de],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[de,pt],me,[pe,Ie]],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/(daum)apps[\/ ]([\w\.]+)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(instagram|snapchat)[\/ ]([-\w\.]+)/i],[de,me,[pe,Ie]],[/\bgsa\/([\w\.]+) .*safari\//i],[me,[de,"GSA"],[pe,Ie]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[me,[de,"TikTok"],[pe,Ie]],[/\[(linkedin)app\]/i],[de,[pe,Ie]],[/(chromium)[\/ ]([-\w\.]+)/i],[de,me],[/headlesschrome(?:\/([\w\.]+)| )/i],[me,[de,st+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[de,st+" WebView"],me],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[me,[de,"Android"+ft]],[/chrome\/([\w\.]+) mobile/i],[me,[de,mt+"Chrome"]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[de,me],[/version\/([\w\.\,]+) .*mobile(?:\/\w+ | ?)safari/i],[me,[de,mt+"Safari"]],[/iphone .*mobile(?:\/\w+ | ?)safari/i],[[de,mt+"Safari"]],[/version\/([\w\.\,]+) .*(safari)/i],[me,de],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[de,[me,"1"]],[/(webkit|khtml)\/([\w\.]+)/i],[de,me],[/(?:mobile|tablet);.*(firefox)\/([\w\.-]+)/i],[[de,mt+ut],me],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[de,"Netscape"],me],[/(wolvic|librewolf)\/([\w\.]+)/i],[de,me],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[me,[de,ut+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(amaya|dillo|doris|icab|ladybird|lynx|mosaic|netsurf|obigo|polaris|w3m|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/\b(links) \(([\w\.]+)/i],[de,[me,/_/g,"."]],[/(cobalt)\/([\w\.]+)/i],[de,[me,/[^\d\.]+./,ee]]],cpu:[[/\b((amd|x|x86[-_]?|wow|win)64)\b/i],[[fe,"amd64"]],[/(ia32(?=;))/i,/\b((i[346]|x)86)(pc)?\b/i],[[fe,"ia32"]],[/\b(aarch64|arm(v?[89]e?l?|_?64))\b/i],[[fe,"arm64"]],[/\b(arm(v[67])?ht?n?[fl]p?)\b/i],[[fe,"armhf"]],[/( (ce|mobile); ppc;|\/[\w\.]+arm\b)/i],[[fe,"arm"]],[/((ppc|powerpc)(64)?)( mac|;|\))/i],[[fe,/ower/,ee,kt]],[/ sun4\w[;\)]/i],[[fe,"sparc"]],[/\b(avr32|ia64(?=;)|68k(?=\))|\barm(?=v([1-7]|[5-7]1)l?|;|eabi)|(irix|mips|sparc)(64)?\b|pa-risc)/i],[[fe,kt]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[ve,[he,nt],[pe,we]],[/\b((?:s[cgp]h|gt|sm)-(?![lr])\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]((?!sm-[lr])[-\w]+)/i,/sec-(sgh\w+)/i],[ve,[he,nt],[pe,ye]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[ve,[he,Ge],[pe,ye]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[ve,[he,Ge],[pe,we]],[/(macintosh);/i],[ve,[he,Ge]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[ve,[he,rt],[pe,ye]],[/\b((?:brt|eln|hey2?|gdi|jdn)-a?[lnw]09|(?:ag[rm]3?|jdn2|kob2)-a?[lw]0[09]hn)(?: bui|\)|;)/i],[ve,[he,Xe],[pe,we]],[/honor([-\w ]+)[;\)]/i],[ve,[he,Xe],[pe,ye]],[/\b((?:ag[rs][2356]?k?|bah[234]?|bg[2o]|bt[kv]|cmr|cpn|db[ry]2?|jdn2|got|kob2?k?|mon|pce|scm|sht?|[tw]gr|vrd)-[ad]?[lw][0125][09]b?|605hw|bg2-u03|(?:gem|fdr|m2|ple|t1)-[7a]0[1-4][lu]|t1-a2[13][lw]|mediapad[\w\. ]*(?= bui|\)))\b(?!.+d\/s)/i],[ve,[he,Qe],[pe,we]],[/(?:huawei)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[ve,[he,Qe],[pe,ye]],[/oid[^\)]+; (2[\dbc]{4}(182|283|rp\w{2})[cgl]|m2105k81a?c)(?: bui|\))/i,/\b((?:red)?mi[-_ ]?pad[\w- ]*)(?: bui|\))/i],[[ve,/_/g," "],[he,ot],[pe,we]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\))/i,/ ([\w ]+) miui\/v?\d/i],[[ve,/_/g," "],[he,ot],[pe,ye]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[ve,[he,tt],[pe,ye]],[/\b(opd2(\d{3}a?))(?: bui|\))/i],[ve,[he,Ot,{OnePlus:["304","403","203"],"*":tt}],[pe,we]],[/(vivo (5r?|6|8l?|go|one|s|x[il]?[2-4]?)[\w\+ ]*)(?: bui|\))/i],[ve,[he,"BLU"],[pe,ye]],[/; vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[ve,[he,"Vivo"],[pe,ye]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[ve,[he,"Realme"],[pe,ye]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto(?! 360)[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[ve,[he,Ze],[pe,ye]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[ve,[he,Ze],[pe,we]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[ve,[he,Ye],[pe,we]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+(?!.*(?:browser|netcast|android tv|watch))(\w+)/i,/\blg-?([\d\w]+) bui/i],[ve,[he,Ye],[pe,ye]],[/(ideatab[-\w ]+|602lv|d-42a|a101lv|a2109a|a3500-hv|s[56]000|pb-6505[my]|tb-?x?\d{3,4}(?:f[cu]|xu|[av])|yt\d?-[jx]?\d+[lfmx])( bui|;|\)|\/)/i,/lenovo ?(b[68]0[08]0-?[hf]?|tab(?:[\w- ]+?)|tb[\w-]{6,7})( bui|;|\)|\/)/i],[ve,[he,Ke],[pe,we]],[/(nokia) (t[12][01])/i],[he,ve,[pe,we]],[/(?:maemo|nokia).*(n900|lumia \d+|rm-\d+)/i,/nokia[-_ ]?(([-\w\. ]*))/i],[[ve,/_/g," "],[pe,ye],[he,"Nokia"]],[/(pixel (c|tablet))\b/i],[ve,[he,qe],[pe,we]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[ve,[he,qe],[pe,ye]],[/droid.+; (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[ve,[he,it],[pe,ye]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[ve,"Xperia Tablet"],[he,it],[pe,we]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[ve,[he,et],[pe,ye]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo(?!bc)\w\w)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[ve,[he,Ue],[pe,we]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[ve,/(.+)/g,"Fire Phone $1"],[he,Ue],[pe,ye]],[/(playbook);[-\w\),; ]+(rim)/i],[ve,he,[pe,we]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[ve,[he,He],[pe,ye]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[ve,[he,Ve],[pe,we]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[ve,[he,Ve],[pe,ye]],[/(nexus 9)/i],[ve,[he,"HTC"],[pe,we]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[he,[ve,/_/g," "],[pe,ye]],[/tcl (xess p17aa)/i,/droid [\w\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])(_\w(\w|\w\w))?(\)| bui)/i],[ve,[he,"TCL"],[pe,we]],[/droid [\w\.]+; (418(?:7d|8v)|5087z|5102l|61(?:02[dh]|25[adfh]|27[ai]|56[dh]|59k|65[ah])|a509dl|t(?:43(?:0w|1[adepqu])|50(?:6d|7[adju])|6(?:09dl|10k|12b|71[efho]|76[hjk])|7(?:66[ahju]|67[hw]|7[045][bh]|71[hk]|73o|76[ho]|79w|81[hks]?|82h|90[bhsy]|99b)|810[hs]))(_\w(\w|\w\w))?(\)| bui)/i],[ve,[he,"TCL"],[pe,ye]],[/(itel) ((\w+))/i],[[he,kt],ve,[pe,Ot,{tablet:["p10001l","w7001"],"*":"mobile"}]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[ve,[he,"Acer"],[pe,we]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[ve,[he,"Meizu"],[pe,ye]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[ve,[he,"Ulefone"],[pe,ye]],[/; (energy ?\w+)(?: bui|\))/i,/; energizer ([\w ]+)(?: bui|\))/i],[ve,[he,"Energizer"],[pe,ye]],[/; cat (b35);/i,/; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\))/i],[ve,[he,"Cat"],[pe,ye]],[/((?:new )?andromax[\w- ]+)(?: bui|\))/i],[ve,[he,"Smartfren"],[pe,ye]],[/droid.+; (a(?:015|06[35]|142p?))/i],[ve,[he,"Nothing"],[pe,ye]],[/; (x67 5g|tikeasy \w+|ac[1789]\d\w+)( b|\))/i,/archos ?(5|gamepad2?|([\w ]*[t1789]|hello) ?\d+[\w ]*)( b|\))/i],[ve,[he,"Archos"],[pe,we]],[/archos ([\w ]+)( b|\))/i,/; (ac[3-6]\d\w{2,8})( b|\))/i],[ve,[he,"Archos"],[pe,ye]],[/(imo) (tab \w+)/i,/(infinix) (x1101b?)/i],[he,ve,[pe,we]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus(?! zenw)|dell|jolla|meizu|motorola|polytron|infinix|tecno|micromax|advan)[-_ ]?([-\w]*)/i,/; (blu|hmd|imo|tcl)[_ ]([\w\+ ]+?)(?: bui|\)|; r)/i,/(hp) ([\w ]+\w)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w ]+?)(?: bui|\)|\/)/i,/(oppo) ?([\w ]+) bui/i],[he,ve,[pe,ye]],[/(kobo)\s(ereader|touch)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i],[he,ve,[pe,we]],[/(surface duo)/i],[ve,[he,Je],[pe,we]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[ve,[he,"Fairphone"],[pe,ye]],[/((?:tegranote|shield t(?!.+d tv))[\w- ]*?)(?: b|\))/i],[ve,[he,$e],[pe,we]],[/(sprint) (\w+)/i],[he,ve,[pe,ye]],[/(kin\.[onetw]{3})/i],[[ve,/\./g," "],[he,Je],[pe,ye]],[/droid.+; ([c6]+|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[ve,[he,at],[pe,we]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[ve,[he,at],[pe,ye]],[/smart-tv.+(samsung)/i],[he,[pe,xe]],[/hbbtv.+maple;(\d+)/i],[[ve,/^/,"SmartTV"],[he,nt],[pe,xe]],[/tcast.+(lg)e?. ([-\w]+)/i],[he,ve,[pe,xe]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[he,Ye],[pe,xe]],[/(apple) ?tv/i],[he,[ve,Ge+" TV"],[pe,xe]],[/crkey.*devicetype\/chromecast/i],[[ve,ct+" Third Generation"],[he,qe],[pe,xe]],[/crkey.*devicetype\/([^/]*)/i],[[ve,/^/,"Chromecast "],[he,qe],[pe,xe]],[/fuchsia.*crkey/i],[[ve,ct+" Nest Hub"],[he,qe],[pe,xe]],[/crkey/i],[[ve,ct],[he,qe],[pe,xe]],[/(portaltv)/i],[ve,[he,pt],[pe,xe]],[/droid.+aft(\w+)( bui|\))/i],[ve,[he,Ue],[pe,xe]],[/(shield \w+ tv)/i],[ve,[he,$e],[pe,xe]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[ve,[he,rt],[pe,xe]],[/(bravia[\w ]+)( bui|\))/i],[ve,[he,it],[pe,xe]],[/(mi(tv|box)-?\w+) bui/i],[ve,[he,ot],[pe,xe]],[/Hbbtv.*(technisat) (.*);/i],[he,ve,[pe,xe]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[he,Tt],[ve,Tt],[pe,xe]],[/droid.+; ([\w- ]+) (?:android tv|smart[- ]?tv)/i],[ve,[pe,xe]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[pe,xe]],[/(ouya)/i,/(nintendo) (\w+)/i],[he,ve,[pe,be]],[/droid.+; (shield)( bui|\))/i],[ve,[he,$e],[pe,be]],[/(playstation \w+)/i],[ve,[he,it],[pe,be]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[ve,[he,Je],[pe,be]],[/\b(sm-[lr]\d\d[0156][fnuw]?s?|gear live)\b/i],[ve,[he,nt],[pe,Ae]],[/((pebble))app/i,/(asus|google|lg|oppo) ((pixel |zen)?watch[\w ]*)( bui|\))/i],[he,ve,[pe,Ae]],[/(ow(?:19|20)?we?[1-3]{1,3})/i],[ve,[he,tt],[pe,Ae]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[ve,[he,Ge],[pe,Ae]],[/(opwwe\d{3})/i],[ve,[he,et],[pe,Ae]],[/(moto 360)/i],[ve,[he,Ze],[pe,Ae]],[/(smartwatch 3)/i],[ve,[he,it],[pe,Ae]],[/(g watch r)/i],[ve,[he,Ye],[pe,Ae]],[/droid.+; (wt63?0{2,3})\)/i],[ve,[he,at],[pe,Ae]],[/droid.+; (glass) \d/i],[ve,[he,qe],[pe,Ee]],[/(pico) (4|neo3(?: link|pro)?)/i],[he,ve,[pe,Ee]],[/(quest( \d| pro)?s?).+vr/i],[ve,[he,pt],[pe,Ee]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[he,[pe,ke]],[/(aeobc)\b/i],[ve,[he,Ue],[pe,ke]],[/(homepod).+mac os/i],[ve,[he,Ge],[pe,ke]],[/windows iot/i],[[pe,ke]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+?(mobile|vr|\d) safari/i],[ve,[pe,Ot,{mobile:"Mobile",xr:"VR","*":we}]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[pe,we]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[pe,ye]],[/droid .+?; ([\w\. -]+)( bui|\))/i],[ve,[he,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[me,[de,"EdgeHTML"]],[/(arkweb)\/([\w\.]+)/i],[de,me],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[me,[de,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[de,me],[/ladybird\//i],[[de,"LibWeb"]],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[me,de]],os:[[/microsoft (windows) (vista|xp)/i],[de,me],[/(windows (?:phone(?: os)?|mobile|iot))[\/ ]?([\d\.\w ]*)/i],[de,[me,Ot,Rt]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[me,Ot,Rt],[de,gt]],[/[adehimnop]{4,7}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[me,/_/g,"."],[de,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[de,"macOS"],[me,/_/g,"."]],[/android ([\d\.]+).*crkey/i],[me,[de,ct+" Android"]],[/fuchsia.*crkey\/([\d\.]+)/i],[me,[de,ct+" Fuchsia"]],[/crkey\/([\d\.]+).*devicetype\/smartspeaker/i],[me,[de,ct+" SmartSpeaker"]],[/linux.*crkey\/([\d\.]+)/i],[me,[de,ct+" Linux"]],[/crkey\/([\d\.]+)/i],[me,[de,ct]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[me,de],[/(ubuntu) ([\w\.]+) like android/i],[[de,/(.+)/,"$1 Touch"],me],[/(android|bada|blackberry|kaios|maemo|meego|openharmony|qnx|rim tablet os|sailfish|series40|symbian|tizen|webos)\w*[-\/\.; ]?([\d\.]*)/i],[de,me],[/\(bb(10);/i],[me,[de,He]],[/(?:symbian ?os|symbos|s60(?=;)|series ?60)[-\/ ]?([\w\.]*)/i],[me,[de,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[me,[de,ut+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[me,[de,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[me,[de,"watchOS"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[de,"Chrome OS"],me],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) (\w+)/i,/(xbox); +xbox ([^\);]+)/i,/(pico) .+os([\w\.]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux)(?: arm\w*| x86\w*| ?)([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[de,me],[/(sunos) ?([\w\.\d]*)/i],[[de,"Solaris"],me],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[de,me]]},Nt=(St.call((Z={init:{},isIgnore:{},isIgnoreRgx:{},toString:{}}).init,[[oe,[de,me,ge,pe]],[ae,[fe]],[se,[pe,ve,he]],[le,[de,me]],[ce,[de,me]]]),St.call(Z.isIgnore,[[oe,[me,ge]],[le,[me]],[ce,[me]]]),St.call(Z.isIgnoreRgx,[[oe,/ ?browser$/i],[ce,/ ?os$/i]]),St.call(Z.toString,[[oe,[de,me]],[ae,[fe]],[se,[he,ve]],[le,[de,me]],[ce,[de,me]]]),Z),Dt=function(e,t){var n=Nt.init[t],r=Nt.isIgnore[t]||0,i=Nt.isIgnoreRgx[t]||0,o=Nt.toString[t]||0;function a(){St.call(this,n)}return a.prototype.getItem=function(){return e},a.prototype.withClientHints=function(){return bt?bt.getHighEntropyValues(Fe).then((function(t){return e.setCH(new jt(t,!1)).parseCH().get()})):e.parseCH().get()},a.prototype.withFeatureCheck=function(){return e.detectFeature().get()},t!=ue&&(a.prototype.is=function(e){var t=!1;for(var n in this)if(this.hasOwnProperty(n)&&!wt(r,n)&&kt(i?Pt(i,this[n]):this[n])==kt(i?Pt(i,e):e)){if(t=!0,e!=ne)break}else if(e==ne&&t){t=!t;break}return t},a.prototype.toString=function(){var e=ee;for(var t in o)typeof this[o[t]]!==ne&&(e+=(e?" ":ee)+this[o[t]]);return e||ne}),bt||(a.prototype.then=function(e){var t=this,n=function(){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e])};n.prototype={is:a.prototype.is,toString:a.prototype.toString};var r=new n;return e(r),r}),new a};function jt(e,t){if(e=e||{},St.call(this,Fe),t)St.call(this,[[Se,Et(e[Re])],[Ce,Et(e[_e])],[ye,/\?1/.test(e[je])],[ve,Ct(e[Be])],[Te,Ct(e[ze])],[Me,Ct(e[We])],[fe,Ct(e[Le])],[Pe,Et(e[De])],[Oe,Ct(e[Ne])]]);else for(var n in e)this.hasOwnProperty(n)&&typeof e[n]!==ne&&(this[n]=e[n])}function Bt(e,t,n,r){return this.get=function(e){return e?this.data.hasOwnProperty(e)?this.data[e]:void 0:this.data},this.set=function(e,t){return this.data[e]=t,this},this.setCH=function(e){return this.uaCH=e,this},this.detectFeature=function(){if(vt&&vt.userAgent==this.ua)switch(this.itemType){case oe:vt.brave&&typeof vt.brave.isBrave==te&&this.set(de,"Brave");break;case se:!this.get(pe)&&bt&&bt[ye]&&this.set(pe,ye),"Macintosh"==this.get(ve)&&vt&&typeof vt.standalone!==ne&&vt.maxTouchPoints&&vt.maxTouchPoints>2&&this.set(ve,"iPad").set(pe,we);break;case ce:!this.get(de)&&bt&&bt[Te]&&this.set(de,bt[Te]);break;case ue:var e=this.data,t=function(t){return e[t].getItem().detectFeature().get()};this.set(oe,t(oe)).set(ae,t(ae)).set(se,t(se)).set(le,t(le)).set(ce,t(ce))}return this},this.parseUA=function(){return this.itemType!=ue&&Mt.call(this.data,this.ua,this.rgxMap),this.itemType==oe&&this.set(ge,It(this.get(me))),this},this.parseCH=function(){var e=this.uaCH,t=this.rgxMap;switch(this.itemType){case oe:case le:var n,r=e[Ce]||e[Se];if(r)for(var i in r){var o=r[i].brand||r[i],a=r[i].version;this.itemType!=oe||/not.a.brand/i.test(o)||n&&(!/chrom/i.test(n)||o==lt)||(o=Ot(o,{Chrome:"Google Chrome",Edge:"Microsoft Edge","Chrome WebView":"Android WebView","Chrome Headless":"HeadlessChrome","Huawei Browser":"HuaweiBrowser","MIUI Browser":"Miui Browser","Opera Mobi":"OperaMobile",Yandex:"YaBrowser"}),this.set(de,o).set(me,a).set(ge,It(a)),n=o),this.itemType==le&&o==lt&&this.set(me,a)}break;case ae:var s=e[fe];s&&(s&&"64"==e[Oe]&&(s+="64"),Mt.call(this.data,s+";",t));break;case se:if(e[ye]&&this.set(pe,ye),e[ve]&&(this.set(ve,e[ve]),!this.get(pe)||!this.get(he))){var l={};Mt.call(l,"droid 9; "+e[ve]+")",t),!this.get(pe)&&l.type&&this.set(pe,l.type),!this.get(he)&&l.vendor&&this.set(he,l.vendor)}if(e[Pe]){var c;if("string"!=typeof e[Pe])for(var u=0;!c&&u<e[Pe].length;)c=Ot(e[Pe][u++],_t);else c=Ot(e[Pe],_t);this.set(pe,c)}break;case ce:var d=e[Te];if(d){var p=e[Me];d==gt&&(p=parseInt(It(p),10)>=13?"11":"10"),this.set(de,d).set(me,p)}this.get(de)==gt&&"Xbox"==e[ve]&&this.set(de,"Xbox").set(me,void 0);break;case ue:var h=this.data,m=function(t){return h[t].getItem().setCH(e).parseCH().get()};this.set(oe,m(oe)).set(ae,m(ae)).set(se,m(se)).set(le,m(le)).set(ce,m(ce))}return this},St.call(this,[["itemType",e],["ua",t],["uaCH",r],["rgxMap",n],["data",Dt(this,e)]]),this}function zt(e,t,n){if(typeof e===re?(xt(e,!0)?(typeof t===re&&(n=t),t=e):(n=e,t=void 0),e=void 0):typeof e!==ie||xt(t,!0)||(n=t,t=void 0),n&&typeof n.append===te){var r={};n.forEach((function(e,t){r[t]=e})),n=r}if(!(this instanceof zt))return new zt(e,t,n).getResult();var i=typeof e===ie?e:n&&n[$]?n[$]:vt&&vt.userAgent?vt.userAgent:ee,o=new jt(n,!0),a=t?function(e,t){var n={},r=t;if(!xt(t))for(var i in r={},t)for(var o in t[i])r[o]=t[i][o].concat(r[o]?r[o]:[]);for(var a in e)n[a]=r[a]&&r[a].length%2==0?r[a].concat(e[a]):e[a];return n}(Lt,t):Lt,s=function(e){return e==ue?function(){return new Bt(e,i,a,o).set("ua",i).set(oe,this.getBrowser()).set(ae,this.getCPU()).set(se,this.getDevice()).set(le,this.getEngine()).set(ce,this.getOS()).get()}:function(){return new Bt(e,i,a[e],o).parseUA().get()}};return St.call(this,[["getBrowser",s(oe)],["getCPU",s(ae)],["getDevice",s(se)],["getEngine",s(le)],["getOS",s(ce)],["getResult",s(ue)],["getUA",function(){return i}],["setUA",function(e){return At(e)&&(i=e.length>500?Tt(e,500):e),this}]]).setUA(i),this}function Wt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ft(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Wt(Object(n),!0).forEach((function(t){Ut(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Wt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ut(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Gt(e,t,n){Vt(e,t),t.set(e,n)}function Vt(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function Ht(e,t,n){return n(Kt(e,t))}function qt(e,t){return e.get(Kt(e,t))}function Qt(e,t,n){return e.set(Kt(e,t),n),n}function Kt(e,t,n){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:n;throw new TypeError("Private element is not present on this object")}zt.VERSION="2.0.3",zt.BROWSER=yt([de,me,ge,pe]),zt.CPU=yt([fe]),zt.DEVICE=yt([ve,he,pe,be,ye,xe,we,Ae,ke]),zt.ENGINE=zt.OS=yt([de,me]);const Xt=["script","iframe","style","img","video","br","hr"],Yt=document.currentScript,Jt=new class{constructor(){s(this,A,{})}add(e,t){return l(A,this)[t]||(l(A,this)[t]=new x(t)),l(A,this)[t].add(e),l(A,this)[t].trigger(),e}clear(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(t(e))if(t(n))l(A,this)[n].clear(e);else for(const t in l(A,this))this.clear(e,+t)}};var Zt=new WeakMap,$t=new WeakMap,en=new WeakMap,tn=new WeakMap,nn=new WeakMap,rn=new WeakMap,on=new WeakMap,an=new WeakMap,sn=new WeakMap,ln=new WeakMap,cn=new WeakMap,un=new WeakMap,dn=new WeakMap,pn=new WeakSet;class hn{constructor(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=K.rootWindow)||void 0===t?void 0:t.document,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,o=!(arguments.length>4&&void 0!==arguments[4])||arguments[4];var a;Vt(this,a=pn),a.add(this),Gt(this,Zt,void 0),Gt(this,$t,void 0),Gt(this,en,void 0),Gt(this,tn,new N),Gt(this,nn,new N),Gt(this,rn,void 0),Gt(this,on,!1),Gt(this,an,!1),Gt(this,sn,!1),Gt(this,ln,0),Gt(this,cn,!1),Gt(this,un,-1),Ut(this,"autoInvalidate",!0),Gt(this,dn,{body:!0,dom:!0,window:!0}),this.spec=e,Qt($t,this,yn(n)),null!=e&&e.document&&Qt($t,this,e.document),null!=e&&e.parent&&Qt(en,this,e.parent),Qt(an,this,r),Qt(ln,this,i),this.autoInvalidate=o}get doc(){return qt($t,this)}get running(){return qt(on,this)}get canceled(){return qt(sn,this)}get existingNode(){if(Sn(qt(Zt,this)))return xn(qt(Zt,this)),qt(Zt,this)}get isValid(){return!!this.existingNode}resolve(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;const r={then:(e,t)=>(qt(tn,this).add(e),qt(nn,this).add(t),r)};if(qt(sn,this))return r;if(Sn(qt(Zt,this))||qt(on,this)||qt(cn,this))return qt(cn,this)&&this.check(),r;(n||qt(ln,this))&&(clearTimeout(qt(un,this)),Qt(un,this,setTimeout(Kt(pn,this,vn).bind(this),n||qt(ln,this))));const i=Ht(pn,this,mn);return e&&t(i)&&(Sn(qt(Zt,this))||(qt(tn,this).reset(),qt(nn,this).reset()),Kt(pn,this,fn).call(this,i)),r}resolveNew(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this.cancel(),Kt(pn,this,bn).call(this),qt(tn,this).clearAndReset(),qt(nn,this).clearAndReset(),this.resolve(e,t)}resolveWithTimeout(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return this.resolve(t,e)}check(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Ht(pn,this,mn);if(qt(sn,this))return;const t=this.existingNode;if(t)return t;const n=null==e?void 0:e();return n&&Kt(pn,this,gn).call(this,n),n}clone(){return new hn(this.spec,qt($t,this),qt(an,this),qt(ln,this),this.autoInvalidate)}equals(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(!e)return!1;const n=new hn(e),r=this.check(),a=n.check();return!((!r||r!==a)&&(!t||!r||(s=r.id,i(s)&&0===s.trim().length)||r.id!==(null==a?void 0:a.id)||r.ownerDocument!==a.ownerDocument&&r.isConnected&&a.isConnected)&&(qt($t,this)!==n.doc||(!o(this.spec.func)||this.spec.func!==e.func)&&(!this.spec.head||!e.head)&&(!this.spec.body||!e.body)&&(!this.spec.currentScript||!e.currentScript)&&(!this.spec.id||this.spec.id!==e.id)&&(!this.spec.query||this.spec.query!==e.query)));var s}cancel(){qt(an,this)?(clearTimeout(qt(rn,this)),Qt(rn,this,-1)):(Jt.clear(qt(rn,this)),Qt(rn,this,null)),Qt(sn,this,!0)}reset(){}}function mn(e){if(!e.spec)throw new Error("No Element Spec");let n=null;return o(e.spec.func)?n=e.spec.func:e.spec.node&&(e.spec.node instanceof Element||e.spec.node instanceof wn(e.doc).Element||e.spec.node instanceof xn(e.spec.node).Element)&&(n=()=>e.spec.node),e.spec.head?n=()=>{if(qt($t,e).head)return qt($t,e).head;const t=qt($t,e).getElementsByTagName("head");return t.length>0?t[0]:void 0}:e.spec.body?n=(()=>{let n;return function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:K.rootWindow;const r=n.document;if(r&&r.body)e(r.body);else{var i;if(!kn.has(n)){const e=new N;kn.set(n,e);let i=!1;const o=()=>{i||r&&(t(n.requestAnimationFrame)?r.body?(e.flush(r.body),i=!0):n.requestAnimationFrame(o):r.body?(e.flush(r.body),i=!0):n.setTimeout(o,0))};o(),In(o,n)}null===(i=kn.get(n))||void 0===i||i.add(e)}}((()=>{n=qt($t,e).body}),wn(qt($t,e))),()=>n||qt($t,e).body})():e.spec.loadedCurrentScript?n=()=>Yt:e.spec.currentScript?n=()=>qt($t,e).currentScript:e.spec.id?n=()=>qt($t,e).getElementById(e.spec.id):e.spec.query&&(n=()=>qt(en,e)&&qt(en,e).querySelector?qt(en,e).querySelector(e.spec.query):qt($t,e).querySelector(e.spec.query)),n}function fn(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:15;if(qt(on,this))return;Qt(on,this,!0);const n=()=>{this.check(e)||r()},r=()=>{qt(an,this)?(clearTimeout(qt(rn,this)),Qt(rn,this,setTimeout(n,t))):(Jt.clear(n),Qt(rn,this,Jt.add(n,t)))};qt(dn,this)&&qt(dn,this).dom&&In((()=>{qt(sn,this)||qt(on,this)&&!qt(Zt,this)&&n()}),wn(qt($t,this))),n()}function gn(e){e&&(xn(e),clearTimeout(qt(un,this)),Qt(un,this,-1),qt(an,this)?(clearTimeout(qt(rn,this)),Qt(rn,this,-1)):(Jt.clear(qt(rn,this)),Qt(rn,this,null)),Qt(on,this,!1),this.autoInvalidate&&qt(Zt,this)!==e&&qt(tn,this).reset(),Qt(Zt,this,e),qt(tn,this).flush(qt(Zt,this)))}function vn(){Qt(cn,this,!0),qt(tn,this).flush(null)}function bn(){Qt(sn,this,!1),Qt(Zt,this,null),Qt(cn,this,!1),Qt(on,this,!1)}function yn(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null===(e=K.topWindow)||void 0===e?void 0:e.document;return t||(t=self.document),t}function wn(e){if(e)return e.defaultView||e.parentWindow}function xn(e){return wn(e.ownerDocument)}function An(e){var t,n,r,i,o;let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=K.topWindow)||void 0===t?void 0:t.document,s=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];a=yn(a),s&&null!==(n=a)&&void 0!==n&&n.body?null==e||e(a.body):null!==(r=a)&&void 0!==r&&r.head?null==e||e(a.head):null!==(i=a)&&void 0!==i&&i.body?null==e||e(a.body):null!==(o=a)&&void 0!==o&&o.documentElement?null==e||e(a.documentElement):d((()=>An(e,a)))}const En=e=>{if(e&&e.ownerDocument){const t=wn(e.ownerDocument);kn.has(t)&&(kn.get(t).flush(e),kn.delete(t))}},kn=new WeakMap;function In(e){const t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:K.rootWindow).document;if(t)if("loading"!==t.readyState)e(t.body);else{const n=r=>{En(t.body),e(t.body),t.removeEventListener("DOMContentLoaded",n)};t.addEventListener("DOMContentLoaded",n)}}function Sn(e){return!(!e||!e.isConnected||null==e.ownerDocument)}const Pn=[];function Cn(e){var t;let n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null===(t=K.rootWindow)||void 0===t?void 0:t.document,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{head:!0},o=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{};return r=yn(r),o&&function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=K.rootWindow)||void 0===t?void 0:t.document,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"=";return n=yn(n),!!n&&null!=n.querySelector("script[src".concat(r,'"').concat(e,'"]'))}(e,r)?{then:(t,n)=>{Pn.includes(e)?null==n||n():null==t||t()}}:new Promise(((t,o)=>{const s=e=>{e.onerror=null,e.onload=null};new hn(i,r).resolve().then((r=>{const i=function(e){let t=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Tn;if(!e)throw new Error("no parent node provided to create the element");return n(e,function(e){return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:K.rootWindow.document).createElement(e)}(t,e.ownerDocument))}(e,"script",arguments.length>1&&void 0!==arguments[1]?arguments[1]:Tn);return t.type="text/javascript",t}(r,Mn);n&&(i.async=!0),i.onload=()=>{E(Pn,e),t(),s(i)},i.onerror=()=>{Pn.includes(e)||Pn.push(e),o(),s(i)},function(e,t){if(e&&t)for(let n in t)t.hasOwnProperty(n)&&e.setAttribute(n,t[n])}(i,a),i.src=e,Tn(r,i)}))}))}function Tn(e,t){return function(e){return!(!e||e&&(1==e.nodeType&&Xt.includes(e.nodeName.toLowerCase())||1!=e.nodeType))}(e)?e.appendChild(t):e.parentNode.insertBefore(t,e.nextSibling)}function Mn(e,t){return t}function On(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";if(!e||!Sn(e)||!e.style)return;const r=wn(e.ownerDocument);try{if(document.defaultView&&r&&r.getComputedStyle)return r.getComputedStyle(e,n)[t];if(e.currentStyle)return e.currentStyle[t]}catch(e){}return e.style[t]}function Rn(e){return!!(e&&Sn(e)&&e.style)&&("inline"===e.style.display||"inline"===On(e,"display"))}function _n(e){return!(!e||!e.style)&&"IFRAME"===e.nodeName.toLocaleUpperCase()}function Ln(e){return!(!e||!e.style)&&"IMG"===e.nodeName.toLocaleUpperCase()}function Nn(e){return 1===(null==e?void 0:e.nodeType)&&e instanceof HTMLElement}function Dn(e){if(e&&Sn(e)&&e.style){const t=J((()=>"none"!==e.style.display&&!("none"===On(e,"display")||function(e,t){do{var n;if(!e)return!1;if(t&&t(e))return!0}while((e=e.parentNode)!=(null===(n=e)||void 0===n?void 0:n.ownerDocument))}(e,(e=>{var t;return"none"===(null==e||null===(t=e.style)||void 0===t?void 0:t.display)||"none"===On(e,"display")}))))),n=J((()=>function(e){return function(e,t){return!(!(e&&Sn(e)&&e.style)||e.style.position!==t&&On(e,"position")!==t)}(e,"fixed")}(e)));return e.offsetParent||n()?!(!n()||t()):!!(e.offsetParent||e.ownerDocument.body!==e&&e.ownerDocument.documentElement!==e||t())&&!t()}return!0}function jn(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return function*(){let n=!1,r=!1;for(let t=0;t<e.childNodes.length;t++){const i=e.childNodes[t];Nn(i)&&(r=!0,Dn(i)||Rn(i)&&!_n(i)&&!Ln(i)||(n=!0,yield i))}if(r&&!n&&t>0)for(let n=0;n<e.childNodes.length;n++){const r=e.childNodes[n];Nn(r)&&(yield*jn(r,t--))}}()}function Bn(e){if(Rn(e)){const t=e.style.display;return e.style.display="inline-block",()=>{t?e.style.display=t:e.style.removeProperty("display")}}return X}function zn(e){if(null!=e&&e.getBoundingClientRect){let t=Bn(e);const{x:n,y:r}=e.getBoundingClientRect();return t(),Ft({position:"viewport"},{x:n,y:r})}}function Wn(e){if(null!=e&&e.getBoundingClientRect&&Rn(e)){let t=Number.MAX_VALUE,n=!1;for(const r of jn(e)){n=!0;const e=r.getBoundingClientRect();t=Math.min(t,e.y)}if(n){const n=e.getBoundingClientRect();return{position:"viewport",x:n.x,y:Math.min(n.y,t)}}}return zn(e)}function Fn(e){return function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:K.rootWindow.document;if("viewport"===e.position){const n=function(){let e,t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:K.rootWindow.document;try{var r,i;e=n.pageXOffset||n.documentElement.scrollLeft||(null==n||null===(r=n.body)||void 0===r?void 0:r.scrollLeft),t=n.pageYOffset||n.documentElement.scrollTop||(null==n||null===(i=n.body)||void 0===i?void 0:i.scrollTop)}catch(n){e=t=0}return{x:e,y:t}}(t);e.x+=n.x,e.y+=n.y,e.position="absolute"}return e}(zn(e),e.ownerDocument)}function Un(e){return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:Fn)(e)}function Gn(e){if(null!=e&&e.getBoundingClientRect){let t=Bn(e);const{width:n,height:r}=e.getBoundingClientRect();return t(),{width:n,height:r}}}function Vn(e){if(null!=e&&e.getBoundingClientRect&&Rn(e)){let t=0,n=!1;for(const r of jn(e)){n=!0;const e=r.getBoundingClientRect();t=Math.max(t,e.height)}if(n){const n=e.getBoundingClientRect();return{width:n.width,height:Math.max(n.height,t)}}}return Gn(e)}const Hn=function(){return zt(arguments.length>0&&void 0!==arguments[0]?arguments[0]:K.rootWindow.navigator.userAgent)}();K.rootWindow.fetch;const qn=JSON.parse('{"meta":{"siteId":2576,"accountId":518,"active":true,"testMode":false,"configDate":"2025-05-20T16:21:20Z"},"gam":{"mcmId":"***********","decisionMaker":null,"decideBelow":null},"breakpoints":{},"cmp":null,"bidders":{},"openRTB":{"schain":{"default":{"config":{"nodes":[{"sid":"SHK"}]}},"bidders":{"amazon":{"validation":"strict","config":{"nodes":[{"asi":"venatus.com","sid":"SHK"}]}}}}},"auction":{"bias":{"default":{},"blockthrough":{}}},"tags":null,"reload":{"enabled":true,"interval":30},"abr":{"enabled":false},"richmediaIds":["smashkarts.io_728x90-new"],"devices":{"default":{"pages":{"known":{"default":{"placements":{"smashkarts-io_300x250":{"placementId":8049,"placementType":"mpu","placementConfig":{"canReload":true},"sizeConfigs":[{"config":{"id":8049,"configId":6788,"sizes":["160x600","300x250","300x600","120x600","336x280"],"slotRoutingConfig":{"enableGAM":true,"enableTAG":false},"bidders":{"adagio":{"params":[{"meta":{},"responseMeta":null,"params":{"site":"smashkarts-io","placement":"smashkarts.io_300x250","organizationId":"1361","adUnitElementId":"smashkarts-io_300x250"}}]},"amazon":{"params":[{"meta":{},"responseMeta":null,"params":{"adunitPath":"/*********/smashkarts.io_300x250"}}]},"appnexus":{"params":[{"meta":{},"responseMeta":null,"params":{"placementId":"********"}}]},"grid":{"params":[{"meta":{},"responseMeta":null,"params":{"uid":"402611"}}]},"ix":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"638010"}}]},"kueezRtb":{"params":[{"meta":{},"responseMeta":null,"params":{"cId":"673c8a24b78ef92798d785fd","pId":"65lk7c192882r0011813fn9"}}]},"medianet":{"params":[{"meta":{},"responseMeta":null,"params":{"cid":"8CUEHU9Y5","crid":"*********"}}]},"nobid":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"74400"}}]},"onetag":{"params":[{"meta":{},"responseMeta":null,"params":{"ext":{"placement_name":"smashkarts.io_300x250"},"pubId":"7683ebe7bee7969"}}]},"openx":{"params":[{"meta":{},"responseMeta":null,"params":{"unit":"*********","delDomain":"adinplay-d.openx.net"}}]},"ozone":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"**********","placementId":"**********","publisherId":"OZONEAIP0001"}}]},"pubmatic":{"params":[{"meta":{},"responseMeta":null,"params":{"adSlot":"4394818","publisherId":"156857"}}]},"rise":{"params":[{"meta":{},"responseMeta":null,"params":{"org":"643813aab7212c00011c3f28"}}]},"rubicon":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"360062","zoneId":"2685800","accountId":"23042"}}]},"sharethrough":{"params":[{"meta":{},"responseMeta":null,"params":{"pkey":"PtKEJ7vtdPMUEbJ7Yyw9LYb5"}}]},"sonobi":{"params":[{"meta":{},"responseMeta":null,"params":{"placement_id":"8d2a0fc6ff210331859b"}}]},"unruly":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"255900"}}]}},"adserver":{"id":"/smashkarts.io_300x250"},"adblock":{}}}]},"smashkarts-io_300x250_2":{"placementId":8050,"placementType":"mpu","placementConfig":{"canReload":true},"sizeConfigs":[{"config":{"id":8050,"configId":6789,"sizes":["160x600","300x250","300x600","120x600","336x280"],"slotRoutingConfig":{"enableGAM":true,"enableTAG":false},"bidders":{"adagio":{"params":[{"meta":{},"responseMeta":null,"params":{"site":"smashkarts-io","placement":"smashkarts.io_300x250_2","organizationId":"1361","adUnitElementId":"smashkarts-io_300x250_2"}}]},"amazon":{"params":[{"meta":{},"responseMeta":null,"params":{"adunitPath":"/*********/smashkarts.io_300x250_2"}}]},"appnexus":{"params":[{"meta":{},"responseMeta":null,"params":{"placementId":"********"}}]},"grid":{"params":[{"meta":{},"responseMeta":null,"params":{"uid":"402612"}}]},"ix":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"638009"}}]},"kueezRtb":{"params":[{"meta":{},"responseMeta":null,"params":{"cId":"673c8a24b78ef92798d785fd","pId":"65lk7c192882r0011813fn9"}}]},"medianet":{"params":[{"meta":{},"responseMeta":null,"params":{"cid":"8CUEHU9Y5","crid":"*********"}}]},"nobid":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"74400"}}]},"onetag":{"params":[{"meta":{},"responseMeta":null,"params":{"ext":{"placement_name":"smashkarts.io_300x250_2"},"pubId":"7683ebe7bee7969"}}]},"openx":{"params":[{"meta":{},"responseMeta":null,"params":{"unit":"*********","delDomain":"adinplay-d.openx.net"}}]},"ozone":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"**********","placementId":"**********","publisherId":"OZONEAIP0001"}}]},"pubmatic":{"params":[{"meta":{},"responseMeta":null,"params":{"adSlot":"4394817","publisherId":"156857"}}]},"rise":{"params":[{"meta":{},"responseMeta":null,"params":{"org":"643813aab7212c00011c3f28"}}]},"rubicon":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"360062","zoneId":"2685800","accountId":"23042"}}]},"sharethrough":{"params":[{"meta":{},"responseMeta":null,"params":{"pkey":"PtKEJ7vtdPMUEbJ7Yyw9LYb5"}}]},"sonobi":{"params":[{"meta":{},"responseMeta":null,"params":{"placement_id":"8d2a0fc6ff210331859b"}}]},"unruly":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"255900"}}]}},"adserver":{"id":"/smashkarts.io_300x250_2"},"adblock":{}}}]},"smashkarts-io_300x250_3":{"placementId":8051,"placementType":"mpu","placementConfig":{"canReload":true},"sizeConfigs":[{"config":{"id":8051,"configId":6790,"sizes":["160x600","300x250","300x600","120x600","336x280"],"slotRoutingConfig":{"enableGAM":true,"enableTAG":false},"bidders":{"adagio":{"params":[{"meta":{},"responseMeta":null,"params":{"site":"smashkarts-io","placement":"smashkarts.io_300x250_3","organizationId":"1361","adUnitElementId":"smashkarts-io_300x250_3"}}]},"amazon":{"params":[{"meta":{},"responseMeta":null,"params":{"adunitPath":"/*********/smashkarts.io_300x250_3"}}]},"appnexus":{"params":[{"meta":{},"responseMeta":null,"params":{"placementId":"********"}}]},"grid":{"params":[{"meta":{},"responseMeta":null,"params":{"uid":"402613"}}]},"ix":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"1018024"}}]},"kueezRtb":{"params":[{"meta":{},"responseMeta":null,"params":{"cId":"673c8a24b78ef92798d785fd","pId":"65lk7c192882r0011813fn9"}}]},"nobid":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"74400"}}]},"onetag":{"params":[{"meta":{},"responseMeta":null,"params":{"ext":{"placement_name":"smashkarts.io_300x250_3"},"pubId":"7683ebe7bee7969"}}]},"openx":{"params":[{"meta":{},"responseMeta":null,"params":{"unit":"*********","delDomain":"adinplay-d.openx.net"}}]},"ozone":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"**********","placementId":"**********","publisherId":"OZONEAIP0001"}}]},"pubmatic":{"params":[{"meta":{},"responseMeta":null,"params":{"adSlot":"5308213","publisherId":"156857"}}]},"rise":{"params":[{"meta":{},"responseMeta":null,"params":{"org":"643813aab7212c00011c3f28"}}]},"rubicon":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"360062","zoneId":"2685800","accountId":"23042"}}]},"sharethrough":{"params":[{"meta":{},"responseMeta":null,"params":{"pkey":"PtKEJ7vtdPMUEbJ7Yyw9LYb5"}}]},"sonobi":{"params":[{"meta":{},"responseMeta":null,"params":{"placement_id":"8d2a0fc6ff210331859b"}}]},"unruly":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"255900"}}]}},"adserver":{"id":"/smashkarts.io_300x250_3"},"adblock":{}}}]},"smashkarts-io_300x600_2":{"placementId":8046,"placementType":"double mpu","placementConfig":{"canReload":true},"sizeConfigs":[{"config":{"id":8046,"configId":6785,"sizes":["160x600","300x250","300x600","120x600","336x280"],"slotRoutingConfig":{"enableGAM":true,"enableTAG":false},"bidders":{"adagio":{"params":[{"meta":{},"responseMeta":null,"params":{"site":"smashkarts-io","placement":"smashkarts.io_300x600_2","organizationId":"1361","adUnitElementId":"smashkarts-io_300x600_2"}}]},"amazon":{"params":[{"meta":{},"responseMeta":null,"params":{"adunitPath":"/*********/smashkarts.io_300x600_2"}}]},"appnexus":{"params":[{"meta":{},"responseMeta":null,"params":{"placementId":"********"}}]},"grid":{"params":[{"meta":{},"responseMeta":null,"params":{"uid":"407765"}}]},"ix":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"1050288"}}]},"kueezRtb":{"params":[{"meta":{},"responseMeta":null,"params":{"cId":"673c8a24b78ef92798d785fd","pId":"65lk7c192882r0011813fn9"}}]},"nobid":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"74400"}}]},"onetag":{"params":[{"meta":{},"responseMeta":null,"params":{"ext":{"placement_name":"smashkarts.io_300x600_2"},"pubId":"7683ebe7bee7969"}}]},"openx":{"params":[{"meta":{},"responseMeta":null,"params":{"unit":"*********","delDomain":"adinplay-d.openx.net"}}]},"ozone":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"**********","placementId":"**********","publisherId":"OZONEAIP0001"}}]},"rise":{"params":[{"meta":{},"responseMeta":null,"params":{"org":"643813aab7212c00011c3f28"}}]},"rubicon":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"360062","zoneId":"2685800","accountId":"23042"}}]},"sharethrough":{"params":[{"meta":{},"responseMeta":null,"params":{"pkey":"FF4odeHvQvznVxD1nWSfeis5"}}]},"sonobi":{"params":[{"meta":{},"responseMeta":null,"params":{"placement_id":"8d2a0fc6ff210331859b"}}]}},"adserver":{"id":"/smashkarts.io_300x600_2"},"adblock":{}}}]},"smashkarts-io_320x100":{"placementId":8045,"placementType":"mobile banner","placementConfig":{"canReload":true},"sizeConfigs":[{"config":{"id":8045,"configId":6784,"sizes":["320x50","300x100","320x100","300x50"],"slotRoutingConfig":{"enableGAM":true,"enableTAG":false},"bidders":{"adagio":{"params":[{"meta":{},"responseMeta":null,"params":{"site":"smashkarts-io","placement":"smashkarts.io_320x100","organizationId":"1361","adUnitElementId":"smashkarts-io_320x100"}}]},"amazon":{"params":[{"meta":{},"responseMeta":null,"params":{"adunitPath":"/*********/smashkarts.io_320x100"}}]},"appnexus":{"params":[{"meta":{},"responseMeta":null,"params":{"placementId":"********"}}]},"grid":{"params":[{"meta":{},"responseMeta":null,"params":{"uid":"402615"}}]},"ix":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"637994"}}]},"kueezRtb":{"params":[{"meta":{},"responseMeta":null,"params":{"cId":"673c8a24b78ef92798d785fd","pId":"65lk7c192882r0011813fn9"}}]},"nobid":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"74400"}}]},"onetag":{"params":[{"meta":{},"responseMeta":null,"params":{"ext":{"placement_name":"smashkarts.io_320x100"},"pubId":"7683ebe7bee7969"}}]},"openx":{"params":[{"meta":{},"responseMeta":null,"params":{"unit":"*********","delDomain":"adinplay-d.openx.net"}}]},"ozone":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"**********","placementId":"**********","publisherId":"OZONEAIP0001"}}]},"pubmatic":{"params":[{"meta":{},"responseMeta":null,"params":{"adSlot":"4394816","publisherId":"156857"}}]},"rise":{"params":[{"meta":{},"responseMeta":null,"params":{"org":"643813aab7212c00011c3f28"}}]},"rubicon":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"360062","zoneId":"2685800","accountId":"23042"}}]},"sharethrough":{"params":[{"meta":{},"responseMeta":null,"params":{"pkey":"eKX3TLRyky9SQdzWu8BC2lPu"}}]},"sonobi":{"params":[{"meta":{},"responseMeta":null,"params":{"placement_id":"8d2a0fc6ff210331859b"}}]}},"adserver":{"id":"/smashkarts.io_320x100"},"adblock":{}}}]},"smashkarts-io_728x90-new":{"placementId":8052,"placementType":"leaderboard","placementConfig":{"canReload":true},"sizeConfigs":[{"config":{"id":8052,"configId":6791,"sizes":["728x90","160x600","300x250","300x600","120x600","468x60","970x250","970x90","320x50","320x100"],"slotRoutingConfig":{"enableGAM":true,"enableTAG":false},"bidders":{"adagio":{"params":[{"meta":{},"responseMeta":null,"params":{"site":"smashkarts-io","placement":"smashkarts.io_728x90-new","organizationId":"1361","adUnitElementId":"smashkarts-io_728x90-new"}}]},"amazon":{"params":[{"meta":{},"responseMeta":null,"params":{"adunitPath":"/*********/smashkarts.io_728x90-new"}}]},"appnexus":{"params":[{"meta":{},"responseMeta":null,"params":{"placementId":"********"}}]},"grid":{"params":[{"meta":{},"responseMeta":null,"params":{"uid":"402614"}}]},"ix":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"638008"}}]},"kueezRtb":{"params":[{"meta":{},"responseMeta":null,"params":{"cId":"673c8a24b78ef92798d785fd","pId":"65lk7c192882r0011813fn9"}}]},"nobid":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"74400"}}]},"onetag":{"params":[{"meta":{},"responseMeta":null,"params":{"ext":{"placement_name":"smashkarts.io_728x90-new"},"pubId":"7683ebe7bee7969"}}]},"openx":{"params":[{"meta":{},"responseMeta":null,"params":{"unit":"*********","delDomain":"adinplay-d.openx.net"}}]},"ozone":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"**********","placementId":"**********","publisherId":"OZONEAIP0001"}}]},"pubmatic":{"params":[{"meta":{},"responseMeta":null,"params":{"adSlot":"4394815","publisherId":"156857"}}]},"rise":{"params":[{"meta":{},"responseMeta":null,"params":{"org":"643813aab7212c00011c3f28"}}]},"rubicon":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"360062","zoneId":"2685800","accountId":"23042"}}]},"sharethrough":{"params":[{"meta":{},"responseMeta":null,"params":{"pkey":"yDTsfH71SE1Or47JMyfViROF"}}]},"sonobi":{"params":[{"meta":{},"responseMeta":null,"params":{"placement_id":"8d2a0fc6ff210331859b"}}]},"unruly":{"params":[{"meta":{},"responseMeta":null,"params":{"siteId":"255900"}}]}},"adserver":{"id":"/smashkarts.io_728x90-new"},"adblock":{}}}]},"smashkarts.io_preroll":{"placementId":8047,"placementType":"video","placementConfig":{},"sizeConfigs":[{"config":{"id":8047,"configId":6786,"slotRoutingConfig":{"enableGAM":true,"enableTAG":false},"bidders":{"amazon":{"params":[{"meta":{},"responseMeta":null,"params":{"slotID":"adinplay_videoad"}}]},"appnexus":{"params":[{"meta":{},"responseMeta":null,"params":{"placementId":"********"}}]},"grid":{"params":[{"meta":{},"responseMeta":null,"params":{"uid":"414557"}}]},"kueezRtb":{"params":[{"meta":{},"responseMeta":null,"params":{"cId":"6751b6307fb09e0ec1a6d5a8","pId":"65lk7c192882r0011813fn9"}}]},"openx":{"params":[{"meta":{},"responseMeta":null,"params":{"unit":"*********","delDomain":"adinplay-d.openx.net"}}]},"pubmatic":{"params":[{"meta":{},"responseMeta":null,"params":{"adSlot":"4394973","publisherId":"156857"}}]},"rise":{"params":[{"meta":{},"responseMeta":null,"params":{"org":"643813aab7212c00011c3f28"}}]},"sharethrough":{"params":[{"meta":{},"responseMeta":null,"params":{"pkey":"scckFeOENv1NYPKcJOdntOoK"}}]}},"adserver":{"id":"/smashkarts.io_preroll"},"adblock":{}}}]},"smashkarts.io_rvideo":{"placementId":8048,"placementType":"video reward","placementConfig":{},"sizeConfigs":[{"config":{"id":8048,"configId":6787,"slotRoutingConfig":{"enableGAM":true,"enableTAG":false},"adserver":{"id":"/smashkarts.io_rvideo"},"adblock":{}}}]}}}},"rules":{}}}}}'),Qn=JSON.parse('{"meta":{"tenantId":2},"gam":{"networkId":"*********"},"a9":{"displayPrices":"AdInPlay/a9_display_prices.json","videoPrices":"AdInPlay/a9_video_prices.json","apstag":{"pubID":"70247b00-ff8f-4016-b3ab-8344daf96e09"}},"openRTB":{"schain":{"default":{"validation":"strict","config":{"nodes":[{"asi":"adinplay.com","hp":1}]}}}},"auction":{"timeout":{"default":1500},"currency":{"adServerCurrency":"EUR","granularityMultiplier":1,"bidderCurrencyDefault":{"openx":"EUR"},"defaultRates":null},"legal":{"GDPR":{"cmpApi":"iab","timeout":1000},"GPP":{"cmpApi":"iab","timeout":1000}},"bias":{"default":{"bidders":{"rubicon":{"adjustment":0.75},"unruly":{"adjustment":0.87},"adagio":{"adjustment":0.9439},"grid":{"adjustment":0.9777},"rise":{"adjustment":0.9269},"ix":{"adjustment":0.9411},"_debug":{"adjustment":0.98,"virtual":1.08}}}},"identity":{"sharedId":{"storage":{"type":"cookie","name":"_sharedid","expires":365}},"criteo":{}},"preloadBidders":["appnexus","ozone","rubicon","onetag","cpmstar","amazon","ix","openx","pubmatic","triplelift"],"aliases":{},"s2s":{}},"bidders":{"pubmatic":{"defaultBidMeta":{"excludeGeo":["RU","UA","BY","RS","VE","LB","BA","MK","AL","IQ","ZW","ME","TM","PF","TJ","LY","SO","GP","AX","YE","AD","CD","MC","GA"]},"defaultBidParams":{"publisherId":"156857"}},"rhythmone":{"defaultBidMeta":{"excludeGeo":["TR","ID","VN","EG"]},"defaultBidParams":{}},"rubicon":{"defaultBidMeta":{"excludeGeo":["KR","BR","RU"]},"defaultBidParams":{"accountId":"23042"}},"onetag":{"defaultBidParams":{"pubId":"7683ebe7bee7969"}},"sharethrough":{"defaultBidMeta":{"includeGeo":["US","GB","CA","AU","DE","ES","IT","FR"]},"defaultBidParams":{}},"sonobi":{"defaultBidMeta":{"includeGeo":["US","GB","CA","AU","DE","FR","CH","HK","MX"]},"defaultBidParams":{}},"sovrn":{"defaultBidMeta":{"excludeGeo":["RU","PL","TR","RO","GR","KR","JP","CN","TW","BR"]},"defaultBidParams":{}},"optidigital":{"defaultBidMeta":{"includeGeo":["US","GB","CA","DE","FR","IT","IE","CH","ES","NL","BE","SE","LU"]},"defaultBidParams":{}},"kueezRtb":{"defaultBidMeta":{"includeGeo":["US","CA","MX","GB","BR","DE","FR","ES","AR","AU","IT","JP","CL","PH","CO","TR","ZA","NL","PE","NZ","CH","HK","AT","SE","SG","HU","AE","PR","SA","PT","IE","DK","CR","IL","CZ","FI","BG","HN","MD","LV"]},"defaultBidParams":{}},"rise":{"defaultBidMeta":{"includeGeo":["US","CA","AU","GB","DE","BE","FI","SE","NO","FR","ES","IT","NL","DK","CH","AT","IE","PT"]},"defaultBidParams":{}}}}');function Kn(e,t,n){return e.set(Zn(e,t),n),n}function Xn(e,t,n){Yn(e,t),t.set(e,n)}function Yn(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function Jn(e,t){return e.get(Zn(e,t))}function Zn(e,t,n){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:n;throw new TypeError("Private element is not present on this object")}var $n=new WeakMap;class er{constructor(){Xn(this,$n,new Map)}get length(){return Jn($n,this).size}clear(){Jn($n,this).clear()}getItem(e){return Jn($n,this).get(e)}key(e){return Jn($n,this).keys()[e]}removeItem(e){Jn($n,this).delete(e)}setItem(e,t){Jn($n,this).set(e,t)}hasKey(e){return Jn($n,this).has(e)}keys(e){return Jn($n,this).keys()}}var tr=new WeakMap,nr=new WeakMap,rr=new WeakMap,ir=new WeakMap,or=new WeakSet;class ar{get initialized(){return Jn(ir,this)}constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"localStorage",n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:K.rootWindow;var r;Yn(this,r=or),r.add(this),Xn(this,tr,"vms--"),Xn(this,nr,void 0),Xn(this,rr,void 0),Xn(this,ir,!1);try{Kn(nr,this,n),Kn(rr,this,Jn(nr,this)[e]),Kn(ir,this,!0)}catch(e){Kn(ir,this,!1),Kn(rr,this,function(){const e=new er;return new Proxy(e,{get:(e,n)=>"length"===n?e.length:t(e[n])?e[n].bind(e):e.getItem(n),set:(e,t,n)=>(e.setItem(t,n),!0),deleteProperty:(e,t)=>!!e.hasKey(t)&&(e.removeItem(t),!0),ownKeys:e=>e.keys(),has:(e,t)=>e.hasKey(t)})}())}}removeExpired(){try{for(let e=0;e<Jn(rr,this).length;e++){const t=Jn(rr,this).key(e);if(t&&t.startsWith(Jn(tr,this))){const e=Jn(rr,this).getItem(t);if(e){const n=JSON.parse(e);n.expiresIn>=0&&(new Date).getTime()>n.expiresIn+n.createdAt&&Jn(rr,this).removeItem(t)}}}}catch(e){}}set(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1;try{Jn(rr,this).setItem(Zn(or,this,sr).call(this,e),JSON.stringify({storedValue:t,createdAt:(new Date).getTime(),expiresIn:n}))}catch(e){}}get(e){const t=Zn(or,this,sr).call(this,e);try{const e=Jn(rr,this).getItem(t);if(e){const n=JSON.parse(e);if(-1==n.expiresIn)return n;if(!((new Date).getTime()>n.expiresIn+n.createdAt))return n;Jn(rr,this).removeItem(t)}}catch(e){}return null}removeItem(e){const t=Zn(or,this,sr).call(this,e);try{Jn(rr,this).getItem(t)&&Jn(rr,this).removeItem(t)}catch(e){}}getValue(e){const t=this.get(e);return null===t?null:t.storedValue}getOrCreate(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1;return this.getValue(e)||(this.set(e,t,n),t)}getValueAsJson(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{const n=Jn(rr,this).getItem(Zn(or,this,sr).call(this,e,t));if(n)return JSON.parse(n)}catch(e){}return null}}function sr(e){return"".concat(arguments.length>1&&void 0!==arguments[1]&&arguments[1]?"":Jn(tr,this)).concat(e)}let lr,cr;try{lr=new ar,lr.removeExpired(),cr=new ar("sessionStorage")}catch(e){}const ur=lr,dr={reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29],black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39],bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]};function pr(e,t,n,r=!1){const i=String(t),o=(e,t)=>`[${t[0]}m${e}[${t[1]}m`,a=(e,t)=>null!=t&&"string"==typeof t?o(e,dr[t]):null!=t&&Array.isArray(t)?t.reduce(((e,t)=>a(e,t)),e):null!=t&&null!=t[e.trim()]?a(e,t[e.trim()]):null!=t&&null!=t["*"]?a(e,t["*"]):e;return i.replace(/{{(.+?)}}/g,((t,i)=>{const s=null!=n[i]?String(n[i]):r?"":t;return e.stylePrettyLogs?a(s,e?.prettyLogStyles?.[i]??null)+o("",dr.reset):s}))}function hr(e,t=2,n=0){return null!=e&&isNaN(e)?"":(e=null!=e?e+n:e,2===t?null==e?"--":e<10?"0"+e:e.toString():null==e?"---":e<10?"00"+e:e<100?"0"+e:e.toString())}function mr(e){const t=new Set;return JSON.stringify(e,((e,n)=>{if("object"==typeof n&&null!==n){if(t.has(n))return"[Circular]";t.add(n)}return"bigint"==typeof n?`${n}`:n}))}function fr(e,t){const n={seen:[],stylize:vr};return null!=t&&Rr(n,t),gr(n.showHidden)&&(n.showHidden=!1),gr(n.depth)&&(n.depth=2),gr(n.colors)&&(n.colors=!0),gr(n.customInspect)&&(n.customInspect=!0),n.colors&&(n.stylize=br),Tr(n,e,n.depth)}function gr(e){return void 0===e}function vr(e){return e}function br(e,t){const n=fr.styles[t];return null!=n&&null!=fr?.colors?.[n]?.[0]&&null!=fr?.colors?.[n]?.[1]?"["+fr.colors[n][0]+"m"+e+"["+fr.colors[n][1]+"m":e}function yr(e){return"function"==typeof e}function wr(e){return"string"==typeof e}function xr(e){return null===e}function Ar(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function Er(e){return kr(e)&&"[object RegExp]"===Pr(e)}function kr(e){return"object"==typeof e&&null!==e}function Ir(e){return kr(e)&&("[object Error]"===Pr(e)||e instanceof Error)}function Sr(e){return kr(e)&&"[object Date]"===Pr(e)}function Pr(e){return Object.prototype.toString.call(e)}function Cr(e){return"["+Error.prototype.toString.call(e)+"]"}function Tr(e,t,n=0){if(e.customInspect&&null!=t&&yr(t)&&t?.inspect!==fr&&(!t?.constructor||t?.constructor.prototype!==t)){if("function"!=typeof t.inspect&&null!=t.toString)return t.toString();let r=t?.inspect(n,e);return wr(r)||(r=Tr(e,r,n)),r}const r=Or(e,t);if(r)return r;let i=Object.keys(t);const o=function(e){const t={};return e.forEach((e=>{t[e]=!0})),t}(i);try{e.showHidden&&Object.getOwnPropertyNames&&(i=Object.getOwnPropertyNames(t))}catch(e){}if(Ir(t)&&(i.indexOf("message")>=0||i.indexOf("description")>=0))return Cr(t);if(0===i.length){if(!yr(e.stylize))return t;if(yr(t)){const n=t.name?": "+t.name:"";return e.stylize("[Function"+n+"]","special")}if(Er(t))return e.stylize(RegExp.prototype.toString.call(t),"regexp");if(Sr(t))return e.stylize(Date.prototype.toISOString.call(t),"date");if(Ir(t))return Cr(t)}let a,s="",l=!1,c=["{\n","\n}"];return Array.isArray(t)&&(l=!0,c=["[\n","\n]"]),yr(t)&&(s=" [Function"+(t.name?": "+t.name:"")+"]"),Er(t)&&(s=" "+RegExp.prototype.toString.call(t)),Sr(t)&&(s=" "+Date.prototype.toUTCString.call(t)),Ir(t)&&(s=" "+Cr(t)),0!==i.length||l&&0!=t.length?n<0?Er(t)?e.stylize(RegExp.prototype.toString.call(t),"regexp"):e.stylize("[Object]","special"):(e.seen.push(t),a=l?function(e,t,n,r,i){const o=[];for(let i=0,a=t.length;i<a;++i)Ar(t,String(i))?o.push(Mr(e,t,n,r,String(i),!0)):o.push("");return i.forEach((i=>{i.match(/^\d+$/)||o.push(Mr(e,t,n,r,i,!0))})),o}(e,t,n,o,i):i.map((r=>Mr(e,t,n,o,r,l))),e.seen.pop(),function(e,t,n){return n[0]+(""===t?"":t+"\n")+"  "+e.join(",\n  ")+" "+n[1]}(a,s,c)):c[0]+s+c[1]}function Mr(e,t,n,r,i,o){let a,s,l={value:void 0};try{l.value=t[i]}catch(e){}try{Object.getOwnPropertyDescriptor&&(l=Object.getOwnPropertyDescriptor(t,i)||l)}catch(e){}if(l.get?s=l.set?e.stylize("[Getter/Setter]","special"):e.stylize("[Getter]","special"):l.set&&(s=e.stylize("[Setter]","special")),Ar(r,i)||(a="["+i+"]"),s||(e.seen.indexOf(l.value)<0?(s=xr(n)?Tr(e,l.value,void 0):Tr(e,l.value,n-1),s.indexOf("\n")>-1&&(s=o?s.split("\n").map((e=>"  "+e)).join("\n").substr(2):"\n"+s.split("\n").map((e=>"   "+e)).join("\n"))):s=e.stylize("[Circular]","special")),gr(a)){if(o&&i.match(/^\d+$/))return s;a=JSON.stringify(""+i),a.match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(a=a.substr(1,a.length-2),a=e.stylize(a,"name")):(a=a.replace(/'/g,"\\'").replace(/\\"/g,"\\'").replace(/(^"|"$)/g,"'"),a=e.stylize(a,"string"))}return a+": "+s}function Or(e,t){if(gr(t))return e.stylize("undefined","undefined");if(wr(t)){const n="'"+JSON.stringify(t).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,"\\'")+"'";return e.stylize(n,"string")}return"number"==typeof t?e.stylize(""+t,"number"):"boolean"==typeof t?e.stylize(""+t,"boolean"):xr(t)?e.stylize("null","null"):void 0}function Rr(e,t){const n={...e};if(!t||!kr(t))return e;const r={...t},i=Object.keys(t);let o=i.length;for(;o--;)n[i[o]]=r[i[o]];return n}fr.colors=dr,fr.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"};const _r={getCallerStackFrame:Dr,getErrorTrace:jr,getMeta:function(e,t,n,r,i,o){return Object.assign({},Lr,{name:i,parentNames:o,date:new Date,logLevelId:e,logLevelName:t,path:r?void 0:Dr(n)})},transportJSON:function(e){console.log(mr(e))},transportFormatted:function(e,t,n,r){const i=(n.length>0&&t.length>0?"\n":"")+n.join("\n");r.prettyInspectOptions.colors=r.stylePrettyLogs,console.log(e+function(e,...t){const n={seen:[],stylize:vr};null!=e&&Rr(n,e);const r=t[0];let i=0,o="",a="";if("string"==typeof r){if(1===t.length)return r;let s,l=0;for(let a=0;a<r.length-1;a++)if(37===r.charCodeAt(a)){const c=r.charCodeAt(++a);if(i+1!==t.length){switch(c){case 115:{const r=t[++i];s="number"==typeof r||"bigint"==typeof r?Or(n,r):"object"!=typeof r||null===r?String(r):fr(r,{...e,compact:3,colors:!1,depth:0});break}case 106:s=mr(t[++i]);break;case 100:{const e=t[++i];s="bigint"==typeof e?Or(n,e):"symbol"==typeof e?"NaN":Or(n,e);break}case 79:s=fr(t[++i],e);break;case 111:s=fr(t[++i],{...e,showHidden:!0,showProxy:!0,depth:4});break;case 105:{const e=t[++i];s="bigint"==typeof e?Or(n,e):"symbol"==typeof e?"NaN":Or(n,parseInt(s));break}case 102:{const e=t[++i];s="symbol"==typeof e?"NaN":Or(n,parseInt(e));break}case 99:i+=1,s="";break;case 37:o+=r.slice(l,a),l=a+1;continue;default:continue}l!==a-1&&(o+=r.slice(l,a-1)),o+=s,l=a+1}else 37===c&&(o+=r.slice(l,a),l=a+1)}0!==l&&(i++,a=" ",l<r.length&&(o+=r.slice(l)))}for(;i<t.length;){const n=t[i];o+=a,o+="string"!=typeof n?fr(n,e):n,a=" ",i++}return o}(r.prettyInspectOptions,...t)+i)},isBuffer:function(e){return!1},isError:zr,prettyFormatLogObj:function(e,t){return e.reduce(((e,n)=>(zr(n)?e.errors.push(Wr(n,t)):e.args.push(n),e)),{args:[],errors:[]})},prettyFormatErrorObj:Wr},Lr={runtime:[typeof window,typeof document].includes("undefined")?"Generic":"Browser",browser:globalThis?.navigator?.userAgent},Nr=/(?:(?:file|https?|global code|[^@]+)@)?(?:file:)?((?:\/[^:/]+){2,})(?::(\d+))?(?::(\d+))?/;function Dr(e,t=Error()){return Br(t?.stack?.split("\n")?.filter((e=>!e.includes("Error: ")))?.[e])}function jr(e){return(e?.stack?.split("\n")??[])?.filter((e=>!e.includes("Error: ")))?.reduce(((e,t)=>(e.push(Br(t)),e)),[])}function Br(e){const t=globalThis?.location?.origin,n={fullFilePath:void 0,fileName:void 0,fileNameWithLine:void 0,fileColumn:void 0,fileLine:void 0,filePath:void 0,filePathWithLine:void 0,method:void 0};if(null!=e){const r=e.match(Nr);if(r){n.filePath=r[1].replace(/\?.*$/,""),n.fullFilePath=`${t}${n.filePath}`;const e=n.filePath.split("/");n.fileName=e[e.length-1],n.fileLine=r[2],n.fileColumn=r[3],n.filePathWithLine=`${n.filePath}:${n.fileLine}`,n.fileNameWithLine=`${n.fileName}:${n.fileLine}`}}return n}function zr(e){return e instanceof Error}function Wr(e,t){const n=jr(e).map((e=>pr(t,t.prettyErrorStackTemplate,{...e},!0))),r={errorName:` ${e.name} `,errorMessage:Object.getOwnPropertyNames(e).reduce(((t,n)=>("stack"!==n&&t.push(e[n]),t)),[]).join(", "),errorStack:n.join("\n")};return pr(t,t.prettyErrorTemplate,r)}class Fr{constructor(e,t,n=4){this.logObj=t,this.stackDepthLevel=n,this.runtime=_r,this.settings={type:e?.type??"pretty",name:e?.name,parentNames:e?.parentNames,minLevel:e?.minLevel??0,argumentsArrayName:e?.argumentsArrayName,hideLogPositionForProduction:e?.hideLogPositionForProduction??!1,prettyLogTemplate:e?.prettyLogTemplate??"{{yyyy}}.{{mm}}.{{dd}} {{hh}}:{{MM}}:{{ss}}:{{ms}}\t{{logLevelName}}\t{{filePathWithLine}}{{nameWithDelimiterPrefix}}\t",prettyErrorTemplate:e?.prettyErrorTemplate??"\n{{errorName}} {{errorMessage}}\nerror stack:\n{{errorStack}}",prettyErrorStackTemplate:e?.prettyErrorStackTemplate??"  • {{fileName}}\t{{method}}\n\t{{filePathWithLine}}",prettyErrorParentNamesSeparator:e?.prettyErrorParentNamesSeparator??":",prettyErrorLoggerNameDelimiter:e?.prettyErrorLoggerNameDelimiter??"\t",stylePrettyLogs:e?.stylePrettyLogs??!0,prettyLogTimeZone:e?.prettyLogTimeZone??"UTC",prettyLogStyles:e?.prettyLogStyles??{logLevelName:{"*":["bold","black","bgWhiteBright","dim"],SILLY:["bold","white"],TRACE:["bold","whiteBright"],DEBUG:["bold","green"],INFO:["bold","blue"],WARN:["bold","yellow"],ERROR:["bold","red"],FATAL:["bold","redBright"]},dateIsoStr:"white",filePathWithLine:"white",name:["white","bold"],nameWithDelimiterPrefix:["white","bold"],nameWithDelimiterSuffix:["white","bold"],errorName:["bold","bgRedBright","whiteBright"],fileName:["yellow"],fileNameWithLine:"white"},prettyInspectOptions:e?.prettyInspectOptions??{colors:!0,compact:!1,depth:1/0},metaProperty:e?.metaProperty??"_meta",maskPlaceholder:e?.maskPlaceholder??"[***]",maskValuesOfKeys:e?.maskValuesOfKeys??["password"],maskValuesOfKeysCaseInsensitive:e?.maskValuesOfKeysCaseInsensitive??!1,maskValuesRegEx:e?.maskValuesRegEx,prefix:[...e?.prefix??[]],attachedTransports:[...e?.attachedTransports??[]],overwrite:{mask:e?.overwrite?.mask,toLogObj:e?.overwrite?.toLogObj,addMeta:e?.overwrite?.addMeta,addPlaceholders:e?.overwrite?.addPlaceholders,formatMeta:e?.overwrite?.formatMeta,formatLogObj:e?.overwrite?.formatLogObj,transportFormatted:e?.overwrite?.transportFormatted,transportJSON:e?.overwrite?.transportJSON}}}log(e,t,...n){if(e<this.settings.minLevel)return;const r=[...this.settings.prefix,...n],i=null!=this.settings.overwrite?.mask?this.settings.overwrite?.mask(r):null!=this.settings.maskValuesOfKeys&&this.settings.maskValuesOfKeys.length>0?this._mask(r):r,o=null!=this.logObj?this._recursiveCloneAndExecuteFunctions(this.logObj):void 0,a=null!=this.settings.overwrite?.toLogObj?this.settings.overwrite?.toLogObj(i,o):this._toLogObj(i,o),s=null!=this.settings.overwrite?.addMeta?this.settings.overwrite?.addMeta(a,e,t):this._addMetaToLogObj(a,e,t);let l,c;return null!=this.settings.overwrite?.formatMeta&&(l=this.settings.overwrite?.formatMeta(s?.[this.settings.metaProperty])),null!=this.settings.overwrite?.formatLogObj&&(c=this.settings.overwrite?.formatLogObj(i,this.settings)),"pretty"===this.settings.type&&(l=l??this._prettyFormatLogObjMeta(s?.[this.settings.metaProperty]),c=c??this.runtime.prettyFormatLogObj(i,this.settings)),null!=l&&null!=c?null!=this.settings.overwrite?.transportFormatted?this.settings.overwrite?.transportFormatted(l,c.args,c.errors,this.settings):this.runtime.transportFormatted(l,c.args,c.errors,this.settings):null!=this.settings.overwrite?.transportJSON?this.settings.overwrite?.transportJSON(s):"hidden"!==this.settings.type&&this.runtime.transportJSON(s),null!=this.settings.attachedTransports&&this.settings.attachedTransports.length>0&&this.settings.attachedTransports.forEach((e=>{e(s)})),s}attachTransport(e){this.settings.attachedTransports.push(e)}getSubLogger(e,t){const n={...this.settings,...e,parentNames:null!=this.settings?.parentNames&&null!=this.settings?.name?[...this.settings.parentNames,this.settings.name]:null!=this.settings?.name?[this.settings.name]:void 0,prefix:[...this.settings.prefix,...e?.prefix??[]]};return new this.constructor(n,t??this.logObj,this.stackDepthLevel)}_mask(e){const t=!0!==this.settings.maskValuesOfKeysCaseInsensitive?this.settings.maskValuesOfKeys:this.settings.maskValuesOfKeys.map((e=>e.toLowerCase()));return e?.map((e=>this._recursiveCloneAndMaskValuesOfKeys(e,t)))}_recursiveCloneAndMaskValuesOfKeys(e,t,n=[]){if(n.includes(e))return{...e};if("object"==typeof e&&null!==e&&n.push(e),this.runtime.isError(e)||this.runtime.isBuffer(e))return e;if(e instanceof Map)return new Map(e);if(e instanceof Set)return new Set(e);if(Array.isArray(e))return e.map((e=>this._recursiveCloneAndMaskValuesOfKeys(e,t,n)));if(e instanceof Date)return new Date(e.getTime());if(e instanceof URL)return{href:(r=e).href,protocol:r.protocol,username:r.username,password:r.password,host:r.host,hostname:r.hostname,port:r.port,pathname:r.pathname,search:r.search,searchParams:[...r.searchParams].map((([e,t])=>({key:e,value:t}))),hash:r.hash,origin:r.origin};if(null!==e&&"object"==typeof e){const r=this.runtime.isError(e)?this._cloneError(e):Object.create(Object.getPrototypeOf(e));return Object.getOwnPropertyNames(e).reduce(((r,i)=>(r[i]=t.includes(!0!==this.settings?.maskValuesOfKeysCaseInsensitive?i:i.toLowerCase())?this.settings.maskPlaceholder:(()=>{try{return this._recursiveCloneAndMaskValuesOfKeys(e[i],t,n)}catch(e){return null}})(),r)),r)}if("string"==typeof e){let t=e;for(const e of this.settings?.maskValuesRegEx||[])t=t.replace(e,this.settings?.maskPlaceholder||"");return t}return e;var r}_recursiveCloneAndExecuteFunctions(e,t=[]){return this.isObjectOrArray(e)&&t.includes(e)?this.shallowCopy(e):(this.isObjectOrArray(e)&&t.push(e),Array.isArray(e)?e.map((e=>this._recursiveCloneAndExecuteFunctions(e,t))):e instanceof Date?new Date(e.getTime()):this.isObject(e)?Object.getOwnPropertyNames(e).reduce(((n,r)=>{const i=Object.getOwnPropertyDescriptor(e,r);if(i){Object.defineProperty(n,r,i);const o=e[r];n[r]="function"==typeof o?o():this._recursiveCloneAndExecuteFunctions(o,t)}return n}),Object.create(Object.getPrototypeOf(e))):e)}isObjectOrArray(e){return"object"==typeof e&&null!==e}isObject(e){return"object"==typeof e&&!Array.isArray(e)&&null!==e}shallowCopy(e){return Array.isArray(e)?[...e]:{...e}}_toLogObj(e,t={}){return e=e?.map((e=>this.runtime.isError(e)?this._toErrorObject(e):e)),null==this.settings.argumentsArrayName?1!==e.length||Array.isArray(e[0])||!0===this.runtime.isBuffer(e[0])||e[0]instanceof Date?{...t,...e}:"object"==typeof e[0]&&null!=e[0]?{...e[0],...t}:{0:e[0],...t}:{...t,[this.settings.argumentsArrayName]:e}}_cloneError(e){const t=new e.constructor;return Object.getOwnPropertyNames(e).forEach((n=>{t[n]=e[n]})),t}_toErrorObject(e){return{nativeError:e,name:e.name??"Error",message:e.message,stack:this.runtime.getErrorTrace(e)}}_addMetaToLogObj(e,t,n){return{...e,[this.settings.metaProperty]:this.runtime.getMeta(t,n,this.stackDepthLevel,this.settings.hideLogPositionForProduction,this.settings.name,this.settings.parentNames)}}_prettyFormatLogObjMeta(e){if(null==e)return"";let t=this.settings.prettyLogTemplate;const n={};t.includes("{{yyyy}}.{{mm}}.{{dd}} {{hh}}:{{MM}}:{{ss}}:{{ms}}")?t=t.replace("{{yyyy}}.{{mm}}.{{dd}} {{hh}}:{{MM}}:{{ss}}:{{ms}}","{{dateIsoStr}}"):"UTC"===this.settings.prettyLogTimeZone?(n.yyyy=e?.date?.getUTCFullYear()??"----",n.mm=hr(e?.date?.getUTCMonth(),2,1),n.dd=hr(e?.date?.getUTCDate(),2),n.hh=hr(e?.date?.getUTCHours(),2),n.MM=hr(e?.date?.getUTCMinutes(),2),n.ss=hr(e?.date?.getUTCSeconds(),2),n.ms=hr(e?.date?.getUTCMilliseconds(),3)):(n.yyyy=e?.date?.getFullYear()??"----",n.mm=hr(e?.date?.getMonth(),2,1),n.dd=hr(e?.date?.getDate(),2),n.hh=hr(e?.date?.getHours(),2),n.MM=hr(e?.date?.getMinutes(),2),n.ss=hr(e?.date?.getSeconds(),2),n.ms=hr(e?.date?.getMilliseconds(),3));const r="UTC"===this.settings.prettyLogTimeZone?e?.date:new Date(e?.date?.getTime()-6e4*e?.date?.getTimezoneOffset());n.rawIsoStr=r?.toISOString(),n.dateIsoStr=r?.toISOString().replace("T"," ").replace("Z",""),n.logLevelName=e?.logLevelName,n.fileNameWithLine=e?.path?.fileNameWithLine??"",n.filePathWithLine=e?.path?.filePathWithLine??"",n.fullFilePath=e?.path?.fullFilePath??"";let i=this.settings.parentNames?.join(this.settings.prettyErrorParentNamesSeparator);return i=null!=i&&null!=e?.name?i+this.settings.prettyErrorParentNamesSeparator:void 0,n.name=null!=e?.name||null!=i?(i??"")+e?.name??"":"",n.nameWithDelimiterPrefix=n.name.length>0?this.settings.prettyErrorLoggerNameDelimiter+n.name:"",n.nameWithDelimiterSuffix=n.name.length>0?n.name+this.settings.prettyErrorLoggerNameDelimiter:"",null!=this.settings.overwrite?.addPlaceholders&&this.settings.overwrite?.addPlaceholders(e,n),pr(this.settings,t,n)}}class Ur extends Fr{constructor(e,t){const n="undefined"!=typeof window&&"undefined"!=typeof document,r=!!n&&void 0!==window.chrome&&void 0!==window.CSS&&window.CSS.supports("color","green"),i=!!n&&/^((?!chrome|android).)*safari/i.test(navigator.userAgent);(e=e||{}).stylePrettyLogs=!(e.stylePrettyLogs&&n&&!r)&&e.stylePrettyLogs,super(e,t,i?4:5)}log(e,t,...n){return super.log(e,t,...n)}silly(...e){return super.log(0,"SILLY",...e)}trace(...e){return super.log(1,"TRACE",...e)}debug(...e){return super.log(2,"DEBUG",...e)}info(...e){return super.log(3,"INFO",...e)}warn(...e){return super.log(4,"WARN",...e)}error(...e){return super.log(5,"ERROR",...e)}fatal(...e){return super.log(6,"FATAL",...e)}getSubLogger(e,t){return super.getSubLogger(e,t)}}function Gr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Vr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Gr(Object(n),!0).forEach((function(t){Hr(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Gr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Hr(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function qr(e,t,n){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,n)}function Qr(e,t,n){return e.set(Xr(e,t),n),n}function Kr(e,t){return e.get(Xr(e,t))}function Xr(e,t,n){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:n;throw new TypeError("Private element is not present on this object")}let Yr=null;const Jr=["silly","trace","debug","info","warn","error","fatal","network"],Zr=e=>{const t=function(){Yr="info";try{e.info(...arguments)}catch(e){console.error("error logging",e)}};return Jr.forEach((n=>{t[n]=function(){Yr=n;try{e[n](...arguments)}catch(e){console.error("error logging",e)}}})),t.logger=e,t},$r=Zr(new class extends Ur{constructor(e,t){super(e,t)}network(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return super.log(7,"NETWORK",...t)}}({argumentsArrayName:"values",prettyLogStyles:{logLevelName:{"*":["bold","black","bgWhiteBright","dim"],SILLY:["bold","black","bgBlackBright"],TRACE:["bold","whiteBright"],DEBUG:["bold","green"],INFO:["bold","white"],WARN:["bold","yellow"],ERROR:["bold","red"],FATAL:["bold","redBright"],NETWORK:["bold","magentaBright"]},name:["black","bold","bgRed"]},prettyErrorParentNamesSeparator:":",minLevel:4,prettyLogTemplate:"{{hh}}:{{MM}}:{{ss}}:{{ms}} {{logLevelName}} {{name}} ",maskValuesOfKeys:[],overwrite:{transportFormatted:function(e,t,n,r){const i=(n.length>0&&t.length>0?"\n":"")+n.join("\n");r.prettyInspectOptions.colors=r.stylePrettyLogs,"string"==typeof t[0]&&(e+=t[0],t=t.slice(1));let o="private"===ai.logFixer?li().console:console;if(ai.logToConsole){if(!1===ai.levelFilter[Jr.indexOf(Yr)])return;try{switch(Yr){case"error":case"fatal":o.error(e,...t,i);break;case"warn":o.warn(e,...t,i);break;case"info":o.info(e,...t,i);break;case"trace":o.trace(e,...t,i);break;case"debug":o.debug(e,...t,i);break;default:try{o.log(e,...t,i)}catch(e){o.log(...t,i,e)}}}catch(e){console.error("error logging message",e)}}}}}));var ei=new WeakMap,ti=new WeakMap,ni=new WeakMap,ri=new WeakMap,ii=new WeakMap,oi=new WeakMap;const ai=new class{constructor(){var e,t,n,r,i,o,a,s;qr(this,ei,"logger-settings"),qr(this,ti,!1),qr(this,ni,null!==(e=null==ur||null===(t=ur.getValue(Kr(ei,this)))||void 0===t?void 0:t.logToConsole)&&void 0!==e&&e),qr(this,ri,null!==(n=null==ur||null===(r=ur.getValue(Kr(ei,this)))||void 0===r?void 0:r.filterText)&&void 0!==n?n:""),qr(this,ii,null!==(i=null==ur||null===(o=ur.getValue(Kr(ei,this)))||void 0===o?void 0:o.levelFilter)&&void 0!==i?i:[!1,!0,!0,!0,!0,!0,!0,!0]),qr(this,oi,null!==(a=null==ur||null===(s=ur.getValue(Kr(ei,this)))||void 0===s?void 0:s.logFixer)&&void 0!==a?a:"native")}set logToConsole(e){Qr(ni,this,e);const t=(null==ur?void 0:ur.getValue(Kr(ei,this)))||{};null==ur||ur.set(Kr(ei,this),Vr(Vr({},t),{},{logToConsole:e}))}get logToConsole(){return Kr(ni,this)}set productionLog(e){$r.logger.settings.minLevel=e?4:0}set preserveLogs(e){Qr(ti,this,e)}get preserveLogs(){return Kr(ti,this)}set filterText(e){Qr(ri,this,e);const t=(null==ur?void 0:ur.getValue(Kr(ei,this)))||{};null==ur||ur.set(Kr(ei,this),Vr(Vr({},t),{},{filterText:e}))}get filterText(){return Kr(ri,this)}get levelFilter(){return Kr(ii,this)}set levelFilter(e){Qr(ii,this,e);const t=(null==ur?void 0:ur.getValue(Kr(ei,this)))||{};null==ur||ur.set(Kr(ei,this),Vr(Vr({},t),{},{levelFilter:e}))}get logFixer(){return Kr(oi,this)}set logFixer(e){Qr(oi,this,e);const t=(null==ur?void 0:ur.getValue(Kr(ei,this)))||{};null==ur||ur.set(Kr(ei,this),Vr(Vr({},t),{},{logFixer:e})),ci(e)}};let si;const li=()=>(si||(si=document.createElement("iframe"),function(e,n){const r={};if(!e)return r;for(var o in n)if(n.hasOwnProperty(o))switch(o){case"left":case"right":case"top":case"bottom":case"inset":case"width":case"height":case"minWidth":case"minHeight":case"maxWidth":case"maxHeight":case"paddingLeft":case"paddingTop":case"paddingBottom":case"paddingRight":case"marginLeft":case"marginTop":case"marginBottom":case"marginRight":case"lineHeight":case"borderRadius":case"fontSize":case"overflowClipMargin":0!==n[o]&&"0"!==n[o]&&t(s=n[o])&&(i(s)&&s.trim().match(/^-?\d+(?:\.\d+)?$/)&&(s=parseFloat(s)),a(s))&&(n[o]+="px");default:r[o]=e.style[o],null===n[o]?e.style.removeProperty(Y(o)):e.style[o]=n[o]}var s}(si,{display:"none"}),An((e=>{e.appendChild(si)}),K.rootWindow.document,!1)),si.contentWindow),ci=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:K.rootWindow;"fix"===e&&(t.console=li().console)};ci(ai.logFixer);const ui=[];$r.logger.attachTransport((e=>{ai.preserveLogs&&(e._meta.index=ui.length,ui.push(e))}));let di=new Map;const pi=(e,t)=>{const n="string"==typeof e?e:null==e?void 0:e.constructor;if(di.has(n))return di.get(n);let r=$r;t&&(r=pi(t));const i=Zr(r.logger.getSubLogger({name:(null==n?void 0:n.name)||n}));return di.set(n,i),di.get(n)};pi("Default");const hi=pi("debug-flag");let mi,fi={trap:()=>hi("captured debug trap",function(){Error.stackTraceLimit=1/0;const e=(new Error).stack;return null==e?void 0:e.replace(/^\s*Error/i,"Stack: \n")}())};new WeakMap,n(6388);var gi=n(5072),vi=n.n(gi),bi=n(7825),yi=n.n(bi),wi=n(7659),xi=n.n(wi),Ai=n(5056),Ei=n.n(Ai),ki=n(8159),Ii=n.n(ki),Si=n(1113),Pi=n.n(Si),Ci=n(7186),Ti=n.n(Ci),Mi={};Mi.styleTagTransform=Pi(),Mi.setAttributes=Ei(),Mi.insert=xi().bind(null,"head"),Mi.domAPI=yi(),Mi.insertStyleElement=Ii(),vi()(Ti(),Mi);const Oi=Ti()&&Ti().locals?Ti().locals:void 0;var Ri=n(897),_i=n.n(Ri),Li={};Li.styleTagTransform=Pi(),Li.setAttributes=Ei(),Li.insert=xi().bind(null,"head"),Li.domAPI=yi(),Li.insertStyleElement=Ii(),vi()(_i(),Li);const Ni=_i()&&_i().locals?_i().locals:void 0;var Di,ji,Bi,zi,Wi,Fi,Ui,Gi,Vi,Hi,qi;function Qi(e,t,n){Ki(e,t),t.set(e,n)}function Ki(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function Xi(e,t,n){return n(Zi(e,t))}function Yi(e,t,n){return e.set(Zi(e,t),n),n}function Ji(e,t){return e.get(Zi(e,t))}function Zi(e,t,n){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:n;throw new TypeError("Private element is not present on this object")}function $i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function eo(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?$i(Object(n),!0).forEach((function(t){to(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):$i(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function to(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const no=self;function ro(e,t,n,r,i,o,a){var s,l,c,u,d,p,h,m,f,g,v,b,y,w,x,A,E,k,I,S,P,C,T;let M,O,R,_,L,N,D,j,B,z=[],W=!1,F=!1,U=!1,G="Unset";i.Utils.isArray;const V=i.Utils.isS,H=i.Utils.isFunction,{isNewReloadSite:q,useNewReloadTime:Q}=(e=>{const t=["garticphone.com","gartic.io","diep.io"].includes(e),n=Math.floor(100*Math.random());return{isNewReloadSite:t,useNewReloadTime:!!t&&n<50}})(n),X=Q?2e4:3e4,Y=i.Utils.setStyle,J=i.Utils.now,Z=i.Utils.bodyAvailable,$=e,ee=$.aiptag.events,te="Arial, Helvetica, sans-serif",ne="cmpPersistentLink",re={},ie={},oe=new WeakMap,ae=i.Instances.iframe.rootWindow.document,se=new i.AdServer.GAMAdserverIdFactory;se.networkId=null!==(s=null===(l=a.gam)||void 0===l?void 0:l.networkId)&&void 0!==s?s:null===(c=o.gam)||void 0===c?void 0:c.networkId,se.mcmId=null===(u=a.gam)||void 0===u?void 0:u.mcmId,i.Instances.landscapeConfigManager.meta.tenantId=null!==(d=null==o||null===(p=o.meta)||void 0===p?void 0:p.tenantId)&&void 0!==d?d:2,i.Instances.landscapeConfigManager.meta.accountId=null!==(h=null==a||null===(m=a.meta)||void 0===m?void 0:m.accountId)&&void 0!==h?h:0,i.Instances.landscapeConfigManager.meta.siteId=null!==(f=null==a||null===(g=a.meta)||void 0===g?void 0:g.siteId)&&void 0!==f?f:0,i.Instances.landscapeConfigManager.resetSessionOnVisibilityChange=!1,i.Slots.setDefaultPrebidVastCreativeIds(["************","************","************","************","************","************","************","************","************","************","************","************"]),r.reloadManager.enabled=!0,r.reloadManager.addDefaultRules=!1,r.reloadManager.requireFocus=!0;const le=new Set(["8ballbros.io","agario.cafe","agario.surf","agariotime.com","armbros.io","boxingbros.io","bulletz.io","classroom-6x.lol","computer-science.lol","dodgebros.io","fine-arts.lol","fishingbros.io","g-class.biz","grims.pro","huntingbros.io","kickbros.io","math-lessons.site","math-lessons.world","mmabros.io","paperio.pro","paperio3.com","paperio4.live","racingbros.io","tennisbros.io","worm.ist"]),ce=new Set(["eggshock.net","deathegg.life","egggames.best","mathactivity.club","geometry.pw","basketball.services","dunk.beauty","basketballtips.info","basketbros.net","fasterbasketball.com","dunk.wiki","dunk.guru","eggwarfare.com","eggshooter.com","shellgame.me","beetree.games","frivez.com","webgltest-17af1.web.app","ghp2smashkarts.github.io","ghp1tallteam.github.io"]),ue=[...le].some((e=>n===e)),de=[...ce].some((e=>$.location.hostname===e||$.location.hostname.endsWith("."+e)));if((ue||de)&&(se.mcmId=""),i.Instances.adblockDetector.then((()=>{R=i.Instances.adblockDetector.isAdBlocked;let e=new CustomEvent("adblockDetector",{detail:{isAdBlocked:i.Instances.adblockDetector.isAdBlocked}});ee.dispatchEvent(e)})),n.endsWith("coolgames.com")){try{window.__cmp=(pe=window.__cmp,function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return"getCMPData"===e?{purposeConsents:{1:!0,4:!0}}:pe&&pe(e,...n)})}catch(e){}ae.dispatchEvent(new CustomEvent("aip_consentapproved"))}var pe;$.aiptag.cmp=$.aiptag.cmp||{},$.aiptag.cmp=eo(eo({},{show:!0,buttonText:"Privacy settings",buttonPosition:"bottom-left"}),$.aiptag.cmp),void 0!==$.aiptag.gdprShowConsentToolButton&&($.aiptag.cmp.button=$.aiptag.gdprShowConsentToolButton);const he=["bloxd.io"].includes(n);he?(i.CMP.setConfig({cmpLoaders:[new i.CMP.CmLoader({timeout:1e3})]}),$.cmp_logoclick="https://adinplay.com/?utm_source=publishers&utm_medium=cmp",$.cmp_stayiniframe=1,$.cmp_target="_blank",void 0!==$.aiptag.gdprConsentToolPosition&&($.aiptag.cmp.position=$.aiptag.gdprConsentToolPosition),"bloxd"===$.aiptag.cmp.position?($.cmp_id=108901,$.cmp_cdid="4222c78c613aa",$.cmp_host="b.delivery.consentmanager.net"):"bottom"===$.aiptag.cmp.position?($.cmp_id=15920,$.cmp_cdid="0d3e4b95c303",$.cmp_host="a.delivery.consentmanager.net"):($.cmp_id=13566,$.cmp_cdid="abbb821549a1",$.cmp_host="c.delivery.consentmanager.net")):(i.CMP.setConfig({cmpLoaders:[new i.CMP.GoogleCmpLoader({timeout:{scriptLoaded:2600,apiReady:1300}})]}),i.CMP.GoogleCmpLoader.overrideDnsLink=!0),i.CMP.setGoogleNetworkCode(null===(v=o.gam)||void 0===v?void 0:v.networkId),i.AdServer.setGPTNetworkCodeByUrlParamsOnWindow(null===(b=o.gam)||void 0===b?void 0:b.networkId);const me=new Set;("garticphone.com"==n||"gartic.io"==n||"dev.shellshock.io"==$.location.hostname||"staging.shellshock.io"==$.location.hostname||$.aiptag.pageProtect)&&me.add(i.Instances.pageProtect),me.add(i.Instances.geolocation),null!=a&&null!==(y=a.abr)&&void 0!==y&&y.enabled&&(me.add(i.Instances.unblockManager),i.Bidder.bidderConfig.alwaysAcceptEmptyAdBlock=!0),me.add(i.Instances.CMPManager),me.add(i.Instances.botDetector),i.MainConfig.AdManager.MainModules.loadDefault=!1,i.MainConfig.AdManager.MainModules.modules=Array.from(me),function(){const e={};i.Instances.auctionManager.on("BIDDER_CONSTRUCTED",((e,t,n)=>{if("amazon"===e.bidder&&e.params&&e.params.adunitPath&&-1===e.params.adunitPath.indexOf("!")&&t.sizeConfig){const n={};t.sizeConfig.configId&&(n.pid=t.sizeConfig.configId,n.ab=R?"true":"false");const r=Object.entries(n).map((e=>{let[t,n]=e;return"!".concat(t,":").concat(n)})).join("");e.params.adunitPath+=r}}),!1);const t=t=>{t.callFast((()=>{i.Instances.iframe.isIframe&&(t.config.pageUrl=$.location.protocol+"//"+$.location.hostname);const e=()=>{t.pubads.setTargeting("config",["PR"]),i.Instances.iframe.isIframe?t.pubads.setTargeting("GS","Yes"):t.pubads.setTargeting("GS","No"),q&&t.pubads.setTargeting("refrate",Q?"new":"old")};if(e(),"bloxd.io"==n){const n=t.pubads.clearTargeting;t.pubads.clearTargeting=function(){n.apply(t.pubads,arguments),e()}}t.pubads.addEventListener("slotVisibilityChanged",(e=>{var t;const n=null==e||null===(t=e.slot)||void 0===t?void 0:t.getAdUnitPath();z[n]=e.inViewPercentage}))})),t.on("BEFORE_REQUEST",((t,n)=>{var r,o,s;let l=oe.get(n);var c;l?l.renders++:(l={renders:0},oe.set(n,l)),null==t||t.setTargeting("FC",[l.renders<10?"".concat(l.renders+1):"10plus"]),"display"===(null==n||null===(r=n.placement)||void 0===r?void 0:r.isAIPPlacement)&&(null!=n&&null!==(c=n.placement)&&void 0!==c&&c.isAutoRefreshed?null==t||t.setTargeting("Refr","Arefr"):null==t||t.setTargeting("Refr","Pubrefr")),$.aiptag.subid&&(null==t||t.setTargeting("subid",$.aiptag.subid)),null!=a&&null!==(o=a.richmediaIds)&&void 0!==o&&o.includes(null==n||null===(s=n.placement)||void 0===s||null===(s=s.activeSize)||void 0===s||null===(s=s.activeAdserverId)||void 0===s?void 0:s.placementId)&&(null==t||t.setTargeting("richmedia","yes")),t&&t.getAdUnitPath&&e[t.getAdUnitPath()]&&(null==t||t.setTargeting("Conc","Yes")),i.Instances.botDetector.isBot&&(null==t||t.setTargeting("isBot","yes")),null==t||t.setTargeting("CNSNT",G)}),!1)};i.AdServer.gamEvents.on("DETECTED_CONFIRMED_CLICK",((t,n)=>{n&&n.getAdUnitPath&&(e[n.getAdUnitPath()]=!0)}),!1);const o=i.AdServer.getGamInstances();for(const e of o)t(e);i.AdServer.gamEvents.on("CREATED_GAM_INSTANCE",(e=>{t(e)}),!1),i.Instances.auctionManager.installed((e=>{})),i.Instances.auctionManager.installed((e=>{try{var t,r;let o=["\n %c %c %c %c ✰ "+(null==a||null===(t=a.openRTB)||void 0===t||null===(t=t.schain)||void 0===t||null===(t=t.default)||void 0===t||null===(t=t.config)||void 0===t||null===(t=t.nodes)||void 0===t||null===(t=t[0])||void 0===t?void 0:t.sid)+" - "+n+" ✰ prosperlib v"+i.Utils.adManagerVersion()+" - prebid "+e.version+(se.mcmId?" - MCM "+se.mcmId:"")+" ✰  %c  %c  ads by https://www.adinplay.com/  %c %c \n","background-image: url(data:image/png;base64,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);padding:12px 57px;background-repeat:no-repeat;","background: #9C0013; padding:5px 0;","background: #9C0013; padding:5px 0;","color: #FFFFFF; background: #030307; padding:5px 0;","background: #9C0013; padding:5px 0;","color: #FFFFFF;background: #DB0028; padding:5px 0;","background: #9C0013; padding:5px 0;","background: #9C0013; padding:5px 0;"];const s=i.Utils.getPrivateLogger?i.Utils.getPrivateLogger():null!==(r=i.Utils.getSafeConsole())&&void 0!==r?r:self.console;s.log.apply(s,o)}catch(e){}}));try{(()=>{if($.aiptag.disableInterstitialAd)return;const e=Ge();if(e){const t=Object.keys(e.placements).filter((t=>{var n;return"interstitial"===(null===(n=e.placements[t])||void 0===n?void 0:n.placementType)}));if(!t||0===t.length)return;const n=(e,t)=>{var n,r,o,s,l,c,u;const d=new i.Placement.PlacementSizeConfig,p=null!==(n=e.config)&&void 0!==n?n:e;return d.configId=null!==(r=null==p?void 0:p.id)&&void 0!==r?r:0,d.name=null!==(o=null==e?void 0:e.name)&&void 0!==o?o:t,d.configuredSizes=null!==(s=p.sizes)&&void 0!==s?s:[],d.bidders=Array.isArray(null==p?void 0:p.bidders)?[...p.bidders]:null!==(l=null==p?void 0:p.bidders)&&void 0!==l?l:[],d.adserverId=new i.AdServer.GAMAdServerId,se.mcmId&&(d.adserverId.mcmId=se.mcmId),se.networkId&&(d.adserverId.networkId=se.networkId),null!==(c=p.adserver)&&void 0!==c&&c.id&&(d.adserverId.placementId=p.adserver.id),null!==(u=p.adserver)&&void 0!==u&&u.interstitialConfig&&(d.adserverId.interstitialConfig=p.adserver.interstitialConfig),d.slotRendererConfig=new i.Slots.PlacementSlotManagerConfig(d),d.slotRendererConfig.que=[{rendererType:i.Slots.GAMInterstitialSlot,enabled:!0}],d.slotRendererConfig.maxCPM=50,d.slotRendererConfig.acceptanceFloor=-1,e.breakpoint&&a.breakpoints&&e.breakpoint.name&&(d.responsiveRule=new i.Placement.PlacementSizeConfigResponsiveRule(a.breakpoints[e.breakpoint.name])),d},o=(e,t)=>e.sizeConfigs?e.sizeConfigs.map((e=>n(e,t))):[n(e,t)];t.forEach((t=>{if(t&&e.placements[t]){const n=e.placements[t];re[t]=r.addPlacement(o(n,t),{body:!0},null,(e=>{e.placementConfig.auction.requireVisibility=!1,e.placementConfig.auction.start="direct",e.placementConfig.load="direct",e.placementConfig.canReload=!1,e.isAIPPlacement="interstitial",e.on(i.Placement.PLACEMENT_EVENTS.RENDERED,(e=>{let t=new CustomEvent("interstitialAdAvailable");ee.dispatchEvent(t);let n=new CustomEvent("aip_interstitialadavailable");ae.dispatchEvent(n)}),!1)}))}}))}})()}catch(e){}}();var fe=new WeakSet,ge=new WeakMap,ve=new WeakMap,be=new WeakMap,ye=new WeakMap,we=new WeakMap,xe=new WeakMap,Ae=new WeakMap,Ee=new WeakMap;class ke extends i.PlacementRenderers.SizeConfigRenderer{constructor(){var e;super(...arguments),Ki(this,e=fe),e.add(this),Qi(this,ge,void 0),Qi(this,ve,void 0),Qi(this,be,void 0),Qi(this,ye,void 0),Qi(this,we,void 0),Qi(this,xe,0),Qi(this,Ae,void 0),Qi(this,Ee,void 0)}get containerElm(){return Ji(Ee,this)}get progress(){return Ji(ye,this)}get loading(){return Ji(we,this)}render(e){super.render(e),this.ownStyle={backgroundColor:"black"},Yi(ge,this,null==e?void 0:e.placement),this.setupListeners()}getAdserverSlot(){const e=super.getAdserverSlot();return Y(e,{width:"100%",height:"100%",display:"grid",gridTemplateRows:"1fr auto"}),this.setupDisplayElements(this.outDOM,e),e}setupListeners(){Ji(ge,this).on(i.Placement.PLACEMENT_EVENTS.SLOT_VIDEO_AD_PROGRESS,Zi(fe,this,Pe).bind(this),!1),Ji(ge,this).on(i.Placement.PLACEMENT_EVENTS.SLOT_VIDEO_AD_STARTED,Zi(fe,this,Se).bind(this),!1),Ji(ge,this).on(i.Placement.PLACEMENT_EVENTS.SLOT_CREATED_NEW_RENDERER,((e,t,n)=>{e.on("ad_paused",(()=>{clearTimeout(j)}),!1),e.on("created_display_elements",((e,t)=>{Yi(Ee,this,null==t?void 0:t.container),Ji(ve,this)&&Ji(ve,this).parentNode&&Ji(ve,this).parentNode.appendChild(Ji(ve,this)),Y(Ji(ve,this),{position:"relative"})}),!1)}),!1)}showSkipButtonOnTimeout(){clearTimeout(j),j=setTimeout((()=>{Ji(be,this)&&Ji(be,this).style&&(Ji(be,this).style.display="inline-block"),Yi(Ae,this,!0)}),7e3)}setupDisplayElements(e,t){var n,r,i;if(Yi(ve,this,t.appendChild(e.ownerDocument.createElement("div"))),Y(Ji(ve,this),{width:"100%",height:"32px",lineHeight:"32px",position:"absolute",backgroundColor:"black",borderTop:"1px solid white",left:0,right:0,bottom:0,zIndex:9999999999,overflow:"hidden"}),Yi(we,this,Ji(ve,this).appendChild(e.ownerDocument.createElement("span"))),Ji(we,this).id="adinplay-loading-text",Y(Ji(we,this),{color:"#fff",fontFamily:te,fontSize:"12px",position:"relative",paddingLeft:"10px"}),Ji(we,this).innerText=null!==(n=null===(r=Xi(fe,this,Ie))||void 0===r?void 0:r.loadingText)&&void 0!==n?n:"Loading Advertisment",Yi(ye,this,Ji(ve,this).appendChild(e.ownerDocument.createElement("span"))),Ji(ye,this).id="adinplay-progress-text",Y(Ji(ye,this),{color:"#fff",fontFamily:te,fontSize:"12px",position:"relative",paddingLeft:"10px",display:"none"}),Yi(be,this,Ji(ve,this).appendChild(e.ownerDocument.createElement("a"))),Y(Ji(be,this),{color:"#fff",fontFamily:te,fontSize:"12px",position:"relative",paddingLeft:"15px",display:"none",cursor:"pointer",backgroundImage:"url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAQAAABKfvVzAAAABGdBTUEAALGPC/xhBQAAAAJiS0dEAP+Hj8y/AAAAZElEQVQ4y+3SwQmDQBBGYRFy9mYLSRXWEyvRRtKBbdhDUoTH8HkVllX+u++48GD2zTTNTYiPrnhDXeBnyAT+Zo9EgNUrE9iMmQCLvhTas2jXWY8jvbNPP5OsU5L1my0uPY2bCjtXdo6mqRVtTgAAAABJRU5ErkJggg==)",backgroundRepeat:"no-repeat",backgroundPosition:"right center",paddingRight:"25px"}),Ji(be,this).innerText="Skip Ad",Ji(be,this).addEventListener("click",(()=>{var e,t,n;null!==(e=Ji(ge,this))&&void 0!==e&&null!==(e=e.activeSize)&&void 0!==e&&null!==(e=e.slotRenderManager)&&void 0!==e&&e.currentRenderer&&Ji(Ae,this)&&(null===(t=Ji(ge,this))||void 0===t||null===(t=t.activeSize)||void 0===t||null===(t=t.slotRenderManager)||void 0===t||null===(t=t.currentRenderer)||void 0===t||null===(n=t.skipAd)||void 0===n||n.call(t))})),null!==(i=Xi(fe,this,Ie))&&void 0!==i&&i.showAdInPlayLogo){var o;const t=Ji(ve,this).appendChild(e.ownerDocument.createElement("a"));null!==(o=Xi(fe,this,Ie))&&void 0!==o&&o.clickAdInPlayLogo&&(t.href="https://adinplay.com/?utm_source=publishers&utm_medium=websites&utm_campaign=PrerollVideo",t.setAttribute("target","_blank")),Y(t,{color:"rgba(255,255,255,0.6)",fontFamily:te,fontSize:"12px",float:"right",position:"relative",textDecoration:"none",marginRight:"6px",height:"32px",display:"flex",alignItems:"center"}),t.appendChild(e.ownerDocument.createElement("img")).src="https://api.adinplay.com/libs/aiptag/assets/powered-by-adinplay.png"}}destroy(){var e;super.destroy(),Ji(ve,this)&&(null===(e=Ji(ve,this).parentNode)||void 0===e||e.removeChild(Ji(ve,this)))}}function Ie(e){return e.placementRenderer.aipConfig}function Se(){var e,t;clearTimeout(N),this.showSkipButtonOnTimeout(),null===(e=_)||void 0===e||null===(e=e.parentNode)||void 0===e||null===(t=e.removeChild)||void 0===t||t.call(e,_)}function Pe(e,t){var n;clearTimeout(N),null===(n=Ji(we,this))||void 0===n||n.remove(),this.showSkipButtonOnTimeout();const r=t.getAdData();let o=r.currentTime;var a,s,l;o-Ji(xe,this)>10?(o=0,Yi(Ae,this,!1)):(Yi(xe,this,o),o>(null===(a=Xi(fe,this,Ie))||void 0===a?void 0:a.forceSkipAfterSeconds)+1?((null===(s=Ji(be,this))||void 0===s?void 0:s.style)&&(Ji(be,this).style.display="inline-block"),Yi(Ae,this,!0)):((null===(l=Ji(be,this))||void 0===l?void 0:l.style)&&(Ji(be,this).style.display="none"),Yi(Ae,this,!1)),i.Utils.toHHMMSS(o)>=i.Utils.toHHMMSS(r.duration)&&(clearTimeout(B),B=setTimeout((()=>{var e;(null===(e=Ji(be,this))||void 0===e?void 0:e.style)&&(Ji(be,this).style.display="inline-block"),Yi(Ae,this,!0)}),3e3)));o>0&&(Ji(ye,this).style.display="inline-block",Ji(ye,this).innerText=i.Utils.toHHMMSS(o)+" / "+i.Utils.toHHMMSS(r.duration))}class Ce extends i.PlacementRenderers.Normal{createOwnNode(){const e=super.createOwnNode();return Y(this.anchor,{display:"none",zIndex:4294967295,textAlign:"initial"}),this.ownStyle={position:"relative",display:"block",width:"100%",height:"100%",zIndex:4294967295,backgroundColor:"black"},e}getNewPlacementSizeConfigRenderer(e){return new ke(this)}}class Te extends Ce{set anchor(e){let t;try{var n;if(null!==(n=M)&&void 0!==n&&null!==(n=n())&&void 0!==n&&n.isConnected){let e=$.getComputedStyle(M(),null).getPropertyValue("position");""!=e&&"static"!=e||(t="relative")}}catch(e){}Y(e,eo({width:this.aipConfig.width,height:this.aipConfig.height},t?{position:t}:{})),super.anchor=e}get anchor(){return super.anchor}}class Me extends Ce{set anchor(e){e.className="",Y(e,{width:"100%",height:"100%",position:"relative",backgroundColor:"black"}),super.anchor=e}get anchor(){return super.anchor}}var Oe=new WeakMap,Re=new WeakMap;class _e extends Ce{constructor(){super(...arguments),Qi(this,Oe,960),Qi(this,Re,540)}setWidthHeight(e,t){Yi(Oe,this,e),Yi(Re,this,t)}createOwnNode(){const e=super.createOwnNode();return Y(this.anchor,{display:"none"}),this.ownStyle={position:"fixed",display:"block",zIndex:4294967295,left:"50%",top:"50%",marginLeft:-.5*Ji(Oe,this),marginTop:-.5*Ji(Re,this),backgroundColor:"black",width:Ji(Oe,this),height:Ji(Re,this)},e}}var Le,Ne=new WeakMap;class De extends _e{constructor(){super(...arguments),Qi(this,Ne,void 0)}createOwnNode(){const e=super.createOwnNode();return Y(this.anchor,{display:"none"}),Ji(Ne,this)||Yi(Ne,this,null==this?void 0:this.ownNode.ownerDocument.createElement("div")),this.anchor.insertAdjacentElement("afterend",Ji(Ne,this)),Y(Ji(Ne,this),{position:"fixed",display:"block",zIndex:1e6,left:0,right:0,bottom:0,top:0,backgroundColor:"rgba(0,0,0,0.8)"}),e}destroy(){var e;super.destroy(),Ji(Ne,this)&&(null===(e=Ji(Ne,this).parentNode)||void 0===e||e.removeChild(Ji(Ne,this)),Yi(Ne,this,null))}}class je extends Ce{createOwnNode(){var e;const t=super.createOwnNode();return null===(e=this.anchor.style)||void 0===e||e.setProperty("position","fixed","important"),this.anchor.className="",Y(this.anchor,{display:"none",width:"100%",height:"100%",transform:"none",overflow:"hidden",top:0,left:0}),this.ownStyle={position:"fixed",display:"block",zIndex:4294967295,left:0,right:0,bottom:0,top:0,backgroundColor:"black"},t}}function Be(e){var t;"display"===(null==e?void 0:e.isAIPPlacement)&&Y(null==e||null===(t=e.renderer)||void 0===t?void 0:t.anchor,{display:null})}function ze(){try{ae.getElementById(ne)||Z((e=>{let t=$.aiptag.cmp.buttonText,n="none";i.Instances.iframe.isIframe&&(!0!==i.Instances.iframe.isIframe||!0!==i.Instances.iframe.isFriendly)||!0!==$.aiptag.cmp.button||(n="block");let r=ae.createElement("div");e.appendChild(r),r.id=ne,r.style.display=n,r.className="cmp-persistent-link ".concat(Ni["cmp-persistent-link"]," ").concat(Ni[$.aiptag.cmp.buttonPosition]),r.innerHTML='\n                    <a class="'.concat(Ni["cmp-persistent-link-a"],'" href="#" tabindex="0" aria-haspopup="dialog" role="button" draggable="false" aria-label="').concat(t,'">\n                        <svg xmlns="http://www.w3.org/2000/svg" height="20px" viewBox="0 -960 960 960" width="20px" fill="#5f6368">\n                            <path d="m438-339 220-221-51-51-169 170-85-85-51 51 136 136Zm42 243q-135-33-223.5-152.84Q168-368.69 168-515v-229l312-120 312 120v229q0 146.31-88.5 266.16Q615-129 480-96Zm0-75q104-32.25 172-129t68-215v-180l-240-92-240 92v180q0 118.25 68 215t172 129Zm0-308Z"/>\n                        </svg>\n                        <span class="').concat(Ni["cmp-persistent-link-txt"],'">').concat(t,"</span>\n                    </a>\n                    "),r.addEventListener("click",(e=>{e.stopPropagation();try{$.aipAPItag.showCMPScreen()}catch(e){}}))}))}catch(e){}}null===(w=i.Debug)||void 0===w||null===(x=w.addCustomRendererMapping)||void 0===x||x.call(w,Te,"Default AdInPlay"),null===(A=i.Debug)||void 0===A||null===(E=A.addCustomRendererMapping)||void 0===E||E.call(A,_e,"Centered AdInPlay"),null===(k=i.Debug)||void 0===k||null===(I=k.addCustomRendererMapping)||void 0===I||I.call(k,De,"Modal AdInPlay"),null===(S=i.Debug)||void 0===S||null===(P=S.addCustomRendererMapping)||void 0===P||P.call(S,Me,"Fill AdInPlay"),null===(C=i.Debug)||void 0===C||null===(T=C.addCustomRendererMapping)||void 0===T||T.call(C,je,"Fullscreen AdInPlay"),r.reloadManager.customReloadRules=[e=>{var t,n,r;const i=null==e?void 0:e.identifier,o=null==e||null===(t=e.renderer)||void 0===t||null===(t=t.anchor)||void 0===t?void 0:t.isConnected;if((null==e||null===(n=e.activeSize)||void 0===n||null===(n=n.stats)||void 0===n||null===(n=n.visibility)||void 0===n?void 0:n.duration)>=X&&o&&(null!=ae&&ae.hasFocus()||null!==(r=document)&&void 0!==r&&r.hasFocus())&&(function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{horizontal:1,vertical:1};if(e){if(Dn(e))return 0;const r=e.ownerDocument;if(r.body===e||r.documentElement===e)return 1;let i=e.offsetHeight;i*=1;const o=Ft(Ft({},function(e){return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:Gn)(e)}(e,Vn)),Un(e,Wn)),a={x:o.x,y:o.y,w:o.width,h:o.height,t:0,b:0,l:0,r:0};t&&(a.w=t.width,a.h=t.height);const s=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:K.rootWindow.document;const t={width:0,height:0};return"BackCompat"===e.compatMode&&e.body&&(t.width=e.body.clientWidth,t.height=e.body.clientHeight),0==t.width&&0==t.height&&(t.width=e.documentElement.clientWidth,t.height=e.documentElement.clientHeight),0==t.width&&0==t.height&&(t.width=e.documentElement.clientWidth,t.height=e.documentElement.clientHeight),t}(r);if(n&&(n.vertical&&(s.height*=n.vertical),n.horizontal&&(s.width*=n.horizontal)),a.t=a.y,a.b=a.y+a.h,a.b<0)return 0;if(a.y<0&&(a.t=0,a.b=Math.max(0,a.h+a.y)),a.b>s.height&&(a.b=s.height),a.b<a.t)return 0;if(a.l=a.x,a.r=a.x+a.w,a.r<0)return 0;if(a.x<0&&(a.l=0,a.r=Math.max(0,a.w+a.x)),a.r>s.width&&(a.r=s.width),a.r<Math.floor(a.l))return 0;const l=(a.r-a.l)*(a.b-a.t),c=a.w*a.h;return c<=0?(!t||0===t.width||0===t.height)&&a.x>=0&&a.y>=0&&Math.floor(a.x)<=s.width&&Math.floor(a.y)<=s.height?1:0:l/c}}(e.renderer.anchor)>.5||z[i]>=50)&&e.activeSize.isExecutable)return!0}],r.on("PLACEMENT_RELOADED",(e=>{Be(e)}),!1),r.on("PLACEMENT_CREATED",(e=>{e.on("rendered",(e=>{Be(e.placement),e.placement}),!1)}),!1),(()=>{const e=(e,t)=>{var n,r,o,s;const l=new i.Placement.PlacementSizeConfig,c=null!==(n=e.config)&&void 0!==n?n:e;return l.configId=null!==(r=null==c?void 0:c.id)&&void 0!==r?r:0,l.name=null!==(o=null==e?void 0:e.name)&&void 0!==o?o:t,l.configuredSizes=c.sizes,l.bidders=Array.isArray(null==c?void 0:c.bidders)?[...c.bidders]:null!==(s=null==c?void 0:c.bidders)&&void 0!==s?s:[],l.adserverId=new i.AdServer.GAMAdServerId,se.mcmId&&(l.adserverId.mcmId=se.mcmId),se.networkId&&(l.adserverId.networkId=se.networkId),l.adserverId.placementId=c.adserver.id,l.slotRendererConfig=new i.Slots.PlacementSlotManagerConfig(l),l.slotRendererConfig.maxCPM=50,l.slotRendererConfig.acceptanceFloor=-1,l.slotRendererConfig.enableTAG=!1,e.breakpoint&&a.breakpoints&&e.breakpoint.name&&(l.responsiveRule=new i.Placement.PlacementSizeConfigResponsiveRule(a.breakpoints[e.breakpoint.name])),l},t=t=>{const n=Ge();if(n&&t&&n.placements[t]){const c=n.placements[t];var o;if(t in ie||(ie[t]=J()),re[t])if(null!==(o=re[t])&&void 0!==o&&null!==(o=o.activeSize)&&void 0!==o&&o.executing){var a;ie[t]=J(),null===(a=re[t])||void 0===a||null===(a=a.activeSize)||void 0===a||null===(a=a.executionContext)||void 0===a||a.check()}else{if(J()-ie[t]<5e3)return;ie[t]=J(),re[t].expectReload=!0,r.reload([re[t]])}else re[t]=r.addPlacement((l=t,(s=c).sizeConfigs?s.sizeConfigs.map((t=>e(t,l))):[e(s,l)]),{id:t,document},null,(e=>{var n,r;e.placementConfig.visibility.requireVisibility=!1,e.placementConfig.auction.requireVisibility=!1,e.placementConfig.auction.start="direct",e.placementConfig.load="direct",e.placementConfig.canReload=null===(n=null==c||null===(r=c.placementConfig)||void 0===r?void 0:r.canReload)||void 0===n||n,e.isAIPPlacement="display",e.on(i.Placement.PLACEMENT_EVENTS.EXECUTED,(n=>{null!=n&&n.renderedOnce&&!e.expectReload?(ie[t]=J(),e.isAutoRefreshed=!0):e.isAutoRefreshed=!1,e.expectReload=!1}),!1),(()=>{const n=e=>({current:new CustomEvent("slotRenderEnded",{detail:{adType:"display",slotId:t,isEmpty:e}}),legacy:new CustomEvent("aip_slotRenderEnded",{detail:{slotElementId:t,isEmpty:e}})});e.on(i.Placement.PLACEMENT_EVENTS.SLOT_RENDERED_INITIAL,(e=>{const t=n(!1);ee.dispatchEvent(t.current),ae.dispatchEvent(t.legacy)}),!1),e.on(i.Placement.PLACEMENT_EVENTS.SLOT_NOTHING_TO_RENDER,(e=>{const t=n(!0);ee.dispatchEvent(t.current),ae.dispatchEvent(t.legacy)}),!1)})()})),ie[t]=J()}var s,l},n=e=>{var t,n;if(V(e))e&&re[e]&&"display"===re[e].isAIPPlacement&&(null===(t=re[e].activeSize)||void 0===t||t.clearRendered(),delete z[null===(n=re[e])||void 0===n||null===(n=n.activeSize)||void 0===n||null===(n=n.activeAdserverId)||void 0===n?void 0:n.adUnitPath],ie[e]=void 0);else for(const e in re){var r,i;re.hasOwnProperty(e)&&"display"===re[e].isAIPPlacement&&(null===(r=re[e].activeSize)||void 0===r||r.clearRendered(),delete z[null===(i=re[e])||void 0===i||null===(i=i.activeSize)||void 0===i||null===(i=i.activeAdserverId)||void 0===i?void 0:i.adUnitPath],ie[e]=void 0)}};$.aipDisplayTag={display:e=>{t(e)},refresh:e=>{t(e)},setAutoRefresh:(e,t)=>{((e,t)=>{var n;"display"===(null===(n=re[e])||void 0===n?void 0:n.isAIPPlacement)&&(re[e].placementConfig.canReload=t)})(e,t)},destroy:e=>{n(e)},destroySlot:e=>{V(e)&&n(e)},clear:e=>{V(e)&&n(e)}}})(),$.aipPlayer=function(e){var t;this.aipConfig=e,this.config=this.aipConfig,null!=a&&null!==(t=a.devices)&&void 0!==t&&null!==(t=t.default)&&void 0!==t&&null!==(t=t.pages)&&void 0!==t&&null!==(t=t.known)&&void 0!==t&&null!==(t=t.default)&&void 0!==t&&null!==(t=t.settings)&&void 0!==t&&t.preroll&&Cn("https://imasdk.googleapis.com/js/sdkloader/ima3.js")},(Le=$.aipPlayer.prototype).startPreRoll=function(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(F){var o,s,l,c;if(null===(o=O)||void 0===o||null===(o=o())||void 0===o||null===(o=o.renderer)||void 0===o||null===(o=o.ownNode)||void 0===o?void 0:o.isConnected)return void(null===(s=console)||void 0===s||s.warn("***WARNING*** A preroll is already playing please wait for the preroll to finish"));null===(l=O)||void 0===l||null===(l=l())||void 0===l||null===(l=l.activeSize)||void 0===l||null===(l=l.slotRenderManager)||void 0===l||null===(l=l.currentRenderer)||void 0===l||l.destroy(),null===(c=O)||void 0===c||null===(c=c())||void 0===c||null===(c=c.renderer)||void 0===c||c.destroy()}F=!0,U=!1,clearTimeout(N),clearTimeout(L),clearTimeout(j),clearTimeout(B),M=()=>{const{PREROLL_ELEM:e}=this.config||{};return H(e)?e():null!=e&&e.id?ae.getElementById(e.id):e};const u=e=>{F&&(clearTimeout(N),clearTimeout(j),clearTimeout(B),F=!1,U=!1,setTimeout((()=>{try{var t,n,r;null===(t=O)||void 0===t||null===(t=t())||void 0===t||null===(t=t.renderer)||void 0===t||t.destroy(),null!==(n=M)&&void 0!==n&&null!==(n=n())&&void 0!==n&&n.isConnected&&null!==(r=M)&&void 0!==r&&null!==(r=r())&&void 0!==r&&r.style&&(M().style.display="none")}catch(e){}if(this.config&&this.config.AIP_COMPLETE)try{this.config.AIP_COMPLETE(e)}catch(e){}clearTimeout(L),this.config&&this.config.AIP_REMOVE&&(L=setTimeout((()=>{try{this.config.AIP_REMOVE(e)}catch(e){}}),2500))}),300))};var d;if(null===(e=M)||void 0===e||null===(e=e())||void 0===e||!e.isConnected)return null===(d=console)||void 0===d||d.warn("***WARNING*** PREROLL_ELEM does not exists, Preroll can't be played"),void u("preroll_elem-doesnt-exists");if(R)return void u("user-has-adblock");const p=Ge();if(p){var h;const e=()=>Object.entries(null==p?void 0:p.placements).filter((e=>{let[t,n]=e;return"video"===n.placementType})).sort(((e,t)=>{let[n,r]=e,[i,o]=t;return r.placementId-o.placementId})).map((e=>{let[t,n]=e;return t}))[0]||null,o="string"==typeof t?t:(null==t?void 0:t.adUnitName)||(null==a||null===(h=a.devices)||void 0===h||null===(h=h.default)||void 0===h||null===(h=h.pages)||void 0===h||null===(h=h.known)||void 0===h||null===(h=h.default)||void 0===h||null===(h=h.settings)||void 0===h?void 0:h.preroll)||e(),s="object"==typeof t&&!0===(null==t?void 0:t.autoPlay);if(o&&null!=p&&p.placements[o]&&"video"===(null==p?void 0:p.placements[o].placementType)){var m,f,g,v,b;const e=null==p?void 0:p.placements[o];null==e||null===(m=e.adserver)||void 0===m||m.id,D=(()=>{const e=document.documentElement.clientWidth||document.body.clientWidth||$.innerWidth,t=document.documentElement.clientHeight||document.body.clientHeight||$.innerHeight,n=function(){const e=null==Hn?void 0:Hn.device;return e?e.type?e.type:"desktop":""}();let r=e=>{};const i=(()=>0===this.config.AD_WIDTH||0===this.config.AD_HEIGHT||"mobile"===n&&!0!==this.config.AD_MOBILEFSOFF||e<=this.config.AD_WIDTH||t<=this.config.AD_HEIGHT?"fullscreen":this.config.AD_DISPLAY?this.config.AD_DISPLAY:this.config.AD_FULLSCREEN?"fullscreen":this.config.AD_CENTERPLAYER?this.config.AD_MODALCENTERPLAYER?"modal-center":"center":this.config.AD_FILL?"fill":"default")();switch(this.config.AD_DISPLAYED=i,i){case"fullscreen":return{renderer:new je,beforeRender:r};case"center":const e=new _e;return e.setWidthHeight(this.config.AD_WIDTH,this.config.AD_HEIGHT),{renderer:e,beforeRender:r};case"modal-center":const t=new De;return t.setWidthHeight(this.config.AD_WIDTH,this.config.AD_HEIGHT),{renderer:t,beforeRender:r};case"fill":return{renderer:new Me,beforeRender:r};default:return r=e=>{const t=e.placement.renderer.ownNode;Y(t,{width:this.config.AD_WIDTH,height:this.config.AD_HEIGHT,backgroundColor:"black"})},{renderer:new Te,beforeRender:r}}})();const t=!("TPZ"===(null==a||null===(f=a.openRTB)||void 0===f||null===(f=f.schain)||void 0===f||null===(f=f.default)||void 0===f||null===(f=f.config)||void 0===f||null===(f=f.nodes)||void 0===f||null===(f=f[0])||void 0===f?void 0:f.sid)||"amongusplay.online"===n),l=!("amongusplay.online"===n||"hole-io.com"===n),c={"gartic.io_preroll":5,"garticphone.com_preroll":5,"rummikub-apps.com_preroll":5,"ferge.io_preroll":5,"songtrivia2.io_preroll":15,"bloxd.io_preroll":20,"basketbros.io_preroll_15s":15}[o],d={"bloxd.io_preroll":2e4,"basketbros.io_preroll_15s":15e3}[o],h=D.renderer;if(h.aipConfig={width:null===(g=this.config)||void 0===g?void 0:g.AD_WIDTH,height:null===(v=this.config)||void 0===v?void 0:v.AD_HEIGHT,loadingText:null===(b=this.config)||void 0===b?void 0:b.LOADING_TEXT,forceSkipAfterSeconds:null!=c?c:30,showAdInPlayLogo:l,clickAdInPlayLogo:t,showTimer:!0},re[o]){try{re[o].renderer=h}catch(e){}r.reload([re[o]])}else{const t=(e,t)=>{var n,r,o,s,l,c;const u=new i.Placement.PlacementSizeConfig,p=null!==(n=e.config)&&void 0!==n?n:e,h=null===(r=p.adserver)||void 0===r?void 0:r.id;return u.configId=null!==(o=null==p?void 0:p.id)&&void 0!==o?o:0,u.name=null!==(s=null==e?void 0:e.name)&&void 0!==s?s:t,u.configuredSizes=null!==(l=p.sizes)&&void 0!==l?l:[],u.bidders=Array.isArray(null==p?void 0:p.bidders)?[...p.bidders]:null!==(c=null==p?void 0:p.bidders)&&void 0!==c?c:[],u.adserverId=new i.AdServer.IMAAdServerId,u.adserverId=se.createIMA(h,i.AdServer.parseSizeConstruct("960x540,640x480")),se.mcmId&&(u.adserverId.mcmId=se.mcmId),se.networkId&&(u.adserverId.networkId=se.networkId),e.breakpoint&&a.breakpoints&&e.breakpoint.name&&(u.responsiveRule=new i.Placement.PlacementSizeConfigResponsiveRule(a.breakpoints[e.breakpoint.name])),u.placementAdUnitBuilder.getSeedAdUnit=e=>{var t,n;const r=new i.AdUnit.AdUnit;return null!==(n=(t=r.mediaTypes).video)&&void 0!==n||(t.video=new i.AdUnit.AdUnitMediaTypeVideo),r.mediaTypes.video.useCacheKey=!1,r.mediaTypes.video.context="instream",r.mediaTypes.video.playerSize=[960,540],r.mediaTypes.video.mimes=["video/mp4","video/webm","application/javascript"],r.mediaTypes.video.protocols=[2,3,5,6,7,8],r.mediaTypes.video.api=[1,2],r.mediaTypes.video.linearity=1,r.mediaTypes.video.skip=1,r.mediaTypes.video.plcmt=1,r.mediaTypes.video.startdelay=0,r.mediaTypes.video.maxduration=(null!=d?d:3e4)/1e3<1?30:(null!=d?d:3e4)/1e3,r.mediaTypes.video.minduration=1,r.mediaTypes.video.playbackmethod=[3],r},u},n=(e,n)=>e.sizeConfigs?e.sizeConfigs.map((e=>t(e,n))):[t(e,n)],l=Object.keys(re).filter((e=>e!==o)).find((e=>"preroll"===re[e].isAIPPlacement));l&&(r.removePlacement(re[l]),delete re[l]),re[o]=r.addPlacement(n(e,o),{func:M},h,(e=>{O=()=>e||null,e.placementConfig.visibility.requireVisibility=!1,e.placementConfig.visibility.requireFocus=!1,e.placementConfig.auction.requireVisibility=!1,e.placementConfig.auction.start="direct",e.placementConfig.load="direct",e.placementConfig.canReload=!1,e.isAIPPlacement="preroll";const t=(e,t)=>{u(e)};let n;i.Instances.iframe.isIframe&&(n=$.location.protocol+"//"+$.location.hostname),null!=e&&e.activeSize&&(e.activeSize.slotRendererConfig=new i.Slots.PlacementSlotManagerConfig(e.activeSize),e.activeSize.slotRendererConfig.que=[{enabled:!0,rendererType:i.Slots.GAMVideoSlot,config:{ima:{pageUrl:n,recheckAutoPlay:!0,showPlayButtonOnMutedPlay:!1,syncIMAAutoPlay:s,adWillAutoPlay:s,checkAutoplaySupport:!0,muted:!1,viewModeFullscreen:"fullscreen",showPlayButtonOnPause:!0,allowAutoResume:!1}}}],e.activeSize.slotRendererConfig.autoImaSlot=!1),e.on(i.Placement.PLACEMENT_EVENTS.BEFORE_RENDER,(e=>{var t,n,r;let o=oe.get(e);o?o.renders++:(o={renders:0},oe.set(e,o));const a={FC:[o.renders<10?"".concat(o.renders+1):"10plus"],GS:[i.Instances.iframe.isIframe?"yes":"no"],CNSNT:[G],config:["PR"]};if($.aiptag.subid&&(a.subid=[$.aiptag.subid]),H(null===(t=this.config)||void 0===t?void 0:t.PREROLL_ELEM)||(a.prefunc=["no"]),e.adserverId.keyValues=a,e.adserverId.plcmt=1,e.adserverId.maxDuration=null!=d?d:3e4,e.adserverId.adRule=!0,this.config&&this.config.AIP_STARTED)try{this.config.AIP_STARTED()}catch(e){}null===(n=D)||void 0===n||null===(r=n.beforeRender)||void 0===r||r.call(n,e)}),!1);const r=n=>{n.placement.renderer.outDOM.querySelector(".".concat(Oi["pr-spinner-loader"]))||(n.placement.renderer.anchor.style.display="block",_=ae.createElement("span"),_.className=Oi["pr-spinner-loader"],n.placement.renderer.outDOM.prepend(_)),clearTimeout(N),N=setTimeout((()=>{var n;t("video-ad-timeout"),null==e||null===(n=e.activeSize)||void 0===n||null===(n=n.slotRenderManager)||void 0===n||null===(n=n.currentRenderer)||void 0===n||n.destroy()}),1e4)};e.on(i.Placement.PLACEMENT_EVENTS.RENDERER_PREPARED,(e=>{r(e)}),!1),e.on(i.Placement.PLACEMENT_EVENTS.SLOT_VIDEO_AD_SKIPPED,(()=>{t("video-ad-skipped")}),!1),e.on(i.Placement.PLACEMENT_EVENTS.CLOSED,(()=>{t("video-ad-completed")}),!1),e.on(i.Placement.PLACEMENT_EVENTS.SLOT_VIDEO_AD_ERROR,(()=>{t("video-ad-error")}),!1),e.on(i.Placement.PLACEMENT_EVENTS.SLOT_NOTHING_TO_RENDER,(()=>{t("video-ad-empty")}),!1),e.on(i.Placement.PLACEMENT_EVENTS.SLOT_CREATED_NEW_RENDERER,((n,i,o)=>{n.on("pending_play",(()=>{var t,n;clearTimeout(N),null===(t=_)||void 0===t||null===(t=t.parentNode)||void 0===t||null===(n=t.removeChild)||void 0===n||n.call(t,_),e&&e.activeSize&&e.activeSize.renderer&&e.activeSize.renderer.loading&&(e.activeSize.renderer.loading.innerText="Click the play icon to continue")}),!1),n.on("admanager_started",(t=>{var n;!1===(null==t?void 0:t.autoplayAllowed)&&(r(o),e&&e.activeSize&&e.activeSize.renderer&&e.activeSize.renderer.loading&&(e.activeSize.renderer.loading.innerText=null===(n=this.config)||void 0===n?void 0:n.LOADING_TEXT))}),!1),n.on("all_ads_completed",(()=>{t("video-ad-completed")}),!1),n.on("error",(()=>{t("video-ad-error")}),!1)}),!1)}))}}else u("video-ad-error")}else u("video-ad-error")},Le.startVideoAd=Le.startPreRoll,Le.showRewardedAd=()=>{},Le.startRewardedAd=(()=>{const e="divFullscreenLoading";let t=ae.getElementById(e);return function(n){var o,s;$.aipAPItag.aipRewardCompleteExec=null;const l=null!==(o=null==n?void 0:n.preload)&&void 0!==o&&o,c=null!==(s=null==n?void 0:n.showLoading)&&void 0!==s&&s,u=this,d=e=>{if($.aipAPItag.aipRewardCompleteExec)return;$.aipAPItag.aipRewardCompleteExec=Date.now(),null!=u&&u.config.AIP_REWARDEDCOMPLETE&&u.config.AIP_REWARDEDCOMPLETE(e),null!=u&&u.config.AIP_REWARDEDNOTGRANTED&&u.config.AIP_REWARDEDNOTGRANTED(e);let t=new CustomEvent("rewardedNotGranted",{detail:{state:e}});ee.dispatchEvent(t)},p=Ge();let h;if(p&&(h=Object.keys(p.placements).filter((e=>{var t;return"video reward"===(null===(t=p.placements[e])||void 0===t?void 0:t.placementType)}))),p&&h&&0!==h.length){const o=(e,t)=>{var n,r,o,s,l;const c=new i.Placement.PlacementSizeConfig,u=null!==(n=e.config)&&void 0!==n?n:e;return c.configId=null!==(r=null==u?void 0:u.id)&&void 0!==r?r:0,c.name=null!==(o=null==e?void 0:e.name)&&void 0!==o?o:t,c.configuredSizes=null!==(s=u.sizes)&&void 0!==s?s:[],c.adserverId=new i.AdServer.GAMAdServerId,se.mcmId&&(c.adserverId.mcmId=se.mcmId),se.networkId&&(c.adserverId.networkId=se.networkId),null!==(l=u.adserver)&&void 0!==l&&l.id&&(c.adserverId.placementId=u.adserver.id),c.slotRendererConfig=new i.Slots.PlacementSlotManagerConfig(c),c.slotRendererConfig.que=[{rendererType:i.Slots.GAMRewardedSlot,enabled:!0}],c.slotRendererConfig.maxCPM=50,c.slotRendererConfig.acceptanceFloor=-1,e.breakpoint&&a.breakpoints&&e.breakpoint.name&&(c.responsiveRule=new i.Placement.PlacementSizeConfigResponsiveRule(a.breakpoints[e.breakpoint.name])),c},s=(e,t)=>e.sizeConfigs?e.sizeConfigs.map((e=>o(e,t))):[o(e,t)];h.forEach((o=>{if(o&&p.placements[o]){const a=p.placements[o];let h;$.aipAPItag.aipRewardCompleteExec=null;const m=function(n){var r,i;!n||arguments.length>1&&void 0!==arguments[1]&&!arguments[1]?t&&(null===(r=t.parentNode)||void 0===r||null===(i=r.removeChild)||void 0===i||i.call(r,t)):(V(t)||(t=ae.createElement("div"),t.id=e,Y(t,{overflow:"hidden",position:"fixed",zIndex:999997,margin:"auto",top:0,left:0,bottom:0,right:0,width:"100%",height:"100%",backgroundColor:"#000000"}),t.innerHTML='<span class="'.concat(Oi["pr-spinner-loader"],'"></span>')),Z((e=>{e.appendChild(t)})))};re[o]=r.addPlacement(s(a,o),{body:!0},null,(e=>{e.placementConfig.visibility.requireVisibility=!1,e.placementConfig.visibility.requireFocus=!1,e.placementConfig.auction.requireVisibility=!1,e.placementConfig.auction.start="direct",e.placementConfig.load="direct",e.placementConfig.canReload=!1,e.isAIPPlacement="rewarded",!0!==l&&(n?c&&m(!0,!0):m(!0,!0),clearTimeout(h),h=setTimeout((()=>{m(!1,!1),d("timeout"),r.removePlacement(e)}),5e3));const t=e=>{if(clearTimeout(h),l){Le.showRewardedAd=()=>{e.makeRewardedVisible(),Le.showRewardedAd=()=>{}};let t=new CustomEvent("rewardedSlotReady",{detail:{isEmpty:!1}});ee.dispatchEvent(t)}else e.makeRewardedVisible();m(!1,!1)},o=t=>{clearTimeout(h),d("closed"),t.cleanup(),r.removePlacement(e)},a=(t,n)=>{t.cleanup(),r.removePlacement(e),null!=u&&u.config.AIP_REWARDEDGRANTED&&u.config.AIP_REWARDEDGRANTED(n);let i=new CustomEvent("rewardedGranted",{detail:{state:n}});ee.dispatchEvent(i)},s=(t,n)=>{if(clearTimeout(h),n.isEmpty){if(l){let e=new CustomEvent("rewardedSlotReady",{detail:{isEmpty:!0}});ee.dispatchEvent(e)}else d("empty");r.removePlacement(e)}m(!1,!1)};e.on(i.Placement.PLACEMENT_EVENTS.SLOT_CREATED_NEW_RENDERER,((e,n,r)=>{e instanceof i.Slots.GAMRewardedSlot&&(e.onUnique("gam_reward_slot_ready",t,!1),e.onUnique("gam_reward_slot_closed",o,!1),e.onUnique("gam_reward_slot_granted",a,!1),e.onUnique("gam_slot_rendered",s,!1))}),!1)}))}}))}else if(l){let e=new CustomEvent("rewardedSlotReady",{detail:{isEmpty:!0}});ee.dispatchEvent(e)}else d("empty")}})(),(e=>{e.showCMPScreen=function(){try{i.Instances.CMPManager.openConsentUI()}catch(e){}},e.showCMPButton=function(){Y(ae.getElementById(ne),{display:"block"})},e.hideCMPButton=function(){Y(ae.getElementById(ne),{display:"none"})}})($.aipAPItag),i.Instances.CMPManager.on("ui_opened",(()=>{let e;e=new CustomEvent("aip_consentscreen"),ae.dispatchEvent(e)}),!1),i.Instances.CMPManager.on("ui_closed",(()=>{let e;e=new CustomEvent("aip_consentscreenoff"),ae.dispatchEvent(e)}),!1),i.Instances.CMPManager.on("consent_updated",(()=>{let e,t;const n=i.Instances.CMPManager.legalType;if(W||"none"!=n&&"unknown"!=n)if("CPRA"==n){var r;G="CPRA";const t=null===(r=i.Instances.CMPManager.gppConsent)||void 0===r||null===(r=r.usnat)||void 0===r?void 0:r[0];if(t){const{SaleOptOut:n,SharingOptOut:r,TargetedAdvertisingOptOut:i}=t;1===n&&1===r&&1===i?$.aipAPItag.hideCMPButton():2===n&&2===r&&2===i&&($.aiptag.cmp.buttonText="Do Not Sell or Share My Personal Information",ze()),e=new CustomEvent("aip_consent"),ae.dispatchEvent(e)}}else if("GDPR"==n){ze(),e=new CustomEvent("aip_consent"),ae.dispatchEvent(e);const n=i.Instances.CMPManager.tcfConsent;n&&([1,2,3,4,5,6,7,8,9,10,11].every((e=>n.includes(e)))?(G="Yes",t=new CustomEvent("aip_consentapproved"),ae.dispatchEvent(t)):(G="No",t=new CustomEvent("aip_consentrejected"),ae.dispatchEvent(t)))}else"LGPD"==n&&(G="LGPD");else W=!0,G="None",t=new CustomEvent("aip_consentnotrequired"),ae.dispatchEvent(t)}),!1);const We=(()=>{try{return i.Utils.getLogger("aipQueue")}catch(e){return console}})();function Fe(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];for(const e of t)try{e()}catch(e){try{We.error(e)}catch(e){}}}const Ue=e=>{e.push=Fe,Fe(...e.splice(0,e.length))};function Ge(e,t){var n,r;if(null!=e||(e="default"),null!=t||(t="default"),null!=a&&null!==(n=a.devices)&&void 0!==n&&null!==(n=n[e])&&void 0!==n&&null!==(n=n.pages)&&void 0!==n&&null!==(n=n.known)&&void 0!==n&&n[t])return null==a||null===(r=a.devices[e])||void 0===r||null===(r=r.pages)||void 0===r?void 0:r.known[t]}if(Ue($.aiptag.cmd.load),Ue($.aiptag.cmd.display),Ue($.aiptag.cmd.player),Ue($.aiptag.cmd),he){const e=i.CMP.getConfig();e&&!e.cmpLoaders.every((e=>"consentmanager"!==e.name))||null==(Ve=["__cmpconsentx13566","__cmpcccx13566","__cmpcvcu13566","__cmpcpcu13566","__cmpcccu13566","__cmpwelu13566","__cmpcvcx13566","__cmpcpc13566","__cmpwel13566","euconsent-v2"])||Ve.forEach((e=>{$.document.cookie=e+"=; expires=Thu, 01 Jan 1970 00:00:00 GMT; domain="+ae.location.hostname+";path=/"}))}var Ve,He;return He={aiptag:$.aiptag},function(e){e&&e(He)}}null!==(Di=no.aiptag)&&void 0!==Di||(no.aiptag={}),null!==(Bi=(ji=no.aiptag).cmd)&&void 0!==Bi||(ji.cmd=[]),null!==(Wi=(zi=no.aiptag.cmd).load)&&void 0!==Wi||(zi.load=[]),null!==(Ui=(Fi=no.aiptag.cmd).display)&&void 0!==Ui||(Fi.display=[]),null!==(Vi=(Gi=no.aiptag.cmd).player)&&void 0!==Vi||(Gi.player=[]),no.aiptag.adplayer="",no.aiptag.settings="",no.aiptag.events=no.document.createElement("aip"),null!==(Hi=no.aipAPItag)&&void 0!==Hi||(no.aipAPItag={}),null!==(qi=no.aipPlayer)&&void 0!==qi||(no.aipPlayer=function(){});const io=JSON.parse('{"USD":{"EUR":0.95,"GBP":0.79}}');function oo(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ao(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?oo(Object(n),!0).forEach((function(t){so(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):oo(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function so(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const lo=fetch("https://hb-vntsm-com.global.ssl.fastly.net/v4/srv/g.txt",{priority:"high",cache:"no-store"});K.rootWindow.__VM=K.rootWindow.__VM||[],K.rootWindow.__VM.defaultGeoResolver=lo,K.rootWindow.__VM.unshift((function(e,t){try{var n,r;i="default1",null!=fi&&fi[i]&&(null==fi||null===(o=fi[i])||void 0===o||o.call(fi)),mi||(mi=(null==ur||null===(a=ur.getValueAsJson("debugger-flag-map"))||void 0===a?void 0:a.storedValue)||{}),mi[i]=mi[i]||!1,mi[i];const s="smashkarts.io",l=(e,n)=>t.Utils.isS(e)||t.Utils.isS(n)?t.Utils.merge(ao({},e),ao({},n)):null;(()=>{const e=l(null==Qn?void 0:Qn.bidders,null==qn?void 0:qn.bidders);for(const n in e){const r=e[n];t.Bidder.bidderConfig.addConfig(n,r)}})(),((e,n,r,i,o,a,s,c)=>{const u=new t.Auction.AuctionConfiguration;u.a9=l(null==Qn?void 0:Qn.a9,null==qn?void 0:qn.a9),u.legal=l(null==Qn||null===(e=Qn.auction)||void 0===e?void 0:e.legal,null==qn||null===(n=qn.auction)||void 0===n?void 0:n.legal),u.identity=l(null==Qn||null===(r=Qn.auction)||void 0===r?void 0:r.identity,null==qn||null===(i=qn.auction)||void 0===i?void 0:i.identity),u.currency=l(null==Qn||null===(o=Qn.auction)||void 0===o?void 0:o.currency,null==qn||null===(a=qn.auction)||void 0===a?void 0:a.currency),null!==(c=(s=u.currency).defaultRates)&&void 0!==c||(s.defaultRates=io),((e,n)=>{const r=l(null==Qn||null===(e=Qn.openRTB)||void 0===e?void 0:e.schain,null==qn||null===(n=qn.openRTB)||void 0===n?void 0:n.schain);var i;if(r.default&&(null!==(i=u.schain)&&void 0!==i||(u.schain={}),u.schain.default=new t.OpenRTB.SChainConfiguration(r.default)),r.bidders)for(const e in r.bidders){var o;null!==(o=u.schain)&&void 0!==o||(u.schain={}),u.schain.bidders||(u.schain.bidders={}),u.schain.bidders[e]=new t.OpenRTB.SChainConfiguration(r.bidders[e])}})(),t.Instances.auctionManager.setConfiguration(u)})(),((e,n)=>{const r=new t.Auction.PlacementAuctionConfiguration({bias:l(null==Qn||null===(e=Qn.auction)||void 0===e?void 0:e.bias,null==qn||null===(n=qn.auction)||void 0===n?void 0:n.bias)});r.maxTier=1,r.maxAuctionTime=1500,t.Instances.auctionManager.setDefaultPlacementAuctionConfiguration(r)})();try{ro(self,K.rootWindow.__VM,s,e,t,Qn,qn)((e=>{}))}catch(e){}null===(n=(r=t.Instances).setApiConfigured)||void 0===n||n.call(r)}catch(e){}var i,o,a})),K.rootWindow.__VM.loaded||function(e){var t;let n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null===(t=K.rootWindow)||void 0===t?void 0:t.document,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{head:!0},o=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{};a=Ft({importance:"high",fetchpriority:"high"},a),Cn(e,n,r,i,o,a)}("https://api.adinplay.com/v4/live/aip/ad-manager.js")})()})();