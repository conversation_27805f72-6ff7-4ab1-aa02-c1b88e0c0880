(function(window,document){var n;function aa(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}var ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};function da(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");}var ea=da(this);function p(a,b){if(b)a:{var c=ea;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&ba(c,a,{configurable:!0,writable:!0,value:b})}}p("Symbol",function(a){function b(f){if(this instanceof b)throw new TypeError("Symbol is not a constructor");return new c(d+(f||"")+"_"+e++,f)}function c(f,g){this.g=f;ba(this,"description",{configurable:!0,writable:!0,value:g})}if(a)return a;c.prototype.toString=function(){return this.g};var d="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",e=0;return b});p("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=ea[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&ba(d.prototype,a,{configurable:!0,writable:!0,value:function(){return fa(aa(this))}})}return a});p("Symbol.asyncIterator",function(a){return a?a:Symbol("Symbol.asyncIterator")});function fa(a){a={next:a};a[Symbol.iterator]=function(){return this};return a}var ha=typeof Object.create=="function"?Object.create:function(a){function b(){}b.prototype=a;return new b},ia;if(typeof Object.setPrototypeOf=="function")ia=Object.setPrototypeOf;else{var ja;a:{var ka={a:!0},la={};try{la.__proto__=ka;ja=la.a;break a}catch(a){}ja=!1}ia=ja?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var ma=ia;function y(a,b){a.prototype=ha(b.prototype);a.prototype.constructor=a;if(ma)ma(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Gg=b.prototype}function z(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");}function A(a){if(!(a instanceof Array)){a=z(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a}function na(a){return oa(a,a)}function oa(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a}function pa(a,b){return Object.prototype.hasOwnProperty.call(a,b)}var qa=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)pa(d,e)&&(a[e]=d[e])}return a};p("Object.assign",function(a){return a||qa});function ra(){this.A=!1;this.o=null;this.h=void 0;this.g=1;this.m=this.l=0;this.G=this.j=null}function sa(a){if(a.A)throw new TypeError("Generator is already running");a.A=!0}ra.prototype.D=function(a){this.h=a};function ta(a,b){a.j={He:b,Re:!0};a.g=a.l||a.m}ra.prototype.return=function(a){this.j={return:a};this.g=this.m};function ua(a,b,c){a.g=c;return{value:b}}ra.prototype.Ja=function(a){this.g=a};function va(a,b,c){a.l=b;c!=void 0&&(a.m=c)}function wa(a){a.l=0;var b=a.j.He;a.j=null;return b}function xa(a,b,c,d){d?a.G[d]=a.j:a.G=[a.j];a.l=b||0;a.m=c||0}function ya(a,b,c){c=a.G.splice(c||0)[0];(c=a.j=a.j||c)?c.Re?a.g=a.l||a.m:c.Ja!=void 0&&a.m<c.Ja?(a.g=c.Ja,a.j=null):a.g=a.m:a.g=b}ra.prototype.forIn=function(a){return new za(a)};function za(a){this.g=[];for(var b in a)this.g.push(b);this.g.reverse()}function Aa(a){this.g=new ra;this.h=a}function Ba(a,b){sa(a.g);var c=a.g.o;if(c)return Ca(a,"return"in c?c["return"]:function(d){return{value:d,done:!0}},b,a.g.return);a.g.return(b);return Da(a)}function Ca(a,b,c,d){try{var e=b.call(a.g.o,c);if(!(e instanceof Object))throw new TypeError("Iterator result "+e+" is not an object");if(!e.done)return a.g.A=!1,e;var f=e.value}catch(g){return a.g.o=null,ta(a.g,g),Da(a)}a.g.o=null;d.call(a.g,f);return Da(a)}function Da(a){for(;a.g.g;)try{var b=a.h(a.g);if(b)return a.g.A=!1,{value:b.value,done:!1}}catch(c){a.g.h=void 0,ta(a.g,c)}a.g.A=!1;if(a.g.j){b=a.g.j;a.g.j=null;if(b.Re)throw b.He;return{value:b.return,done:!0}}return{value:void 0,done:!0}}function Ea(a){this.next=function(b){sa(a.g);a.g.o?b=Ca(a,a.g.o.next,b,a.g.D):(a.g.D(b),b=Da(a));return b};this.throw=function(b){sa(a.g);a.g.o?b=Ca(a,a.g.o["throw"],b,a.g.D):(ta(a.g,b),b=Da(a));return b};this.return=function(b){return Ba(a,b)};this[Symbol.iterator]=function(){return this}}function Fa(a){function b(d){return a.next(d)}function c(d){return a.throw(d)}return new Promise(function(d,e){function f(g){g.done?d(g.value):Promise.resolve(g.value).then(b,c).then(f,e)}f(a.next())})}function Ga(a){return Fa(new Ea(new Aa(a)))}function Ha(a){this[Symbol.asyncIterator]=function(){return this};this[Symbol.iterator]=function(){return a};this.next=function(b){return Promise.resolve(a.next(b))};this["throw"]=function(b){return new Promise(function(c,d){var e=a["throw"];e!==void 0?c(e.call(a,b)):(c=a["return"],c!==void 0&&c.call(a),d(new TypeError("no `throw` method")))})};a["return"]!==void 0&&(this["return"]=function(b){return Promise.resolve(a["return"](b))})}function C(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b}p("globalThis",function(a){return a||ea});p("Reflect.setPrototypeOf",function(a){return a?a:ma?function(b,c){try{return ma(b,c),!0}catch(d){return!1}}:null});p("Promise",function(a){function b(g){this.h=0;this.j=void 0;this.g=[];this.A=!1;var h=this.l();try{g(h.resolve,h.reject)}catch(k){h.reject(k)}}function c(){this.g=null}function d(g){return g instanceof b?g:new b(function(h){h(g)})}if(a)return a;c.prototype.h=function(g){if(this.g==null){this.g=[];var h=this;this.j(function(){h.m()})}this.g.push(g)};var e=ea.setTimeout;c.prototype.j=function(g){e(g,0)};c.prototype.m=function(){for(;this.g&&this.g.length;){var g=this.g;this.g=[];for(var h=0;h<g.length;++h){var k=g[h];g[h]=null;try{k()}catch(l){this.l(l)}}}this.g=null};c.prototype.l=function(g){this.j(function(){throw g;})};b.prototype.l=function(){function g(l){return function(m){k||(k=!0,l.call(h,m))}}var h=this,k=!1;return{resolve:g(this.da),reject:g(this.m)}};b.prototype.da=function(g){if(g===this)this.m(new TypeError("A Promise cannot resolve to itself"));else if(g instanceof b)this.ja(g);else{a:switch(typeof g){case "object":var h=g!=null;break a;case "function":h=!0;break a;default:h=!1}h?this.V(g):this.o(g)}};b.prototype.V=function(g){var h=void 0;try{h=g.then}catch(k){this.m(k);return}typeof h=="function"?this.ta(h,g):this.o(g)};b.prototype.m=function(g){this.D(2,g)};b.prototype.o=function(g){this.D(1,g)};b.prototype.D=function(g,h){if(this.h!=0)throw Error("Cannot settle("+g+", "+h+"): Promise already settled in state"+this.h);this.h=g;this.j=h;this.h===2&&this.ea();this.G()};b.prototype.ea=function(){var g=this;e(function(){if(g.R()){var h=ea.console;typeof h!=="undefined"&&h.error(g.j)}},1)};b.prototype.R=function(){if(this.A)return!1;var g=ea.CustomEvent,h=ea.Event,k=ea.dispatchEvent;if(typeof k==="undefined")return!0;typeof g==="function"?g=new g("unhandledrejection",{cancelable:!0}):typeof h==="function"?g=new h("unhandledrejection",{cancelable:!0}):(g=ea.document.createEvent("CustomEvent"),g.initCustomEvent("unhandledrejection",!1,!0,g));g.promise=this;g.reason=this.j;return k(g)};b.prototype.G=function(){if(this.g!=null){for(var g=0;g<this.g.length;++g)f.h(this.g[g]);this.g=null}};var f=new c;b.prototype.ja=function(g){var h=this.l();g.Lc(h.resolve,h.reject)};b.prototype.ta=function(g,h){var k=this.l();try{g.call(h,k.resolve,k.reject)}catch(l){k.reject(l)}};b.prototype.then=function(g,h){function k(t,r){return typeof t=="function"?function(w){try{l(t(w))}catch(u){m(u)}}:r}var l,m,q=new b(function(t,r){l=t;m=r});this.Lc(k(g,l),k(h,m));return q};b.prototype.catch=function(g){return this.then(void 0,g)};b.prototype.Lc=function(g,h){function k(){switch(l.h){case 1:g(l.j);break;case 2:h(l.j);break;default:throw Error("Unexpected state: "+l.h);}}var l=this;this.g==null?f.h(k):this.g.push(k);this.A=!0};b.resolve=d;b.reject=function(g){return new b(function(h,k){k(g)})};b.race=function(g){return new b(function(h,k){for(var l=z(g),m=l.next();!m.done;m=l.next())d(m.value).Lc(h,k)})};b.all=function(g){var h=z(g),k=h.next();return k.done?d([]):new b(function(l,m){function q(w){return function(u){t[w]=u;r--;r==0&&l(t)}}var t=[],r=0;do t.push(void 0),r++,d(k.value).Lc(q(t.length-1),m),k=h.next();while(!k.done)})};return b});p("Object.setPrototypeOf",function(a){return a||ma});p("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});p("WeakMap",function(a){function b(k){this.g=(h+=Math.random()+1).toString();if(k){k=z(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}}function c(){}function d(k){var l=typeof k;return l==="object"&&k!==null||l==="function"}function e(k){if(!pa(k,g)){var l=new c;ba(k,g,{value:l})}}function f(k){var l=Object[k];l&&(Object[k]=function(m){if(m instanceof c)return m;Object.isExtensible(m)&&e(m);return l(m)})}if(function(){if(!a||!Object.seal)return!1;try{var k=Object.seal({}),l=Object.seal({}),m=new a([[k,2],[l,3]]);if(m.get(k)!=2||m.get(l)!=3)return!1;m.delete(k);m.set(l,4);return!m.has(k)&&m.get(l)==4}catch(q){return!1}}())return a;var g="$jscomp_hidden_"+Math.random();f("freeze");f("preventExtensions");f("seal");var h=0;b.prototype.set=function(k,l){if(!d(k))throw Error("Invalid WeakMap key");e(k);if(!pa(k,g))throw Error("WeakMap key fail: "+k);k[g][this.g]=l;return this};b.prototype.get=function(k){return d(k)&&pa(k,g)?k[g][this.g]:void 0};b.prototype.has=function(k){return d(k)&&pa(k,g)&&pa(k[g],this.g)};b.prototype.delete=function(k){return d(k)&&pa(k,g)&&pa(k[g],this.g)?delete k[g][this.g]:!1};return b});p("Map",function(a){function b(){var h={};return h.Ka=h.next=h.head=h}function c(h,k){var l=h[1];return fa(function(){if(l){for(;l.head!=h[1];)l=l.Ka;for(;l.next!=l.head;)return l=l.next,{done:!1,value:k(l)};l=null}return{done:!0,value:void 0}})}function d(h,k){var l=k&&typeof k;l=="object"||l=="function"?f.has(k)?l=f.get(k):(l=""+ ++g,f.set(k,l)):l="p_"+k;var m=h[0][l];if(m&&pa(h[0],l))for(h=0;h<m.length;h++){var q=m[h];if(k!==k&&q.key!==q.key||k===q.key)return{id:l,list:m,index:h,entry:q}}return{id:l,list:m,index:-1,entry:void 0}}function e(h){this[0]={};this[1]=b();this.size=0;if(h){h=z(h);for(var k;!(k=h.next()).done;)k=k.value,this.set(k[0],k[1])}}if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var h=Object.seal({x:4}),k=new a(z([[h,"s"]]));if(k.get(h)!="s"||k.size!=1||k.get({x:4})||k.set({x:4},"t")!=k||k.size!=2)return!1;var l=k.entries(),m=l.next();if(m.done||m.value[0]!=h||m.value[1]!="s")return!1;m=l.next();return m.done||m.value[0].x!=4||m.value[1]!="t"||!l.next().done?!1:!0}catch(q){return!1}}())return a;var f=new WeakMap;e.prototype.set=function(h,k){h=h===0?0:h;var l=d(this,h);l.list||(l.list=this[0][l.id]=[]);l.entry?l.entry.value=k:(l.entry={next:this[1],Ka:this[1].Ka,head:this[1],key:h,value:k},l.list.push(l.entry),this[1].Ka.next=l.entry,this[1].Ka=l.entry,this.size++);return this};e.prototype.delete=function(h){h=d(this,h);return h.entry&&h.list?(h.list.splice(h.index,1),h.list.length||delete this[0][h.id],h.entry.Ka.next=h.entry.next,h.entry.next.Ka=h.entry.Ka,h.entry.head=null,this.size--,!0):!1};e.prototype.clear=function(){this[0]={};this[1]=this[1].Ka=b();this.size=0};e.prototype.has=function(h){return!!d(this,h).entry};e.prototype.get=function(h){return(h=d(this,h).entry)&&h.value};e.prototype.entries=function(){return c(this,function(h){return[h.key,h.value]})};e.prototype.keys=function(){return c(this,function(h){return h.key})};e.prototype.values=function(){return c(this,function(h){return h.value})};e.prototype.forEach=function(h,k){for(var l=this.entries(),m;!(m=l.next()).done;)m=m.value,h.call(k,m[1],m[0],this)};e.prototype[Symbol.iterator]=e.prototype.entries;var g=0;return e});p("Set",function(a){function b(c){this.g=new Map;if(c){c=z(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.g.size}if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var c=Object.seal({x:4}),d=new a(z([c]));if(!d.has(c)||d.size!=1||d.add(c)!=d||d.size!=1||d.add({x:4})!=d||d.size!=2)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||f.value[0].x!=4||f.value[1]!=f.value[0]?!1:e.next().done}catch(g){return!1}}())return a;b.prototype.add=function(c){c=c===0?0:c;this.g.set(c,c);this.size=this.g.size;return this};b.prototype.delete=function(c){c=this.g.delete(c);this.size=this.g.size;return c};b.prototype.clear=function(){this.g.clear();this.size=0};b.prototype.has=function(c){return this.g.has(c)};b.prototype.entries=function(){return this.g.entries()};b.prototype.values=function(){return this.g.values()};b.prototype.keys=b.prototype.values;b.prototype[Symbol.iterator]=b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.g.forEach(function(f){return c.call(d,f,f,e)})};return b});function Ja(a,b,c){if(a==null)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""}p("Array.prototype.find",function(a){return a?a:function(b,c){a:{var d=this;d instanceof String&&(d=String(d));for(var e=d.length,f=0;f<e;f++){var g=d[f];if(b.call(c,g,f,d)){b=g;break a}}b=void 0}return b}});function Ka(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e}p("Array.prototype.entries",function(a){return a?a:function(){return Ka(this,function(b,c){return[b,c]})}});p("Array.prototype.keys",function(a){return a?a:function(){return Ka(this,function(b){return b})}});p("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=Ja(this,b,"startsWith"),e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var g=0;g<f&&c<e;)if(d[c++]!=b[g++])return!1;return g>=f}});p("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}});p("String.prototype.repeat",function(a){return a?a:function(b){var c=Ja(this,null,"repeat");if(b<0||b>1342177279)throw new RangeError("Invalid count value");b|=0;for(var d="";b;)if(b&1&&(d+=c),b>>>=1)c+=c;return d}});p("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)pa(b,d)&&c.push([d,b[d]]);return c}});p("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991});p("Number.MIN_SAFE_INTEGER",function(){return-9007199254740991});p("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});p("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});p("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}});p("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});p("String.prototype.includes",function(a){return a?a:function(b,c){return Ja(this,b,"includes").indexOf(b,c||0)!==-1}});p("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)pa(b,d)&&c.push(b[d]);return c}});p("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}});p("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}});p("Array.prototype.values",function(a){return a?a:function(){return Ka(this,function(b,c){return c})}});p("Array.prototype.fill",function(a){return a?a:function(b,c,d){var e=this.length||0;c<0&&(c=Math.max(0,e+c));if(d==null||d>e)d=e;d=Number(d);d<0&&(d=Math.max(0,e+d));for(c=Number(c||0);c<d;c++)this[c]=b;return this}});function La(a){return a?a:Array.prototype.fill}p("Int8Array.prototype.fill",La);p("Uint8Array.prototype.fill",La);p("Uint8ClampedArray.prototype.fill",La);p("Int16Array.prototype.fill",La);p("Uint16Array.prototype.fill",La);p("Int32Array.prototype.fill",La);p("Uint32Array.prototype.fill",La);p("Float32Array.prototype.fill",La);p("Float64Array.prototype.fill",La);p("String.prototype.padStart",function(a){return a?a:function(b,c){var d=Ja(this,null,"padStart");b-=d.length;c=c!==void 0?String(c):" ";return(b>0&&c?c.repeat(Math.ceil(b/c.length)).substring(0,b):"")+d}});p("Array.prototype.flat",function(a){return a?a:function(b){b=b===void 0?1:b;var c=[];Array.prototype.forEach.call(this,function(d){Array.isArray(d)&&b>0?(d=Array.prototype.flat.call(d,b-1),c.push.apply(c,d)):c.push(d)});return c}});/* 
 
 Copyright The Closure Library Authors. 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var Ma=this||self;function Na(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"}function Oa(a){var b=Na(a);return b=="array"||b=="object"&&typeof a.length=="number"}function Pa(a){var b=typeof a;return b=="object"&&a!=null||b=="function"}function Ra(a){return a}function Sa(a,b){function c(){}c.prototype=b.prototype;a.Gg=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.sh=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Ta;function Ua(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1}function Va(a,b){for(var c=a.length,d=typeof a==="string"?a.split(""):a,e=0;e<c;e++)e in d&&b.call(void 0,d[e],e,a)}function Wa(a,b){for(var c=a.length,d=[],e=0,f=typeof a==="string"?a.split(""):a,g=0;g<c;g++)if(g in f){var h=f[g];b.call(void 0,h,g,a)&&(d[e++]=h)}return d}function Xa(a,b,c){for(var d=a.length,e=Array(d),f=typeof a==="string"?a.split(""):a,g=0;g<d;g++)g in f&&(e[g]=b.call(c,f[g],g,a));return e}function Ya(a,b){var c="";Va(a,function(d,e){c=b.call(void 0,c,d,e,a)});return c}function Za(a,b){for(var c=a.length,d=typeof a==="string"?a.split(""):a,e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a))return!0;return!1}function $a(a){for(var b=["x","y","width","height"],c=b.length,d=typeof b==="string"?b.split(""):b,e=0;e<c;e++)if(e in d&&!a.call(void 0,d[e],e,b))return!1;return!0}function ab(a,b){a:{for(var c=a.length,d=typeof a==="string"?a.split(""):a,e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a)){b=e;break a}b=-1}return b<0?null:typeof a==="string"?a.charAt(b):a[b]}function bb(a,b){b=Ua(a,b);b>=0&&Array.prototype.splice.call(a,b,1)}function cb(a){return Array.prototype.concat.apply([],arguments)}function eb(a){var b=a.length;if(b>0){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]}function fb(a,b,c){return arguments.length<=2?Array.prototype.slice.call(a,b):Array.prototype.slice.call(a,b,c)}function gb(a,b){a.sort(b||hb)}function ib(a,b,c){if(!Oa(a)||!Oa(b)||a.length!=b.length)return!1;var d=a.length;c=c||jb;for(var e=0;e<d;e++)if(!c(a[e],b[e]))return!1;return!0}function hb(a,b){return a>b?1:a<b?-1:0}function jb(a,b){return a===b}function kb(a){for(var b=[],c=0;c<arguments.length;c++){var d=arguments[c];if(Array.isArray(d))for(var e=0;e<d.length;e+=8192){var f=fb(d,e,e+8192);f=kb.apply(null,f);for(var g=0;g<f.length;g++)b.push(f[g])}else b.push(d)}return b}function lb(a,b,c){return cb.apply([],Xa(a,b,c))};var mb={NONE:0,bh:1},pb={Wg:0,jh:1,ih:2,kh:3},qb={Ug:"a",Zg:"d",nh:"v"};function rb(){this.W=0;this.h=!1;this.g=-1;this.ca=!1;this.j=0}rb.prototype.isVisible=function(){return this.ca?this.W>=.3:this.W>=.5};var sb={Vg:0,dh:1},tb={668123728:0,668123729:1},ub={44731964:0,44731965:1},vb={NONE:0,fh:1,eh:2},wb={480596784:0,480596785:1,21063355:2};function xb(a,b,c){for(var d in a)b.call(c,a[d],d,a)}function yb(a,b){var c={},d;for(d in a)b.call(void 0,a[d],d,a)&&(c[d]=a[d]);return c}function zb(a){var b=Ab,c;for(c in b)if(!a.call(void 0,b[c],c,b))return!1;return!0}function Bb(a){var b=[],c=0,d;for(d in a)b[c++]=a[d];return b}function Cb(a){var b={},c;for(c in a)b[c]=a[c];return b}var Db="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function Eb(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<Db.length;f++)c=Db[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};function Fb(){this.g=null;this.l=!1;this.h=null}function Gb(a){a.l=!0;return a}function Hb(a,b){a.h&&Va(b,function(c){c=a.h[c];c!==void 0&&a.j(c)})}function Ib(a){Fb.call(this);this.m=a}y(Ib,Fb);Ib.prototype.j=function(a){var b;if(!(b=this.g!==null)){a:{b=this.m;for(c in b)if(b[c]==a){var c=!0;break a}c=!1}b=!c}b||(this.g=a)};function Jb(){Fb.call(this)}y(Jb,Fb);Jb.prototype.j=function(a){this.g===null&&typeof a==="number"&&(this.g=a)};function Kb(){this.g={};this.j=!0;this.h={}}Kb.prototype.reset=function(){this.g={};this.j=!0;this.h={}};function Lb(a,b,c){a.g[b]||(a.g[b]=new Ib(c));return a.g[b]}function Nb(a,b){(a=a.g.od)&&a.j(b)}function Ob(a,b){var c=a.h;if(c!==null&&b in c)return a.h[b];if(a=a.g[b])return a.g}function Pb(a){var b={},c=yb(a.g,function(d){return d.l});xb(c,function(d,e){d=a.h[e]!==void 0?String(a.h[e]):d.l&&d.g!==null?String(d.g):"";d.length>0&&(b[e]=d)},a);return b}function Qb(a,b){if(!a.j)return b;b=b.split("&");for(var c=b.length-1;c>=0;c--){var d=b[c].split("="),e=decodeURIComponent(d[0]);d.length>1?(d=decodeURIComponent(d[1]),d=/^[0-9]+$/g.exec(d)?parseInt(d,10):d):d=1;(e=a.g[e])&&e.j(d)}return b.join("&")}function Rb(a,b){a.j&&Va(Bb(a.g),function(c){return Hb(c,b)})}function Sb(a,b){a.j&&b&&typeof b==="string"&&(b=b.match(/[&;?]eid=([^&;]+)/))&&b.length===2&&(b=decodeURIComponent(b[1]).split(","),b=Xa(b,function(c){return Number(c)}),Rb(a,b))};function Tb(a){Lb(a,"od",mb);Gb(Lb(a,"opac",sb));Gb(Lb(a,"sbeos",sb));Gb(Lb(a,"prf",sb));Gb(Lb(a,"mwt",sb));Lb(a,"iogeo",sb)};var Ub=document,D=window;var Vb,Wb;a:{for(var Xb=["CLOSURE_FLAGS"],Yb=Ma,Zb=0;Zb<Xb.length;Zb++)if(Yb=Yb[Xb[Zb]],Yb==null){Wb=null;break a}Wb=Yb}var $b=Wb&&Wb[610401301];Vb=$b!=null?$b:!1;function ac(a,b){return a.toLowerCase().indexOf(b.toLowerCase())!=-1};function bc(){var a=Ma.navigator;return a&&(a=a.userAgent)?a:""}var cc,dc=Ma.navigator;cc=dc?dc.userAgentData||null:null;function ec(a){if(!Vb||!cc)return!1;for(var b=0;b<cc.brands.length;b++){var c=cc.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function F(a){return bc().indexOf(a)!=-1};function fc(){return Vb?!!cc&&cc.brands.length>0:!1}function hc(){return fc()?!1:F("Opera")}function ic(){return F("Firefox")||F("FxiOS")}function jc(){return F("Safari")&&!(kc()||(fc()?0:F("Coast"))||hc()||(fc()?0:F("Edge"))||(fc()?ec("Microsoft Edge"):F("Edg/"))||(fc()?ec("Opera"):F("OPR"))||ic()||F("Silk")||F("Android"))}function kc(){return fc()?ec("Chromium"):(F("Chrome")||F("CriOS"))&&!(fc()?0:F("Edge"))||F("Silk")}function lc(){return F("Android")&&!(kc()||ic()||hc()||F("Silk"))};/* 
 
 Copyright Google LLC 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var mc=globalThis.trustedTypes,nc;function pc(){var a=null;if(!mc)return a;try{var b=function(c){return c};a=mc.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a};function qc(a){this.g=a}qc.prototype.toString=function(){return this.g+""};function rc(a){var b;nc===void 0&&(nc=pc());a=(b=nc)?b.createScriptURL(a):a;return new qc(a)};var sc=na([""]),tc=oa(["\x00"],["\\0"]),uc=oa(["\n"],["\\n"]),vc=oa(["\x00"],["\\u0000"]);function wc(a){return a.toString().indexOf("`")===-1}wc(function(a){return a(sc)})||wc(function(a){return a(tc)})||wc(function(a){return a(uc)})||wc(function(a){return a(vc)});function xc(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};function yc(){return"opacity".replace(/\-([a-z])/g,function(a,b){return b.toUpperCase()})}function zc(a){return String(a).replace(/([A-Z])/g,"-$1").toLowerCase()}function Ac(a){return a.replace(RegExp("(^|[\\s]+)([a-z])","g"),function(b,c,d){return c+d.toUpperCase()})};function Bc(){return Vb?!!cc&&!!cc.platform:!1}function Cc(){return F("iPhone")&&!F("iPod")&&!F("iPad")}function Dc(){Cc()||F("iPad")||F("iPod")};function Ec(a){Ec[" "](a);return a}Ec[" "]=function(){};function Fc(a,b){try{return Ec(a[b]),!0}catch(c){}return!1};hc();var Gc=fc()?!1:F("Trident")||F("MSIE");F("Edge");var Hc=F("Gecko")&&!(ac(bc(),"WebKit")&&!F("Edge"))&&!(F("Trident")||F("MSIE"))&&!F("Edge"),Ic=ac(bc(),"WebKit")&&!F("Edge"),Jc=Ic&&F("Mobile");Bc()||F("Macintosh");Bc()||F("Windows");(Bc()?cc.platform==="Linux":F("Linux"))||Bc()||F("CrOS");Bc()||F("Android");Cc();F("iPad");F("iPod");Dc();ac(bc(),"KaiOS");ic();Cc()||F("iPod");F("iPad");lc();kc();jc()&&Dc();var Kc=!Gc&&!jc();function Lc(a,b){if(/-[a-z]/.test(b))return null;if(Kc&&a.dataset){if(lc()&&!(b in a.dataset))return null;a=a.dataset[b];return a===void 0?null:a}return a.getAttribute("data-"+zc(b))}function Mc(a,b){return/-[a-z]/.test(b)?!1:Kc&&a.dataset?b in a.dataset:a.hasAttribute?a.hasAttribute("data-"+zc(b)):!!a.getAttribute("data-"+zc(b))};function Nc(a,b){this.x=a!==void 0?a:0;this.y=b!==void 0?b:0}n=Nc.prototype;n.clone=function(){return new Nc(this.x,this.y)};n.equals=function(a){return a instanceof Nc&&(this==a?!0:this&&a?this.x==a.x&&this.y==a.y:!1)};n.ceil=function(){this.x=Math.ceil(this.x);this.y=Math.ceil(this.y);return this};n.floor=function(){this.x=Math.floor(this.x);this.y=Math.floor(this.y);return this};n.round=function(){this.x=Math.round(this.x);this.y=Math.round(this.y);return this};function Oc(a,b){this.width=a;this.height=b}n=Oc.prototype;n.clone=function(){return new Oc(this.width,this.height)};n.aspectRatio=function(){return this.width/this.height};n.isEmpty=function(){return!(this.width*this.height)};n.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};n.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};n.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON","INPUT"]);function Pc(a){var b=C.apply(1,arguments);if(b.length===0)return rc(a[0]);for(var c=a[0],d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return rc(c)};function Qc(a){return a?new Rc(Vc(a)):Ta||(Ta=new Rc)}function Wc(a){var b=a.scrollingElement?a.scrollingElement:Ic||a.compatMode!="CSS1Compat"?a.body||a.documentElement:a.documentElement;a=a.defaultView;return new Nc(a.pageXOffset||b.scrollLeft,a.pageYOffset||b.scrollTop)}function Xc(a,b,c){function d(h){h&&b.appendChild(typeof h==="string"?a.createTextNode(h):h)}for(var e=1;e<c.length;e++){var f=c[e];if(!Oa(f)||Pa(f)&&f.nodeType>0)d(f);else{a:{if(f&&typeof f.length=="number"){if(Pa(f)){var g=typeof f.item=="function"||typeof f.item=="string";break a}if(typeof f==="function"){g=typeof f.item=="function";break a}}g=!1}Va(g?eb(f):f,d)}}}function Vc(a){return a.nodeType==9?a:a.ownerDocument||a.document}function Yc(a,b){a&&(a=a.parentNode);for(var c=0;a;){if(b(a))return a;a=a.parentNode;c++}return null}function Rc(a){this.g=a||Ma.document||document}n=Rc.prototype;n.getElementsByTagName=function(a,b){return(b||this.g).getElementsByTagName(String(a))};n.appendChild=function(a,b){a.appendChild(b)};n.append=function(a,b){Xc(Vc(a),a,arguments)};n.canHaveChildren=function(a){if(a.nodeType!=1)return!1;switch(a.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case "SCRIPT":case "SOURCE":case "STYLE":case "TRACK":case "WBR":return!1}return!0};n.isElement=function(a){return Pa(a)&&a.nodeType==1};n.contains=function(a,b){if(!a||!b)return!1;if(a.contains&&b.nodeType==1)return a==b||a.contains(b);if(typeof a.compareDocumentPosition!="undefined")return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};function Zc(){this.g=this.P=null;this.h="no"}function $c(a){if(!a)return!1;try{var b=a.getBoundingClientRect();return b&&b.height>=30&&b.width>=30}catch(c){return!1}}function ad(a){return Wa(a,function(b){return $c(b)})}function bd(a,b){b=b===void 0?!0:b;return Wa(a,function(c){return c.nodeName!="SCRIPT"&&(!b||c.nodeName!="FONT")})}function cd(a,b){b=b===void 0?!0:b;if(!a)return null;if(!a.children)return a;for(var c=bd(eb(a.children),b);c.length;){var d=ad(c);if(d.length==1)return d[0];if(d.length>1)break;c=lb(c,function(e){return e.children?bd(eb(e.children)):[]},b)}return a}function dd(){var a=D.document.body;return kb(Xa(["GoogleActiveViewInnerContainer"],function(b){return eb((a||document).querySelectorAll("."+b))}))};function ed(){}ed.prototype.now=function(){return 0};ed.prototype.h=function(){return 0};ed.prototype.j=function(){return 0};ed.prototype.g=function(){return 0};function fd(){if(!gd())throw Error();}y(fd,ed);function gd(){return!(!D||!D.performance)}fd.prototype.now=function(){return gd()&&D.performance.now?D.performance.now():ed.prototype.now.call(this)};fd.prototype.h=function(){return gd()&&D.performance.memory?D.performance.memory.totalJSHeapSize||0:ed.prototype.h.call(this)};fd.prototype.j=function(){return gd()&&D.performance.memory?D.performance.memory.usedJSHeapSize||0:ed.prototype.j.call(this)};fd.prototype.g=function(){return gd()&&D.performance.memory?D.performance.memory.jsHeapSizeLimit||0:ed.prototype.g.call(this)};function hd(){}function id(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}};function G(a,b,c,d){this.top=a;this.h=b;this.g=c;this.left=d}n=G.prototype;n.Za=function(){return this.h-this.left};n.Xa=function(){return this.g-this.top};n.clone=function(){return new G(this.top,this.h,this.g,this.left)};n.contains=function(a){return this&&a?a instanceof G?a.left>=this.left&&a.h<=this.h&&a.top>=this.top&&a.g<=this.g:a.x>=this.left&&a.x<=this.h&&a.y>=this.top&&a.y<=this.g:!1};function jd(a,b){return a==b?!0:a&&b?a.top==b.top&&a.h==b.h&&a.g==b.g&&a.left==b.left:!1}n.ceil=function(){this.top=Math.ceil(this.top);this.h=Math.ceil(this.h);this.g=Math.ceil(this.g);this.left=Math.ceil(this.left);return this};n.floor=function(){this.top=Math.floor(this.top);this.h=Math.floor(this.h);this.g=Math.floor(this.g);this.left=Math.floor(this.left);return this};n.round=function(){this.top=Math.round(this.top);this.h=Math.round(this.h);this.g=Math.round(this.g);this.left=Math.round(this.left);return this};function kd(a,b,c){b instanceof Nc?(a.left+=b.x,a.h+=b.x,a.top+=b.y,a.g+=b.y):(a.left+=b,a.h+=b,typeof c==="number"&&(a.top+=c,a.g+=c));return a};var ld={};function md(a){try{return a.getBoundingClientRect()}catch(b){return{left:0,top:0,right:0,bottom:0}}}function nd(a,b){var c=new Nc(0,0);var d=(d=Vc(a))?d.defaultView:window;if(!Fc(d,"parent"))return c;do{if(d==b){var e=Vc(a);var f=new Nc(0,0);if(a!=(e?Vc(e):document).documentElement){var g=md(a);e=Qc(e);e=Wc(e.g);f.x=g.left+e.x;f.y=g.top+e.y}}else f=md(a),f=new Nc(f.left,f.top);c.x+=f.x;c.y+=f.y}while(d&&d!=b&&d!=d.parent&&(a=d.frameElement)&&(d=d.parent));return c};var od=id(function(){var a=!1;try{var b=Object.defineProperty({},"passive",{get:function(){a=!0}});Ma.addEventListener("test",null,b)}catch(c){}return a});function pd(a){return a?a.passive&&od()?a:a.capture||!1:!1}function qd(a,b,c,d){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,pd(d)),!0):!1}function rd(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,pd())};function sd(a){if(a.prerendering)return 3;var b;return(b={visible:1,hidden:2,prerender:3,preview:4,unloaded:5,"":0}[a.visibilityState||a.webkitVisibilityState||a.mozVisibilityState||""])!=null?b:0}function td(a){var b;a.visibilityState?b="visibilitychange":a.mozVisibilityState?b="mozvisibilitychange":a.webkitVisibilityState&&(b="webkitvisibilitychange");return b};function ud(){}ud.prototype.isVisible=function(){return sd(Ub)===1};ud.prototype.g=function(){return sd(Ub)===0};ud.prototype.l=function(a){var b=td(Ub);return b?qd(Ub,b,a,{capture:!1}):!1};ud.prototype.m=function(a){var b=td(Ub);b&&rd(Ub,b,a)};function vd(a){a.uh=!0;return a};var wd=vd(function(a){return typeof a==="number"}),xd=vd(function(a){return typeof a==="string"}),yd=vd(function(a){return typeof a==="boolean"});function zd(){return Vb&&cc?cc.mobile:!Ad()&&(F("iPod")||F("iPhone")||F("Android")||F("IEMobile"))}function Ad(){return Vb&&cc?!cc.mobile&&(F("iPad")||F("Android")||F("Silk")):F("iPad")||F("Android")&&!F("Mobile")||F("Silk")};function Bd(a){try{return!!a&&a.location.href!=null&&Fc(a,"foo")}catch(b){return!1}}function Cd(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)}function Dd(a,b){b=b===void 0?document:b;return b.createElement(String(a).toLowerCase())}function Ed(a){for(var b=a;a&&a!=a.parent;)a=a.parent,Bd(a)&&(b=a);return b};var Fd=RegExp("^https?://(\\w|-)+\\.cdn\\.ampproject\\.(net|org)(\\?|/|$)");function Gd(){var a=Ma,b=[],c=null;do{var d=a;if(Bd(d)){var e=d.location.href;c=d.document&&d.document.referrer||null}else e=c,c=null;b.push(new Hd(e||""));try{a=d.parent}catch(f){a=null}}while(a&&d!==a);d=0;for(a=b.length-1;d<=a;++d)b[d].depth=a-d;d=Ma;if(d.location&&d.location.ancestorOrigins&&d.location.ancestorOrigins.length===b.length-1)for(a=1;a<b.length;++a)e=b[a],e.url||(e.url=d.location.ancestorOrigins[a-1]||"",e.g=!0);return b}function Kd(a,b){this.g=a;this.h=b}function Hd(a,b){this.url=a;this.g=!!b;this.depth=null};function Ld(){this.j="&";this.h={};this.l=0;this.g=[]}function Md(a,b){var c={};c[a]=b;return[c]}function Nd(a,b,c,d,e){var f=[];Cd(a,function(g,h){(g=Od(g,b,c,d,e))&&f.push(h+"="+g)});return f.join(b)}function Od(a,b,c,d,e){if(a==null)return"";b=b||"&";c=c||",$";typeof c==="string"&&(c=c.split(""));if(a instanceof Array){if(d||(d=0),d<c.length){for(var f=[],g=0;g<a.length;g++)f.push(Od(a[g],b,c,d+1,e));return f.join(c[d])}}else if(typeof a==="object")return e||(e=0),e<2?encodeURIComponent(Nd(a,b,c,d,e+1)):"...";return encodeURIComponent(String(a))}function Pd(a,b,c){b=b+"//pagead2.googlesyndication.com"+c;var d=Qd(a)-c.length;if(d<0)return"";a.g.sort(function(m,q){return m-q});c=null;for(var e="",f=0;f<a.g.length;f++)for(var g=a.g[f],h=a.h[g],k=0;k<h.length;k++){if(!d){c=c==null?g:c;break}var l=Nd(h[k],a.j,",$");if(l){l=e+l;if(d>=l.length){d-=l.length;b+=l;e=a.j;break}c=c==null?g:c}}a="";c!=null&&(a=e+"trn="+c);return b+a}function Qd(a){var b=1,c;for(c in a.h)c.length>b&&(b=c.length);return 3997-b-a.j.length-1};function Rd(a,b,c,d,e){e=e===void 0?!1:e;a.google_image_requests||(a.google_image_requests=[]);var f=Dd("IMG",a.document);if(d){var g=function(){d&&bb(a.google_image_requests,f);rd(f,"load",g);rd(f,"error",g)};qd(f,"load",g);qd(f,"error",g)}c&&(f.referrerPolicy="no-referrer");e&&(f.attributionSrc="");f.src=b;a.google_image_requests.push(f)}function Sd(a){var b=b===void 0?!1:b;var c=c===void 0?!1:c;if(Td())Rd(window,a,!0,b,c);else{var d=Ma.document;if(d.body){var e=d.getElementById("goog-srcless-iframe");e||(e=Dd("IFRAME"),e.style.display="none",e.id="goog-srcless-iframe",d.body.appendChild(e));d=e}else d=null;d&&d.contentWindow&&Rd(d.contentWindow,a,!0,b,c)}}var Td=id(function(){return"referrerPolicy"in Dd("IMG")});function Ud(a){var b="wb";if(a.wb&&a.hasOwnProperty(b))return a.wb;b=new a;return a.wb=b};function Vd(){this.g=new ud;this.h=gd()?new fd:new ed}Vd.prototype.setInterval=function(a,b){return D.setInterval(a,b)};Vd.prototype.clearInterval=function(a){D.clearInterval(a)};Vd.prototype.setTimeout=function(a,b){return D.setTimeout(a,b)};Vd.prototype.clearTimeout=function(a){D.clearTimeout(a)};function Wd(a){Xd();Rd(D,a,!1,!1,!1)};function Yd(){}function Xd(){var a=Ud(Yd);if(!a.g){if(!D)throw Error("Context has not been set and window is undefined.");a.g=Ud(Vd)}return a.g};function Zd(){throw Error("Invalid UTF8");}function $d(a,b){b=String.fromCharCode.apply(null,b);return a==null?b:a+b}var ae=void 0,be,ce=typeof TextDecoder!=="undefined",de,ee=typeof String.prototype.isWellFormed==="function",fe=typeof TextEncoder!=="undefined";function ge(a){Ma.setTimeout(function(){throw a;},0)};var he={},ie=null,je=Hc||Ic||typeof Ma.btoa=="function";function ke(a){var b;b===void 0&&(b=0);le();b=he[b];for(var c=Array(Math.floor(a.length/3)),d=b[64]||"",e=0,f=0;e<a.length-2;e+=3){var g=a[e],h=a[e+1],k=a[e+2],l=b[g>>2];g=b[(g&3)<<4|h>>4];h=b[(h&15)<<2|k>>6];k=b[k&63];c[f++]=l+g+h+k}l=0;k=d;switch(a.length-e){case 2:l=a[e+1],k=b[(l&15)<<2]||d;case 1:a=a[e],c[f]=b[a>>2]+b[(a&3)<<4|l>>4]+k+d}return c.join("")}function me(a){var b=a.length,c=b*3/4;c%3?c=Math.floor(c):"=.".indexOf(a[b-1])!=-1&&(c="=.".indexOf(a[b-2])!=-1?c-2:c-1);var d=new Uint8Array(c),e=0;ne(a,function(f){d[e++]=f});return e!==c?d.subarray(0,e):d}function ne(a,b){function c(k){for(;d<a.length;){var l=a.charAt(d++),m=ie[l];if(m!=null)return m;if(!/^[\s\xa0]*$/.test(l))throw Error("Unknown base64 encoding at char: "+l);}return k}le();for(var d=0;;){var e=c(-1),f=c(0),g=c(64),h=c(64);if(h===64&&e===-1)break;b(e<<2|f>>4);g!=64&&(b(f<<4&240|g>>2),h!=64&&b(g<<6&192|h))}}function le(){if(!ie){ie={};for(var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"],c=0;c<5;c++){var d=a.concat(b[c].split(""));he[c]=d;for(var e=0;e<d.length;e++){var f=d[e];ie[f]===void 0&&(ie[f]=e)}}}};var oe=typeof Uint8Array!=="undefined",pe=!Gc&&typeof btoa==="function",qe=/[-_.]/g,re={"-":"+",_:"/",".":"="};function se(a){return re[a]||""}function te(a){if(!pe)return me(a);a=qe.test(a)?a.replace(qe,se):a;a=atob(a);for(var b=new Uint8Array(a.length),c=0;c<a.length;c++)b[c]=a.charCodeAt(c);return b}var ue={};function ve(a,b){we(b);this.g=a;if(a!=null&&a.length===0)throw Error("ByteString should be constructed with non-empty values");}ve.prototype.isEmpty=function(){return this.g==null};function xe(a){we(ue);var b=a.g;b==null||oe&&b!=null&&b instanceof Uint8Array||(typeof b==="string"?b=te(b):(Na(b),b=null));return b==null?b:a.g=b}var ye;function we(a){if(a!==ue)throw Error("illegal external caller");};var ze=void 0;function Ae(a){a=Error(a);xc(a,"warning");return a}function Be(a,b){if(a!=null){var c;var d=(c=ze)!=null?c:ze={};c=d[a]||0;c>=b||(d[a]=c+1,a=Error(),xc(a,"incident"),ge(a))}};var Ce=typeof Symbol==="function"&&typeof Symbol()==="symbol";function De(a,b,c){return typeof Symbol==="function"&&typeof Symbol()==="symbol"?(c===void 0?0:c)&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol():b}var Ee=De("jas",void 0,!0),Fe=De(void 0,"0di"),Ge=De(void 0,Symbol()),He=De(void 0,"0ubs"),Ie=De(void 0,"0ubsb"),Je=De(void 0,"0actk"),Ke=De("m_m","xh",!0);var Le={dg:{value:0,configurable:!0,writable:!0,enumerable:!1}},Me=Object.defineProperties,H=Ce?Ee:"dg",Ne,Oe=[];Pe(Oe,7);Ne=Object.freeze(Oe);function Qe(a,b){Ce||H in a||Me(a,Le);a[H]|=b}function Pe(a,b){Ce||H in a||Me(a,Le);a[H]=b};function Re(){return typeof BigInt==="function"};var Se={};function Ve(a,b){return b===void 0?a.h!==We&&!!(2&(a.C[H]|0)):!!(2&b)&&a.h!==We}var We={},Xe=Object.freeze({});function Ye(a,b,c){b=b&128?0:-1;var d=a.length,e;if(e=!!d)e=a[d-1],e=e!=null&&typeof e==="object"&&e.constructor===Object;for(var f=d+(e?-1:0),g=0;g<f;g++)c(g-b,a[g]);if(e){a=a[d-1];for(var h in a)Object.prototype.hasOwnProperty.call(a,h)&&!isNaN(h)&&c(+h,a[h])}}var Ze={};var $e=typeof Ma.BigInt==="function"&&typeof Ma.BigInt(0)==="bigint";function af(a){var b=a;if(xd(b)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(b))throw Error(String(b));}else if(wd(b)&&!Number.isSafeInteger(b))throw Error(String(b));return $e?BigInt(a):a=yd(a)?a?"1":"0":xd(a)?a.trim()||"0":String(a)}var gf=vd(function(a){return $e?a>=bf&&a<=cf:a[0]==="-"?df(a,ef):df(a,ff)}),ef=Number.MIN_SAFE_INTEGER.toString(),bf=$e?BigInt(Number.MIN_SAFE_INTEGER):void 0,ff=Number.MAX_SAFE_INTEGER.toString(),cf=$e?BigInt(Number.MAX_SAFE_INTEGER):void 0;function df(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(var c=0;c<a.length;c++){var d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}};var hf=typeof Uint8Array.prototype.slice==="function",jf=0,kf=0,lf;function mf(a){var b=a>>>0;jf=b;kf=(a-b)/4294967296>>>0}function nf(a){if(a<0){mf(-a);var b=z(of(jf,kf));a=b.next().value;b=b.next().value;jf=a>>>0;kf=b>>>0}else mf(a)}function pf(a,b){var c=b*4294967296+(a>>>0);return Number.isSafeInteger(c)?c:qf(a,b)}function rf(a,b){var c=b&**********;c&&(a=~a+1>>>0,b=~b>>>0,a==0&&(b=b+1>>>0));a=pf(a,b);return typeof a==="number"?c?-a:a:c?"-"+a:a}function qf(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(4294967296*b+a);else Re()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),c=b+sf(c)+sf(a));return c}function sf(a){a=String(a);return"0000000".slice(a.length)+a}function tf(){var a=jf,b=kf;b&**********?Re()?a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):(b=z(of(a,b)),a=b.next().value,b=b.next().value,a="-"+qf(a,b)):a=qf(a,b);return a}function uf(a){if(a.length<16)nf(Number(a));else if(Re())a=BigInt(a),jf=Number(a&BigInt(4294967295))>>>0,kf=Number(a>>BigInt(32)&BigInt(4294967295));else{var b=+(a[0]==="-");kf=jf=0;for(var c=a.length,d=b,e=(c-b)%6+b;e<=c;d=e,e+=6)d=Number(a.slice(d,e)),kf*=1E6,jf=jf*1E6+d,jf>=4294967296&&(kf+=Math.trunc(jf/4294967296),kf>>>=0,jf>>>=0);b&&(b=z(of(jf,kf)),a=b.next().value,b=b.next().value,jf=a,kf=b)}}function of(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]};function vf(a){return Array.prototype.slice.call(a)};var wf=typeof BigInt==="function"?BigInt.asIntN:void 0,xf=typeof BigInt==="function"?BigInt.asUintN:void 0,yf=Number.isSafeInteger,zf=Number.isFinite,Af=Math.trunc;function Bf(a){if(a==null||typeof a==="number")return a;if(a==="NaN"||a==="Infinity"||a==="-Infinity")return Number(a)}function Cf(a){if(a==null||typeof a==="boolean")return a;if(typeof a==="number")return!!a}var Df=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;function Ef(a){switch(typeof a){case "bigint":return!0;case "number":return zf(a);case "string":return Df.test(a);default:return!1}}function Ff(a){if(!zf(a))throw Ae("enum");return a|0}function Gf(a){return a==null?a:zf(a)?a|0:void 0}function Hf(a){if(a!=null){if(typeof a!=="number")throw Ae("int32");if(!zf(a))throw Ae("int32");a|=0}return a}function If(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return zf(a)?a|0:void 0}function Jf(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return zf(a)?a>>>0:void 0}function Kf(a){var b=0;b=b===void 0?0:b;if(!Ef(a))throw Ae("int64");var c=typeof a;switch(b){case 512:switch(c){case "string":return Lf(a);case "bigint":return String(wf(64,a));default:return Mf(a)}case 1024:switch(c){case "string":return b=Af(Number(a)),yf(b)?a=af(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),a=Re()?af(wf(64,BigInt(a))):af(Nf(a))),a;case "bigint":return af(wf(64,a));default:return yf(a)?af(Of(a)):af(Mf(a))}case 0:switch(c){case "string":return Lf(a);case "bigint":return af(wf(64,a));default:return Of(a)}default:throw Error("Unknown format requested type for int64");}}function Pf(a){if(a[0]==="-")return!1;var b=a.length;return b<20?!0:b===20&&Number(a.substring(0,6))<184467}function Qf(a){var b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337}function Rf(a){if(a<0){nf(a);var b=qf(jf,kf);a=Number(b);return yf(a)?a:b}b=String(a);if(Pf(b))return b;nf(a);return pf(jf,kf)}function Nf(a){if(Qf(a))return a;uf(a);return tf()}function Of(a){a=Af(a);yf(a)||(nf(a),a=rf(jf,kf));return a}function Mf(a){a=Af(a);if(yf(a))a=String(a);else{var b=String(a);Qf(b)?a=b:(nf(a),a=tf())}return a}function Lf(a){var b=Af(Number(a));if(yf(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return Nf(a)}function Sf(a){if(a==null)return a;var b=typeof a;if(b==="bigint")return String(wf(64,a));if(Ef(a)){if(b==="string")return Lf(a);if(b==="number")return Of(a)}}function Tf(a){if(a==null)return a;var b=typeof a;if(b==="bigint")return String(xf(64,a));if(Ef(a)){if(b==="string")return b=Af(Number(a)),yf(b)&&b>=0?a=String(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),Pf(a)||(uf(a),a=qf(jf,kf))),a;if(b==="number")return a=Af(a),a>=0&&yf(a)?a:Rf(a)}}function Uf(a){return a==null||typeof a==="string"?a:void 0}function Vf(a,b,c){if(a!=null&&a[Ke]===Se)return a;if(Array.isArray(a)){var d=a[H]|0;c=d|c&32|c&2;c!==d&&Pe(a,c);return new b(a)}};function Wf(a){return a};function Xf(a){var b=Ra(Ge);return b?a[b]:void 0}function Yf(){}function Zf(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&!isNaN(c)&&b(a,+c,a[c])}function $f(a){var b=new Yf;Zf(a,function(c,d,e){b[d]=vf(e)});b.g=a.g;return b}function ag(a,b){b<100||Be(He,1)};function bg(a,b,c,d){var e=d!==void 0;d=!!d;var f=Ra(Ge),g;!e&&Ce&&f&&(g=a[f])&&Zf(g,ag);f=[];var h=a.length;g=4294967295;var k=!1,l=!!(b&64),m=l?b&128?0:-1:void 0;if(!(b&1)){var q=h&&a[h-1];q!=null&&typeof q==="object"&&q.constructor===Object?(h--,g=h):q=void 0;if(l&&!(b&128)&&!e){k=!0;var t;g=((t=cg)!=null?t:Wf)(g-m,m,a,q)+m}}b=void 0;for(t=0;t<h;t++){var r=a[t];if(r!=null&&(r=c(r,d))!=null)if(l&&t>=g){var w=t-m,u=void 0;((u=b)!=null?u:b={})[w]=r}else f[t]=r}if(q)for(var v in q)Object.prototype.hasOwnProperty.call(q,v)&&(h=q[v],h!=null&&(h=c(h,d))!=null&&(t=+v,r=void 0,l&&!Number.isNaN(t)&&(r=t+m)<g?f[r]=h:(t=void 0,((t=b)!=null?t:b={})[v]=h)));b&&(k?f.push(b):f[g]=b);e&&Ra(Ge)&&(a=Xf(a))&&a instanceof Yf&&(f[Ge]=$f(a));return f}function dg(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return gf(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[H]|0;return a.length===0&&b&1?void 0:bg(a,b,dg)}if(a!=null&&a[Ke]===Se)return eg(a);if(a instanceof ve){b=a.g;if(b==null)a="";else if(typeof b==="string")a=b;else{if(pe){for(var c="",d=0,e=b.length-10240;d<e;)c+=String.fromCharCode.apply(null,b.subarray(d,d+=10240));c+=String.fromCharCode.apply(null,d?b.subarray(d):b);b=btoa(c)}else b=ke(b);a=a.g=b}return a}return}return a}var cg;function eg(a){a=a.C;return bg(a,a[H]|0,dg)};var fg,gg;function hg(a){switch(typeof a){case "boolean":return fg||(fg=[0,void 0,!0]);case "number":return a>0?void 0:a===0?gg||(gg=[0,void 0]):[-a,void 0];case "string":return[0,a];case "object":return a}}function ig(a,b,c){return a=jg(a,b[0],b[1],c?1:2)}function jg(a,b,c,d){d=d===void 0?0:d;if(a==null){var e=32;c?(a=[c],e|=128):a=[];b&&(e=e&-8380417|(b&1023)<<13)}else{if(!Array.isArray(a))throw Error("narr");e=a[H]|0;2048&e&&!(2&e)&&kg();if(e&256)throw Error("farr");if(e&64)return d!==0||e&2048||Pe(a,e|2048),a;if(c&&(e|=128,c!==a[0]))throw Error("mid");a:{c=a;e|=64;var f=c.length;if(f){var g=f-1,h=c[g];if(h!=null&&typeof h==="object"&&h.constructor===Object){b=e&128?0:-1;g-=b;if(g>=1024)throw Error("pvtlmt");for(var k in h)Object.prototype.hasOwnProperty.call(h,k)&&(f=+k,f<g&&(c[f+b]=h[k],delete h[k]));e=e&-8380417|(g&1023)<<13;break a}}if(b){k=Math.max(b,f-(e&128?0:-1));if(k>1024)throw Error("spvt");e=e&-8380417|(k&1023)<<13}}}e|=64;d===0&&(e|=2048);Pe(a,e);return a}function kg(){Be(Je,5)};function lg(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[H]|0;a.length===0&&c&1?a=void 0:c&2||(!b||4096&c||16&c?a=mg(a,c,!1,b&&!(c&16)):(Qe(a,34),c&4&&Object.freeze(a)));return a}if(a!=null&&a[Ke]===Se)return b=a.C,c=b[H]|0,Ve(a,c)?a:ng(a,b,c)?og(a,b):mg(b,c);if(a instanceof ve)return a}function og(a,b,c){a=new a.constructor(b);c&&(a.h=We);a.j=We;return a}function mg(a,b,c,d){d!=null||(d=!!(34&b));a=bg(a,b,lg,d);d=32;c&&(d|=2);b=b&8380609|d;Pe(a,b);return a}function pg(a){if(a.h!==We)return!1;var b=a.C;b=mg(b,b[H]|0);Qe(b,2048);a.C=b;a.h=void 0;a.j=void 0;return!0}function qg(a){if(!pg(a)&&Ve(a,a.C[H]|0))throw Error();}function rg(a,b){b===void 0&&(b=a[H]|0);b&32&&!(b&4096)&&Pe(a,b|4096)}function ng(a,b,c){return c&2?!0:c&32&&!(c&4096)?(Pe(b,c|2),a.h=We,!0):!1};function sg(a,b){a=tg(a.C,b);if(a!==null)return a}function tg(a,b,c,d){if(b===-1)return null;var e=b+(c?0:-1),f=a.length-1;if(!(f<1+(c?0:-1))){if(e>=f){var g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object){c=g[b];var h=!0}else if(e===f)c=g;else return}else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d;if(!Object.is(d,c))return h?g[b]=d:a[e]=d,d}return c}}function ug(a,b,c){qg(a);var d=a.C;vg(d,d[H]|0,b,c);return a}function vg(a,b,c,d,e){var f=c+(e?0:-1),g=a.length-1;if(g>=1+(e?0:-1)&&f>=g){var h=a[g];if(h!=null&&typeof h==="object"&&h.constructor===Object)return h[c]=d,b}if(f<=g)return a[f]=d,b;if(d!==void 0){var k;g=((k=b)!=null?k:b=a[H]|0)>>13&1023||536870912;c>=g?d!=null&&(f={},a[g+(e?0:-1)]=(f[c]=d,f)):a[f]=d}return b}function wg(a,b,c){a=a.C;return xg(a,a[H]|0,b,c)!==void 0}function yg(a,b,c,d,e){var f=a.C,g=f[H]|0;d=Ve(a,g)?1:d;e=!!e||d===3;d===2&&pg(a)&&(f=a.C,g=f[H]|0);a=zg(f,b);var h=a===Ne?7:a[H]|0,k=Ag(h,g);var l=4&k?!1:!0;if(l){4&k&&(a=vf(a),h=0,k=Bg(k,g),g=vg(f,g,b,a));for(var m=0,q=0;m<a.length;m++){var t=c(a[m]);t!=null&&(a[q++]=t)}q<m&&(a.length=q);c=(k|4)&-513;k=c&=-1025;k&=-4097}k!==h&&(Pe(a,k),2&k&&Object.freeze(a));return a=Cg(a,k,f,g,b,d,l,e)}function Cg(a,b,c,d,e,f,g,h){var k=b;f===1||(f!==4?0:2&b||!(16&b)&&32&d)?Dg(b)||(b|=!a.length||g&&!(4096&b)||32&d&&!(4096&b||16&b)?2:256,b!==k&&Pe(a,b),Object.freeze(a)):(f===2&&Dg(b)&&(a=vf(a),k=0,b=Bg(b,d),d=vg(c,d,e,a)),Dg(b)||(h||(b|=16),b!==k&&Pe(a,b)));2&b||!(4096&b||16&b)||rg(c,d);return a}function zg(a,b,c){a=tg(a,b,c);return Array.isArray(a)?a:Ne}function Ag(a,b){2&b&&(a|=2);return a|1}function Dg(a){return!!(2&a)&&!!(4&a)||!!(256&a)}function Eg(a,b,c,d){qg(a);var e=a.C;vg(e,e[H]|0,b,(d==="0"?Number(c)===0:c===d)?void 0:c);return a}function Fg(a,b,c){if(b&2)throw Error();var d=b&128?Ze:void 0,e=zg(a,c,d),f=e===Ne?7:e[H]|0,g=Ag(f,b);if(2&g||Dg(g)||16&g)e=vf(e),f=0,g=Bg(g,b),vg(a,b,c,e,d);g&=-13;g!==f&&Pe(e,g);return e}function Gg(a,b,c){var d=a[H]|0,e=d&128?Ze:void 0,f=tg(a,c,e);if(f!=null&&f[Ke]===Se){if(!Ve(f))return pg(f),f.C;var g=f.C}else Array.isArray(f)&&(g=f);if(g){var h=g[H]|0;h&2&&(g=mg(g,h))}g=ig(g,b,!0);g!==f&&vg(a,d,c,g,e);return g}function xg(a,b,c,d){var e=!1;d=tg(a,d,void 0,function(f){var g=Vf(f,c,b);e=g!==f&&g!=null;return g});if(d!=null)return e&&!Ve(d)&&rg(a,b),d}function Hg(a){var b=Ig;a=a.C;(a=xg(a,a[H]|0,b,2))||(a=b[Fe])||(a=new b,Qe(a.C,34),a=b[Fe]=a);return a}function Jg(a,b,c){var d=a.C,e=d[H]|0;b=xg(d,e,b,c);if(b==null)return b;e=d[H]|0;if(!Ve(a,e)){var f=b;var g=f.C,h=g[H]|0;f=Ve(f,h)?ng(f,g,h)?og(f,g,!0):new f.constructor(mg(g,h,!1)):f;f!==b&&(pg(a)&&(d=a.C,e=d[H]|0),b=f,e=vg(d,e,c,b),rg(d,e))}return b}function Kg(a){a=a.C;var b=a[H]|0,c=Lg;var d=!!d||!1;var e=zg(a,10),f=e===Ne?7:e[H]|0,g=Ag(f,b),h=!(4&g);if(h){var k=e,l=b,m;(m=!!(2&g))&&(l|=2);for(var q=!m,t=!0,r=0,w=0;r<k.length;r++){var u=Vf(k[r],c,l);if(u instanceof c){if(!m){var v=Ve(u);q&&(q=!v);t&&(t=v)}k[w++]=u}}w<r&&(k.length=w);g|=4;g=t?g&-4097:g|4096;g=q?g|8:g&-9}g!==f&&(Pe(e,g),2&g&&Object.freeze(e));return e=Cg(e,g,a,b,10,1,h,d)}function Mg(a,b,c){c==null&&(c=void 0);ug(a,b,c);c&&!Ve(c)&&rg(a.C);return a}function Bg(a,b){return a=(2&b?a|2:a&-3)&-273}function Ng(a,b,c){qg(a);b=yg(a,b,Gf,2,!0);if(Array.isArray(c))for(var d=c.length,e=0;e<d;e++)b.push(Ff(c[e]));else for(c=z(c),d=c.next();!d.done;d=c.next())b.push(Ff(d.value));return a}function Og(a,b,c){c=c===void 0?!1:c;var d;return(d=Cf(sg(a,b)))!=null?d:c}function Pg(a,b){var c=c===void 0?0:c;var d;return(d=If(sg(a,b)))!=null?d:c}function Qg(a){var b=b===void 0?"":b;a=Uf(sg(a,2));return a!=null?a:b}function Rg(a,b,c){if(c!=null&&typeof c!=="boolean")throw Error("Expected boolean but got "+Na(c)+": "+c);return ug(a,b,c)}function Sg(a,b,c){if(c!=null){if(typeof c!=="number")throw Ae("uint32");if(!zf(c))throw Ae("uint32");c>>>=0}return ug(a,b,c)}function Tg(a,b,c){c=c==null?c:Kf(c);return Eg(a,b,c,"0")}function Ug(a,b,c){if(c!=null&&typeof c!=="string")throw Error();return ug(a,b,c)}function Vg(a,b,c){return ug(a,b,c==null?c:Ff(c))};function Wg(a,b){return Error("Invalid wire type: "+a+" (at position "+b+")")}function Xg(){return Error("Failed to read varint, encoding is invalid.")}function Zg(a,b){return Error("Tried to read past the end of the data "+b+" > "+a)};function $g(a,b,c){this.g=a;if(c&&!b)throw Error();this.h=b}function ah(a,b){if(typeof a==="string")return new $g(te(a),b);if(Array.isArray(a))return new $g(new Uint8Array(a),b);if(a.constructor===Uint8Array)return new $g(a,!1);if(a.constructor===ArrayBuffer)return a=new Uint8Array(a),new $g(a,!1);if(a.constructor===ve)return b=xe(a)||new Uint8Array(0),new $g(b,!0,a);if(a instanceof Uint8Array)return a=a.constructor===Uint8Array?a:new Uint8Array(a.buffer,a.byteOffset,a.byteLength),new $g(a,!1);throw Error();};function bh(a,b,c,d){this.h=null;this.m=!1;this.g=this.j=this.l=0;this.init(a,b,c,d)}bh.prototype.init=function(a,b,c,d){var e=d===void 0?{}:d;d=e.Kc===void 0?!1:e.Kc;e=e.ed===void 0?!1:e.ed;this.Kc=d;this.ed=e;a&&(a=ah(a,this.ed),this.h=a.g,this.m=a.h,this.l=b||0,this.j=c!==void 0?this.l+c:this.h.length,this.g=this.l)};bh.prototype.clear=function(){this.h=null;this.m=!1;this.g=this.j=this.l=0;this.Kc=!1};bh.prototype.reset=function(){this.g=this.l};function ch(a,b){var c=0,d=0,e=0,f=a.h,g=a.g;do{var h=f[g++];c|=(h&127)<<e;e+=7}while(e<32&&h&128);e>32&&(d|=(h&127)>>4);for(e=3;e<32&&h&128;e+=7)h=f[g++],d|=(h&127)<<e;dh(a,g);if(h<128)return b(c>>>0,d>>>0);throw Xg();}function dh(a,b){a.g=b;if(b>a.j)throw Zg(a.j,b);}function eh(a){var b=a.h,c=a.g,d=b[c++],e=d&127;if(d&128&&(d=b[c++],e|=(d&127)<<7,d&128&&(d=b[c++],e|=(d&127)<<14,d&128&&(d=b[c++],e|=(d&127)<<21,d&128&&(d=b[c++],e|=d<<28,d&128&&b[c++]&128&&b[c++]&128&&b[c++]&128&&b[c++]&128&&b[c++]&128)))))throw Xg();dh(a,c);return e}function fh(a){return eh(a)>>>0}function gh(a){return ch(a,pf)}function hh(a){var b=a.h,c=a.g,d=b[c],e=b[c+1],f=b[c+2];b=b[c+3];dh(a,a.g+4);return(d<<0|e<<8|f<<16|b<<24)>>>0}function ih(a){var b=hh(a),c=hh(a);a=(c>>31)*2+1;var d=c>>>20&2047;b=4294967296*(c&1048575)+b;return d==2047?b?NaN:a*Infinity:d==0?a*4.9E-324*b:a*Math.pow(2,d-1075)*(b+4503599627370496)}function jh(a){for(var b=0,c=a.g,d=c+10,e=a.h;c<d;){var f=e[c++];b|=f;if((f&128)===0)return dh(a,c),!!(b&127)}throw Xg();}function kh(a){return eh(a)}function lh(a,b){if(b<0)throw Error("Tried to read a negative byte length: "+b);var c=a.g,d=c+b;if(d>a.j)throw Zg(b,a.j-c);a.g=d;return c}var mh=[];function nh(a,b,c,d){if(mh.length){var e=mh.pop();e.init(a,b,c,d);a=e}else a=new bh(a,b,c,d);this.g=a;this.j=this.g.g;this.h=this.l=-1;oh(this,d)}function oh(a,b){b=b===void 0?{}:b;a.Bd=b.Bd===void 0?!1:b.Bd}function ph(a,b,c,d){if(qh.length){var e=qh.pop();oh(e,d);e.g.init(a,b,c,d);return e}return new nh(a,b,c,d)}function rh(a){a.g.clear();a.l=-1;a.h=-1;qh.length<100&&qh.push(a)}nh.prototype.reset=function(){this.g.reset();this.j=this.g.g;this.h=this.l=-1};function sh(a){var b=a.g;if(b.g==b.j)return!1;a.j=a.g.g;var c=fh(a.g);b=c>>>3;c&=7;if(!(c>=0&&c<=5))throw Wg(c,a.j);if(b<1)throw Error("Invalid field number: "+b+" (at position "+a.j+")");a.l=b;a.h=c;return!0}function th(a){switch(a.h){case 0:a.h!=0?th(a):jh(a.g);break;case 1:a=a.g;dh(a,a.g+8);break;case 2:if(a.h!=2)th(a);else{var b=fh(a.g);a=a.g;dh(a,a.g+b)}break;case 5:a=a.g;dh(a,a.g+4);break;case 3:b=a.l;do{if(!sh(a))throw Error("Unmatched start-group tag: stream EOF");if(a.h==4){if(a.l!=b)throw Error("Unmatched end-group tag");break}th(a)}while(1);break;default:throw Wg(a.h,a.j);}}function uh(a,b,c){var d=a.g.j,e=fh(a.g),f=a.g.g+e,g=f-d;g<=0&&(a.g.j=f,c(b,a,void 0,void 0,void 0),g=f-a.g.g);if(g)throw Error("Message parsing ended unexpectedly. Expected to read "+(e+" bytes, instead read "+(e-g)+" bytes, either the data ended unexpectedly or the message misreported its own length"));a.g.g=f;a.g.j=d}function vh(a,b,c){var d=fh(a.g);for(d=a.g.g+d;a.g.g<d;)c.push(b(a.g))}var qh=[];function wh(a,b){this.h=a>>>0;this.g=b>>>0}function xh(a){if(!a)return yh||(yh=new wh(0,0));if(!/^\d+$/.test(a))return null;uf(a);return new wh(jf,kf)}var yh;function zh(a,b){this.h=a>>>0;this.g=b>>>0}function Ah(a){if(!a)return Bh||(Bh=new zh(0,0));if(!/^-?\d+$/.test(a))return null;uf(a);return new zh(jf,kf)}var Bh;function Ch(){this.g=[]}Ch.prototype.length=function(){return this.g.length};Ch.prototype.end=function(){var a=this.g;this.g=[];return a};function Dh(a,b,c){for(;c>0||b>127;)a.g.push(b&127|128),b=(b>>>7|c<<25)>>>0,c>>>=7;a.g.push(b)}function Eh(a,b){for(;b>127;)a.g.push(b&127|128),b>>>=7;a.g.push(b)}function Fh(a,b){if(b>=0)Eh(a,b);else{for(var c=0;c<9;c++)a.g.push(b&127|128),b>>=7;a.g.push(1)}}function Gh(a,b){a.g.push(b>>>0&255);a.g.push(b>>>8&255);a.g.push(b>>>16&255);a.g.push(b>>>24&255)};function Hh(){this.j=[];this.h=0;this.g=new Ch}function Ih(a,b){b.length!==0&&(a.j.push(b),a.h+=b.length)}function Jh(a,b){Kh(a,b,2);b=a.g.end();Ih(a,b);b.push(a.h);return b}function Lh(a,b){var c=b.pop();for(c=a.h+a.g.length()-c;c>127;)b.push(c&127|128),c>>>=7,a.h++;b.push(c);a.h++}function Kh(a,b,c){Eh(a.g,b*8+c)}function Mh(a,b,c){if(c!=null)switch(Kh(a,b,0),typeof c){case "number":a=a.g;nf(c);Dh(a,jf,kf);break;case "bigint":c=BigInt.asUintN(64,c);c=new wh(Number(c&BigInt(4294967295)),Number(c>>BigInt(32)));Dh(a.g,c.h,c.g);break;default:c=xh(c),Dh(a.g,c.h,c.g)}}function Nh(a,b,c){c!=null&&(c=parseInt(c,10),Kh(a,b,0),Fh(a.g,c))};function Oh(){function a(){throw Error();}Object.setPrototypeOf(a,a.prototype);return a}var Ph=Oh(),Qh=Oh(),Rh=Oh(),Sh=Oh(),Th=Oh(),Uh=Oh(),Vh=Oh(),Wh=Oh(),Xh=Oh(),Yh=Oh(),Zh=Oh();function $h(a,b,c){this.C=jg(a,b,c)}$h.prototype.toJSON=function(){return eg(this)};function ai(a){return JSON.stringify(eg(a))}$h.prototype.clone=function(){var a=this.C,b=a[H]|0;return ng(this,a,b)?og(this,a,!0):new this.constructor(mg(a,b,!1))};$h.prototype[Ke]=Se;$h.prototype.toString=function(){return this.C.toString()};function bi(a,b,c){this.g=a;this.h=b;a=Ra(Ph);this.j=!!a&&c===a||!1}function ci(a){var b=di;var c=c===void 0?Ph:c;return new bi(a,b,c)}function di(a,b,c,d,e){b=b instanceof $h?b.C:Array.isArray(b)?ig(b,d,!1):void 0;b!=null&&(c=Jh(a,c),e(b,a),Lh(a,c))}var ei=ci(function(a,b,c,d,e){if(a.h!==2)return!1;uh(a,Gg(b,d,c),e);return!0}),fi=ci(function(a,b,c,d,e){if(a.h!==2)return!1;uh(a,Gg(b,d,c),e);return!0}),gi=Symbol(),hi=Symbol(),ii=Symbol(),ji=Symbol(),ki=Symbol(),li,mi;function ni(a,b,c,d){var e=d[a];if(e)return e;e={};e.Af=d;e.wc=hg(d[0]);var f=d[1],g=1;f&&f.constructor===Object&&(e.Dd=f,f=d[++g],typeof f==="function"&&(e.Se=!0,li!=null||(li=f),mi!=null||(mi=d[g+1]),f=d[g+=2]));for(var h={};f&&oi(f);){for(var k=0;k<f.length;k++)h[f[k]]=f;f=d[++g]}for(k=1;f!==void 0;){typeof f==="number"&&(k+=f,f=d[++g]);var l=void 0;if(f instanceof bi)var m=f;else m=ei,g--;f=void 0;if((f=m)==null?0:f.j){f=d[++g];l=d;var q=g;typeof f==="function"&&(f=f(),l[q]=f);l=f}f=d[++g];q=k+1;typeof f==="number"&&f<0&&(q-=f,f=d[++g]);for(;k<q;k++){var t=h[k];l?c(e,k,m,l,t):b(e,k,m,t)}}return d[a]=e}function oi(a){return Array.isArray(a)&&!!a.length&&typeof a[0]==="number"&&a[0]>0}function pi(a){return Array.isArray(a)?a[0]instanceof bi?a:[fi,a]:[a,void 0]};function qi(a,b,c,d){var e=c.g;a[b]=d?function(f,g,h){return e(f,g,h,d)}:e}function ri(a,b,c,d,e){var f=c.g,g,h;a[b]=function(k,l,m){return f(k,l,m,h||(h=ni(hi,qi,ri,d).wc),g||(g=si(d)),e)}}function si(a){var b=a[ii];if(b!=null)return b;var c=ni(hi,qi,ri,a);b=c.Se?function(d,e){return li(d,e,c)}:function(d,e){for(;sh(e)&&e.h!=4;){var f=e.l,g=c[f];if(g==null){var h=c.Dd;h&&(h=h[f])&&(h=ti(h),h!=null&&(g=c[f]=h))}if(g==null||!g(e,d,f)){g=e;h=g.j;th(g);var k=g;if(k.Bd)var l=void 0;else g=k.g.g-h,k.g.g=h,k=k.g,g==0?g=ye||(ye=new ve(null,ue)):(h=lh(k,g),k.Kc&&k.m?g=k.h.subarray(h,h+g):(k=k.h,g=h+g,g=h===g?new Uint8Array(0):hf?k.slice(h,g):new Uint8Array(k.subarray(h,g))),g=g.length==0?ye||(ye=new ve(null,ue)):new ve(g,ue)),l=g;k=h=g=void 0;var m=d;l&&((g=(h=(k=m[Ge])!=null?k:m[Ge]=new Yf)[f])!=null?g:h[f]=[]).push(l)}}if(d=Xf(d))d.g=c.Af[ki];return!0};a[ii]=b;a[ki]=ui.bind(a);return b}function ui(a,b,c,d){var e=this[hi],f=this[ii],g=ig(void 0,e.wc,!1),h=Xf(a);if(h){var k=!1,l=e.Dd;if(l){e=function(w,u,v){if(v.length!==0)if(l[u])for(w=z(v),u=w.next();!u.done;u=w.next()){u=ph(u.value);try{k=!0,f(g,u)}finally{rh(u)}}else d==null||d(a,u,v)};if(b==null)Zf(h,e);else if(h!=null){var m=h[b];m&&e(h,b,m)}if(k){var q=a[H]|0;if(q&2&&q&2048&&(c==null||!c.Rh))throw Error();var t=q&128?Ze:void 0,r=function(w,u){if(tg(a,w,t)!=null)switch(c==null?void 0:c.Qh){case 1:return;default:throw Error();}u!=null&&(q=vg(a,q,w,u,t));delete h[w]};b==null?Ye(g,g[H]|0,function(w,u){r(w,u)}):r(b,tg(g,b,t))}}}}function ti(a){a=pi(a);var b=a[0].g;if(a=a[1]){var c=si(a),d=ni(hi,qi,ri,a).wc;return function(e,f,g){return b(e,f,g,d,c)}}return b};function vi(a,b,c){a[b]=c.h}function wi(a,b,c,d){var e,f,g=c.h;a[b]=function(h,k,l){return g(h,k,l,f||(f=ni(gi,vi,wi,d).wc),e||(e=xi(d)))}}function xi(a){var b=a[ji];if(!b){var c=ni(gi,vi,wi,a);b=function(d,e){return yi(d,e,c)};a[ji]=b}return b}function yi(a,b,c){Ye(a,a[H]|0,function(d,e){if(e!=null){var f=zi(c,d);f?f(b,e,d):d<500||Be(Ie,3)}});(a=Xf(a))&&Zf(a,function(d,e,f){Ih(b,b.g.end());for(d=0;d<f.length;d++)Ih(b,xe(f[d])||new Uint8Array(0))})}function zi(a,b){var c=a[b];if(c)return c;if(c=a.Dd)if(c=c[b]){c=pi(c);var d=c[0].h;if(c=c[1]){var e=xi(c),f=ni(gi,vi,wi,c).wc;c=a.Se?mi(f,e):function(g,h,k){return d(g,h,k,f,e)}}else c=d;return a[b]=c}};function Ai(a,b,c){if(Array.isArray(b)){var d=b[H]|0;if(d&4)return b;for(var e=0,f=0;e<b.length;e++){var g=a(b[e]);g!=null&&(b[f++]=g)}f<e&&(b.length=f);c&&(Pe(b,(d|5)&-1537),d&2&&Object.freeze(b));return b}}function Bi(a,b,c){return new bi(a,b,c)}function Ci(a,b,c){return new bi(a,b,c)}function Di(a,b,c){vg(a,a[H]|0,b,c,(a[H]|0)&128?Ze:void 0)}function Ei(a,b,c){b=Bf(b);b!=null&&(Kh(a,c,1),a=a.g,c=lf||(lf=new DataView(new ArrayBuffer(8))),c.setFloat64(0,+b,!0),jf=c.getUint32(0,!0),kf=c.getUint32(4,!0),Gh(a,jf),Gh(a,kf))}function Fi(a,b,c){b=Sf(b);if(b!=null){switch(typeof b){case "string":Ah(b)}if(b!=null)switch(Kh(a,c,0),typeof b){case "number":a=a.g;nf(b);Dh(a,jf,kf);break;case "bigint":c=BigInt.asUintN(64,b);c=new zh(Number(c&BigInt(4294967295)),Number(c>>BigInt(32)));Dh(a.g,c.h,c.g);break;default:c=Ah(b),Dh(a.g,c.h,c.g)}}}function Gi(a,b,c){b=If(b);b!=null&&b!=null&&(Kh(a,c,0),Fh(a.g,b))}function Hi(a,b,c){if(a.h!==0&&a.h!==2)return!1;b=Fg(b,b[H]|0,c);a.h==2?vh(a,kh,b):b.push(eh(a.g));return!0}var Ii=Bi(function(a,b,c){if(a.h!==1)return!1;Di(b,c,ih(a.g));return!0},Ei,Yh),Ji=Bi(function(a,b,c){if(a.h!==1)return!1;a=ih(a.g);Di(b,c,a===0?void 0:a);return!0},Ei,Yh),Ki=Bi(function(a,b,c){if(a.h!==5)return!1;var d=hh(a.g);a=(d>>31)*2+1;var e=d>>>23&255;d&=8388607;Di(b,c,e==255?d?NaN:a*Infinity:e==0?a*1.401298464324817E-45*d:a*Math.pow(2,e-150)*(d+8388608));return!0},function(a,b,c){b=Bf(b);b!=null&&(Kh(a,c,5),a=a.g,c=lf||(lf=new DataView(new ArrayBuffer(8))),c.setFloat32(0,+b,!0),kf=0,jf=c.getUint32(0,!0),Gh(a,jf))},Xh),Li=Bi(function(a,b,c){if(a.h!==0)return!1;Di(b,c,ch(a.g,rf));return!0},Fi,Vh),Mi=Bi(function(a,b,c){if(a.h!==0)return!1;a=ch(a.g,rf);Di(b,c,a===0?void 0:a);return!0},Fi,Vh),Ni=Bi(function(a,b,c){if(a.h!==0)return!1;Di(b,c,gh(a.g));return!0},function(a,b,c){b=Tf(b);if(b!=null){switch(typeof b){case "string":xh(b)}Mh(a,c,b)}},Wh),Oi=Ci(function(a,b,c){if(a.h!==0&&a.h!==2)return!1;b=Fg(b,b[H]|0,c);a.h==2?vh(a,gh,b):b.push(gh(a.g));return!0},function(a,b,c){b=Ai(Tf,b,!1);if(b!=null)for(var d=0;d<b.length;d++)Mh(a,c,b[d])},Wh),Pi=Bi(function(a,b,c){if(a.h!==0)return!1;Di(b,c,eh(a.g));return!0},Gi,Sh),Qi=Ci(function(a,b,c){if(a.h!==0&&a.h!==2)return!1;b=Fg(b,b[H]|0,c);a.h==2?vh(a,eh,b):b.push(eh(a.g));return!0},function(a,b,c){b=Ai(If,b,!0);if(b!=null)for(var d=0;d<b.length;d++){var e=a,f=c,g=b[d];g!=null&&(Kh(e,f,0),Fh(e.g,g))}},Sh),Ri=Bi(function(a,b,c){if(a.h!==0)return!1;a=eh(a.g);Di(b,c,a===0?void 0:a);return!0},Gi,Sh),Si=Bi(function(a,b,c){if(a.h!==5)return!1;Di(b,c,hh(a.g));return!0},function(a,b,c){b=Jf(b);b!=null&&(Kh(a,c,5),Gh(a.g,b))},Uh),Ti=Bi(function(a,b,c){if(a.h!==0)return!1;Di(b,c,jh(a.g));return!0},function(a,b,c){b=Cf(b);b!=null&&(Kh(a,c,0),a.g.g.push(b?1:0))},Qh),Ui=Bi(function(a,b,c){if(a.h!==2)return!1;var d=fh(a.g);a=a.g;var e=lh(a,d);a=a.h;if(ce){var f=a,g;(g=be)||(g=be=new TextDecoder("utf-8",{fatal:!0}));d=e+d;f=e===0&&d===f.length?f:f.subarray(e,d);try{var h=g.decode(f)}catch(q){if(ae===void 0){try{g.decode(new Uint8Array([128]))}catch(t){}try{g.decode(new Uint8Array([97])),ae=!0}catch(t){ae=!1}}!ae&&(be=void 0);throw q;}}else{h=e;d=h+d;e=[];for(var k=null,l,m;h<d;)l=a[h++],l<128?e.push(l):l<224?h>=d?Zd():(m=a[h++],l<194||(m&192)!==128?(h--,Zd()):e.push((l&31)<<6|m&63)):l<240?h>=d-1?Zd():(m=a[h++],(m&192)!==128||l===224&&m<160||l===237&&m>=160||((g=a[h++])&192)!==128?(h--,Zd()):e.push((l&15)<<12|(m&63)<<6|g&63)):l<=244?h>=d-2?Zd():(m=a[h++],(m&192)!==128||(l<<28)+(m-144)>>30!==0||((g=a[h++])&192)!==128||((f=a[h++])&192)!==128?(h--,Zd()):(l=(l&7)<<18|(m&63)<<12|(g&63)<<6|f&63,l-=65536,e.push((l>>10&1023)+55296,(l&1023)+56320))):Zd(),e.length>=8192&&(k=$d(k,e),e.length=0);h=$d(k,e)}Di(b,c,h);return!0},function(a,b,c){b=Uf(b);if(b!=null){var d=!1;d=d===void 0?!1:d;if(fe){if(d&&(ee?!b.isWellFormed():/(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])/.test(b)))throw Error("Found an unpaired surrogate");b=(de||(de=new TextEncoder)).encode(b)}else{for(var e=0,f=new Uint8Array(3*b.length),g=0;g<b.length;g++){var h=b.charCodeAt(g);if(h<128)f[e++]=h;else{if(h<2048)f[e++]=h>>6|192;else{if(h>=55296&&h<=57343){if(h<=56319&&g<b.length){var k=b.charCodeAt(++g);if(k>=56320&&k<=57343){h=(h-55296)*1024+k-56320+65536;f[e++]=h>>18|240;f[e++]=h>>12&63|128;f[e++]=h>>6&63|128;f[e++]=h&63|128;continue}else g--}if(d)throw Error("Found an unpaired surrogate");h=65533}f[e++]=h>>12|224;f[e++]=h>>6&63|128}f[e++]=h&63|128}}b=e===f.length?f:f.subarray(0,e)}Kh(a,c,2);Eh(a.g,b.length);Ih(a,a.g.end());Ih(a,b)}},Rh),Vi=Bi(function(a,b,c){if(a.h!==0)return!1;Di(b,c,fh(a.g));return!0},function(a,b,c){b=Jf(b);b!=null&&b!=null&&(Kh(a,c,0),Eh(a.g,b))},Th),Wi=Ci(function(a,b,c){if(a.h!==0&&a.h!==2)return!1;b=Fg(b,b[H]|0,c);a.h==2?vh(a,fh,b):b.push(fh(a.g));return!0},function(a,b,c){b=Ai(Jf,b,!0);if(b!=null&&b.length){c=Jh(a,c);for(var d=0;d<b.length;d++)Eh(a.g,b[d]);Lh(a,c)}},Th),Xi=Bi(function(a,b,c){if(a.h!==0)return!1;Di(b,c,eh(a.g));return!0},function(a,b,c){Nh(a,c,If(b))},Zh),Yi=Ci(Hi,function(a,b,c){b=Ai(If,b,!0);if(b!=null)for(var d=0;d<b.length;d++)Nh(a,c,b[d])},Zh),Zi=Ci(Hi,function(a,b,c){b=Ai(If,b,!0);if(b!=null&&b.length){c=Jh(a,c);for(var d=0;d<b.length;d++)Fh(a.g,b[d]);Lh(a,c)}},Zh);function $i(a,b){return function(c,d){var e={ed:!0};d&&Object.assign(e,d);c=ph(c,void 0,void 0,e);try{var f=new a,g=f.C;si(b)(g,c);var h=f}finally{rh(c)}return h}}function aj(a){return function(){var b=new Hh;yi(this.C,b,ni(gi,vi,wi,a));Ih(b,b.g.end());for(var c=new Uint8Array(b.h),d=b.j,e=d.length,f=0,g=0;g<e;g++){var h=d[g];c.set(h,f);f+=h.length}b.j=[c];return c}}function bj(a){return function(b){if(b==null||b=="")b=new a;else{b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");Qe(b,32);b=new a(b)}return b}};function cj(a){this.C=jg(a)}y(cj,$h);cj.prototype.g=aj([0,Ji,Mi,-2,Ri]);function dj(a){this.j=a;this.g=-1;this.h=this.l=0}function ej(a,b){return function(){var c=C.apply(0,arguments);if(a.g>-1)return b.apply(null,A(c));try{return a.g=a.j.h.now(),b.apply(null,A(c))}finally{a.l+=a.j.h.now()-a.g,a.g=-1,a.h+=1}}};function fj(a,b){this.h=a;this.j=b;this.g=new dj(a)};function gj(){this.g={}}function hj(a,b){a=a.g[b.key];if(b.valueType==="proto"){try{var c=JSON.parse(a);if(Array.isArray(c))return c}catch(d){}return b.defaultValue}return typeof a===typeof b.defaultValue?a:b.defaultValue}function ij(a,b){try{var c=JSON.parse(b)[0];b="";for(var d=0;d<c.length;d++)b+=String.fromCharCode(c.charCodeAt(d)^"\u0003\u0007\u0003\u0007\b\u0004\u0004\u0006\u0005\u0003".charCodeAt(d%10));a.g=JSON.parse(b)}catch(e){}};var jj={hh:1,oh:2,gh:3,1:"POSITION",2:"VISIBILITY",3:"MONITOR_VISIBILITY"};function kj(){this.A=void 0;this.Aa=this.G=0;this.D=-1;this.g=new Kb;Gb(Lb(this.g,"mv",vb)).h=wb===void 0?null:wb;Lb(this.g,"omid",sb);Gb(Lb(this.g,"epoh",sb));Gb(Lb(this.g,"epph",sb));Gb(Lb(this.g,"umt",sb)).h=tb===void 0?null:tb;Gb(Lb(this.g,"phel",sb));Gb(Lb(this.g,"phell",sb));Gb(Lb(this.g,"oseid",jj));var a=this.g;a.g.sloi||(a.g.sloi=new Jb);Gb(a.g.sloi);Lb(this.g,"mm",qb);Gb(Lb(this.g,"ovms",pb));Gb(Lb(this.g,"xdi",sb));Gb(Lb(this.g,"amp",sb));Gb(Lb(this.g,"prf",sb));Gb(Lb(this.g,"gtx",sb));Gb(Lb(this.g,"mvp_lv",sb));Gb(Lb(this.g,"ssmol",sb)).h=ub===void 0?null:ub;Gb(Lb(this.g,"fmd",sb));Lb(this.g,"gen204simple",sb);this.h=new fj(Xd(),this.g);this.m=null;this.j=this.l=this.o=!1;this.flags=new gj}function K(){return Ud(kj)};function lj(a,b,c,d){if(Math.random()<(d||a.g))try{if(c instanceof Ld)var e=c;else e=new Ld,Cd(c,function(g,h){var k=e,l=k.l++;g=Md(h,g);k.g.push(l);k.h[l]=g});var f=Pd(e,a.h,"/pagead/gen_204?id="+b+"&");f&&Wd(f)}catch(g){}};function mj(a,b,c){c=c===void 0?{}:c;this.error=a;this.meta=c;this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"};var nj=null;function oj(){var a=a===void 0?Ma:a;return(a=a.performance)&&a.now&&a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function pj(){var a=a===void 0?Ma:a;return(a=a.performance)&&a.now?a.now():null};function qj(a,b,c){this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()};var rj=Ma.performance,sj=!!(rj&&rj.mark&&rj.measure&&rj.clearMarks),tj=id(function(){var a;if(a=sj){var b=b===void 0?window:b;if(nj===null){nj="";try{a="";try{a=b.top.location.hash}catch(d){a=b.location.hash}if(a){var c=a.match(/\bdeid=([\d,]+)/);nj=c?c[1]:""}}catch(d){}}b=nj;a=!!b.indexOf&&b.indexOf("1337")>=0}return a});function uj(){var a=window;this.events=[];this.h=a||Ma;var b=null;a&&(a.google_js_reporting_queue=a.google_js_reporting_queue||[],this.events=a.google_js_reporting_queue,b=a.google_measure_js_timing);this.g=tj()||(b!=null?b:Math.random()<1)}function vj(a){a&&rj&&tj()&&(rj.clearMarks("goog_"+a.label+"_"+a.uniqueId+"_start"),rj.clearMarks("goog_"+a.label+"_"+a.uniqueId+"_end"))}uj.prototype.start=function(a,b){if(!this.g)return null;var c=pj()||oj();a=new qj(a,b,c);b="goog_"+a.label+"_"+a.uniqueId+"_start";rj&&tj()&&rj.mark(b);return a};uj.prototype.end=function(a){if(this.g&&typeof a.value==="number"){var b=pj()||oj();a.duration=b-a.value;b="goog_"+a.label+"_"+a.uniqueId+"_end";rj&&tj()&&rj.mark(b);!this.g||this.events.length>2048||this.events.push(a)}};var wj=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function xj(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(d>=0){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}};function yj(){var a=zj;this.h=Aj;this.Fe="jserror";this.Zd=!0;this.ud=null;this.j=this.Pd;this.g=a===void 0?null:a}function Bj(a,b){var c=Cj;return ej(K().h.g,function(){try{if(c.g&&c.g.g){var d=c.g.start(a.toString(),3);var e=b();c.g.end(d)}else e=b()}catch(g){var f=c.Zd;try{vj(d),f=c.j(a,new Dj(Ej(g)),void 0,void 0)}catch(h){c.Pd(217,h)}if(!f)throw g;}return e})()}function Fj(a,b){return ej(K().h.g,function(){var c=C.apply(0,arguments);return Bj(a,function(){return b.apply(void 0,c)})})}yj.prototype.Pd=function(a,b,c,d,e){e=e||this.Fe;try{var f=new Ld;f.g.push(1);f.h[1]=Md("context",a);b.error&&b.meta&&b.id||(b=new Dj(Ej(b)));if(b.msg){var g=b.msg.substring(0,512);f.g.push(2);f.h[2]=Md("msg",g)}var h=b.meta||{};if(this.ud)try{this.ud(h)}catch(E){}if(d)try{d(h)}catch(E){}d=[h];f.g.push(3);f.h[3]=d;var k=k||Gd();var l=new Hd(Ma.location.href,!1);d=null;var m=k.length-1;for(g=m;g>=0;--g){var q=k[g];!d&&Fd.test(q.url)&&(d=q);if(q.url&&!q.g){l=q;break}}q=null;var t=k.length&&k[m].url;l.depth!==0&&t&&(q=k[m]);var r=new Kd(l,q);if(r.h){var w=r.h.url||"";f.g.push(4);f.h[4]=Md("top",w)}var u={url:r.g.url||""};if(r.g.url){var v=r.g.url.match(wj),B=v[1],P=v[3],I=v[4];k="";B&&(k+=B+":");P&&(k+="//",k+=P,I&&(k+=":"+I));var ca=k}else ca="";u=[u,{url:ca}];f.g.push(5);f.h[5]=u;lj(this.h,e,f,c)}catch(E){try{lj(this.h,e,{context:"ecmserr",rctx:a,msg:Ej(E),url:r&&r.g.url},c)}catch(x){}}return this.Zd};function Ej(a){var b=a.toString();a.name&&b.indexOf(a.name)==-1&&(b+=": "+a.name);a.message&&b.indexOf(a.message)==-1&&(b+=": "+a.message);if(a.stack)a:{a=a.stack;var c=b;try{a.indexOf(c)==-1&&(a=c+"\n"+a);for(var d;a!=d;)d=a,a=a.replace(/((https?:\/..*\/)[^\/:]*:\d+(?:.|\n)*)\2/,"$1");b=a.replace(/\n */g,"\n");break a}catch(e){b=c;break a}b=void 0}return b}function Dj(a){mj.call(this,Error(a),{message:a})}y(Dj,mj);var Aj,Cj,zj=new uj;function Gj(){if(D&&typeof D.google_measure_js_timing!="undefined"&&!D.google_measure_js_timing){var a=zj;a.g=!1;a.events!==a.h.google_js_reporting_queue&&(tj()&&Va(a.events,vj),a.events.length=0)}}Aj=new function(){var a="https:";D&&D.location&&D.location.protocol==="http:"&&(a="http:");this.h=a;this.g=.01};Cj=new yj;D&&D.document&&(D.document.readyState=="complete"?Gj():zj.g&&qd(D,"load",function(){Gj()}));function Hj(a){Cj.ud=function(b){Va(a,function(c){c(b)})}}function Ij(a,b){return Bj(a,b)}function Jj(a,b){return Fj(a,b)}function Kj(a,b,c,d){Cj.Pd(a,b,c,d)};var Lj=Date.now(),Mj=-1,Nj=-1,Oj=!1;function Pj(){return Date.now()-Lj}function Qj(){var a=K().A,b=Nj>=0?Pj()-Nj:-1,c=Oj?Pj()-Mj:-1;if(a==947190542)return 100;if(a==79463069)return 200;a=[2E3,4E3];var d=[250,500,1E3];Kj(637,Error(),.001);var e=b;c!=-1&&c<b&&(e=c);for(b=0;b<a.length;++b)if(e<a[b]){var f=d[b];break}f===void 0&&(f=d[a.length]);return f};function Rj(a,b,c){var d=new G(0,0,0,0);this.time=a;this.volume=null;this.j=b;this.g=d;this.h=null;this.l=c}Rj.prototype.equals=function(a,b){return!!a&&(!(b===void 0?0:b)||this.volume==a.volume)&&this.j==a.j&&jd(this.g,a.g)&&this.h==a.h};function Sj(a,b,c,d,e,f,g,h){this.l=a;this.D=b;this.j=c;this.o=d;this.h=e;this.m=f;this.g=g;this.A=h}Sj.prototype.equals=function(a,b){return this.l.equals(a.l,b===void 0?!1:b)&&this.D==a.D&&jd(this.j,a.j)&&jd(this.o,a.o)&&this.h==a.h&&this.m==a.m&&this.g==a.g&&this.A==a.A};var Tj=["GoogleActiveViewElement","GoogleActiveViewClass","DfaVisibilityIdentifier"];var Ab={kf:"addEventListener",lf:"getMaxSize",mf:"getScreenSize",nf:"getState",qf:"getVersion",sf:"removeEventListener",rf:"isViewable"};function Uj(a){var b=a!==a.top,c=a.top===Ed(a),d=-1,e=0;if(b&&c&&a.top.mraid){d=3;var f=a.top.mraid}else d=(f=a.mraid)?b?c?2:1:0:-1;f&&(f.IS_GMA_SDK||(e=2),zb(function(g){return typeof f[g]==="function"})||(e=1));return{ya:f,Ea:e,La:d}};function Vj(){var a=window.document;return a&&typeof a.elementFromPoint==="function"};function Wj(a,b,c){try{if(a){if(!b.top)return new G(-12245933,-12245933,-12245933,-12245933);b=b.top}a:{var d=b;if(a&&d!==null&&d!=d.top){if(!d.top){var e=new Oc(-12245933,-12245933);break a}d=d.top}try{if(c===void 0?0:c)var f=(new Oc(d.innerWidth,d.innerHeight)).round();else{var g=(d||window).document,h=g.compatMode=="CSS1Compat"?g.documentElement:g.body;f=(new Oc(h.clientWidth,h.clientHeight)).round()}e=f}catch(w){e=new Oc(-12245933,-12245933)}}a=e;var k=a.height,l=a.width;if(l===-12245933)return new G(l,l,l,l);var m=Qc(b.document);var q=Wc(m.g);var t=q.x,r=q.y;return new G(r,t+l,r+k,t)}catch(w){return new G(-12245933,-12245933,-12245933,-12245933)}};function Xj(){var a=Xd().g;return a.g()?-1:a.isVisible()?0:1};function Yj(){var a=bc();return a?Za("AmazonWebAppPlatform;Android TV;Apple TV;AppleTV;BRAVIA;BeyondTV;Freebox;GoogleTV;HbbTV;LongTV;MiBOX;MiTV;NetCast.TV;Netcast;Opera TV;PANASONIC;POV_TV;SMART-TV;SMART_TV;SWTV;Smart TV;SmartTV;TV Store;UnionTV;WebOS".split(";"),function(b){return ac(a,b)})||ac(a,"OMI/")&&!ac(a,"XiaoMi/")?!0:ac(a,"Presto")&&ac(a,"Linux")&&!ac(a,"X11")&&!ac(a,"Android")&&!ac(a,"Mobi"):!1}function Zj(){var a;(a=ac(bc(),"CrKey")&&!(ac(bc(),"CrKey")&&ac(bc(),"SmartSpeaker"))||ac(bc(),"PlayStation")||ac(bc(),"Roku")||Yj()||ac(bc(),"Xbox"))||(a=bc(),a=ac(a,"AppleTV")||ac(a,"Apple TV")||ac(a,"CFNetwork")||ac(a,"tvOS"));a||(a=bc(),a=ac(a,"sdk_google_atv_x86")||ac(a,"Android TV"));return a};function ak(){this.A=!Bd(D.top);this.m=Ad()||zd();var a=Gd();a.length>0&&a[a.length-1]!=null&&a[a.length-1].url!=null&&(a=a[a.length-1].url.match(wj)[3]||null)&&decodeURI(a);this.g=new G(0,0,0,0);this.j=new Oc(0,0);this.o=new Oc(0,0);this.h=new G(0,0,0,0);this.frameOffset=new Nc(0,0);this.D=!1;this.l=!(!D||!Uj(D).ya);this.update(D)}function bk(a,b){b&&b.screen&&(a.j=new Oc(b.screen.width,b.screen.height))}function ck(a,b){a:{var c=a.g?new Oc(a.g.Za(),a.g.Xa()):new Oc(0,0);b=b===void 0?D:b;b!==null&&b!=b.top&&(b=b.top);var d=0,e=0;try{var f=b.document,g=f.body,h=f.documentElement;if(f.compatMode=="CSS1Compat"&&h.scrollHeight)d=h.scrollHeight!=c.height?h.scrollHeight:h.offsetHeight,e=h.scrollWidth!=c.width?h.scrollWidth:h.offsetWidth;else{var k=h.scrollHeight,l=h.scrollWidth,m=h.offsetHeight,q=h.offsetWidth;h.clientHeight!=m&&(k=g.scrollHeight,l=g.scrollWidth,m=g.offsetHeight,q=g.offsetWidth);k>c.height?k>m?(d=k,e=l):(d=m,e=q):k<m?(d=k,e=l):(d=m,e=q)}var t=new Oc(e,d);break a}catch(r){t=new Oc(-12245933,-12245933);break a}t=void 0}a.o=t}ak.prototype.update=function(a){a&&a.document&&(this.h=Wj(!1,a,this.m),this.g=Wj(!0,a,this.m),ck(this,a),bk(this,a))};function dk(){if(ek().D)return!0;var a=Xd().g,b=a.isVisible();a=a.g();return b||a}function ek(){return Ud(ak)};function fk(a){this.j=a;this.h=0;this.g=null}fk.prototype.cancel=function(){Xd().clearTimeout(this.g);this.g=null};fk.prototype.I=function(){var a=this,b=Xd(),c=K().h.g;this.g=b.setTimeout(ej(c,Jj(143,function(){a.h++;a.j.sample()})),Qj())};function gk(a,b,c){this.l=a;this.Ec=c===void 0?"na":c;this.o=[];this.ea=!1;this.h=new Rj(-1,!0,this);this.g=this;this.Ra=b;this.Ua=this.ja=!1;this.Gb="uk";this.kb=!1;this.m=!0}n=gk.prototype;n.Dc=function(){return this.Pa()};n.Pa=function(){return!1};n.Zb=function(){return this.ea=!0};n.Lb=function(){return this.g.Gb};n.Nb=function(){return this.g.Ua};n.fail=function(a,b){if(!this.Ua||(b===void 0?0:b))this.Ua=!0,this.Gb=a,this.Ra=0,this.g!=this||hk(this)};n.ka=function(){return this.g.Ec};n.rb=function(){return this.g.Ed()};n.Ed=function(){return{}};n.Ya=function(){return this.g.Ra};function ik(a,b){Ua(a.o,b)>=0||(a.o.push(b),b.Uc(a.g),b.Ha(a.h),b.ac()&&(a.ja=!0))}n.hd=function(){var a=ek();a.g=Wj(!0,this.l,a.m)};n.jd=function(){bk(ek(),this.l)};n.ie=function(){ck(ek(),this.l)};n.je=function(){var a=ek();a.h=Wj(!1,this.l,a.m)};function jk(a){a=a.g;a.jd();a.hd();a.je();a.ie();a.h.g=a.h.g}function kk(a){var b=Pj(),c=dk();return new Rj(b,c,a)}n.sample=function(){};n.isActive=function(){return this.g.m};function lk(a){a.ja=a.o.length?Za(a.o,function(b){return b.ac()}):!1}function mk(a){var b=eb(a.o);Va(b,function(c){c.Ha(a.h)})}function hk(a){var b=eb(a.o);Va(b,function(c){c.Uc(a.g)});a.g!=a||mk(a)}n.Uc=function(a){var b=this.g;this.g=a.Ya()>=this.Ra?a:this;b!==this.g?(this.m=this.g.m,hk(this)):this.m!==this.g.m&&(this.m=this.g.m,hk(this))};n.Ha=function(a){if(a.l===this.g){var b=!this.h.equals(a,this.ja);this.h=a;b&&mk(this)}};n.ac=function(){return this.ja};n.dispose=function(){this.kb=!0};n.Rb=function(){return this.kb};function nk(a,b,c,d){this.element=a;this.F=new G(0,0,0,0);this.containerGeometry=null;this.o=new G(0,0,0,0);this.g=b;this.D=c;this.G=d;this.A=!1;this.timestamp=-1;this.m=new Sj(b.h,this.element,this.F,new G(0,0,0,0),0,0,Pj(),0);this.h=void 0}n=nk.prototype;n.observe=function(){return!0};n.unobserve=function(){};n.dispose=function(){if(!this.Rb()){var a=this.g;bb(a.o,this);a.ja&&this.ac()&&lk(a);this.unobserve();this.A=!0}};n.Rb=function(){return this.A};n.rb=function(){return this.g.rb()};n.Ya=function(){return this.g.Ya()};n.Lb=function(){return this.g.Lb()};n.Nb=function(){return this.g.Nb()};n.Uc=function(){};n.Ha=function(){ok(this)};n.ac=function(){return this.G};function pk(a){this.m=!1;this.g=a;this.l=function(){}}n=pk.prototype;n.Ya=function(){return this.g.Ya()};n.Lb=function(){return this.g.Lb()};n.Nb=function(){return this.g.Nb()};n.create=function(a,b,c){var d=null;this.g&&(d=this.we(a,b,c),ik(this.g,d));return d};n.ld=function(){return this.kd()};n.kd=function(){return!1};n.init=function(a){return this.g.Zb()?(ik(this.g,this),this.l=a,!0):!1};n.Uc=function(a){a.Ya()==0&&this.l(a.Lb(),this)};n.Ha=function(){};n.ac=function(){return!1};n.dispose=function(){this.m=!0};n.Rb=function(){return this.m};n.rb=function(){return{}};function qk(a,b,c){this.j=c===void 0?0:c;this.h=a;this.g=b==null?"":b}function rk(a){switch(Math.trunc(a.j)){case -16:return-16;case -8:return-8;case 0:return 0;case 8:return 8;case 16:return 16;default:return 16}}function uk(a,b){return a.j<b.j?!0:a.j>b.j?!1:a.h<b.h?!0:a.h>b.h?!1:typeof a.g<typeof b.g?!0:typeof a.g>typeof b.g?!1:a.g<b.g};function vk(){this.j=0;this.g=[];this.h=!1}vk.prototype.add=function(a,b,c){++this.j;a=new qk(a,b,c);this.g.push(new qk(a.h,a.g,a.j+this.j/4096));this.h=!0;return this};function wk(a,b){Va(b.g,function(c){a.add(c.h,c.g,rk(c))})}function xk(a,b){var c=c===void 0?0:c;var d=d===void 0?!0:d;Cd(b,function(e,f){d&&e===void 0||a.add(f,e,c)});return a}function yk(a){var b=zk;a.h&&(gb(a.g,function(c,d){return uk(d,c)?1:uk(c,d)?-1:0}),a.h=!1);return Ya(a.g,function(c,d){d=b(d);return""+c+(c!=""&&d!=""?"&":"")+d})};function zk(a){var b=a.h;a=a.g;if(a!=="")if(typeof a==="boolean")b=a?b:"";else if(Array.isArray(a))b=a.length===0?b:b+"="+a.join();else{var c=Ua(["mtos","tos","p"],b)>=0;b=b+"="+(c?a:encodeURIComponent(a))}return b};function Ak(a){var b=b===void 0?!0:b;this.g=new vk;a!==void 0&&wk(this.g,a);b&&this.g.add("v","unreleased",-16)}Ak.prototype.toString=function(){var a="//pagead2.googlesyndication.com//pagead/gen_204",b=yk(this.g);b.length>0&&(a+="?"+b);return a};function Bk(a,b,c,d,e){var f=[];if(b.length)return f=Xa(b,function(g){return g+"&id="+a});b="//"+(e||"pagead2.googlesyndication.com")+"/activeview";e=[];c&&e.push("avi="+c);d&&e.push("cid="+d);e.push("id="+a);f.push(b+"?"+e.join("&"));return f}function Ck(a){if(!D.navigator||!D.navigator.sendBeacon)return!1;a=a.toString().split("?");return D.navigator.sendBeacon(a[0],a[1])};function Dk(){this.g=0};function Ek(){this.R=this.R;this.Va=this.Va}Ek.prototype.R=!1;Ek.prototype.Rb=function(){return this.R};Ek.prototype.dispose=function(){this.R||(this.R=!0,this.qc())};Ek.prototype[Symbol.dispose]=function(){this.dispose()};Ek.prototype.qc=function(){if(this.Va)for(;this.Va.length;)this.Va.shift()()};function Fk(a,b,c){Va(a.j,function(d){var e=a.g;if(!d.g&&(d.j(b,c),d.l())){d.g=!0;var f=d.h(),g=new vk;g.add("id","av-js");g.add("type","verif");g.add("vtype",d.m);d=Ud(Dk);g.add("i",d.g++);g.add("adk",e);xk(g,f);e=new Ak(g);Wd(e.toString())}})};function Gk(a){this.C=jg(a)}y(Gk,$h);var Hk=[0,Li,Si,-1];function Ik(){this.g=this.h=this.j=0}Ik.prototype.update=function(a,b,c){a&&(this.j+=b,this.h+=b,this.g=Math.max(this.g,this.h));if(c===void 0?!a:c)this.h=0};var Jk=[1,.75,.5,.3,0];function Kk(a){this.g=a=a===void 0?Jk:a;this.h=Xa(this.g,function(){return new Ik})}function Lk(a){return Mk(a,function(b){return b.j},!1)}function Nk(a){return Mk(a,function(b){return b.g},!0)}Kk.prototype.update=function(a,b,c,d,e,f){f=f===void 0?!0:f;b=e?Math.min(a,b):b;for(e=0;e<this.g.length;e++){var g=this.g[e],h=b>0&&b>=g;g=!(a>0&&a>=g)||c;this.h[e].update(f&&h,d,!f||g)}};function Mk(a,b,c){a=Xa(a.h,function(d){return b(d)});return c?a:Ok(a)}function Ok(a){return Xa(a,function(b,c,d){return c>0?d[c]-d[c-1]:d[c]})};function Pk(){this.Ma=new Kk;this.ib=this.hb=0;this.g=new Ik;this.h=-1;this.j=new Kk([1,.9,.8,.7,.6,.5,.4,.3,.2,.1,0])}Pk.prototype.update=function(a,b,c,d){this.h=Math.max(this.h,b.W);this.j.update(b.j,c.j,b.h,a,d);this.hb+=a;b.W===0&&(this.ib+=a);this.Ma.update(b.W,c.W,b.h,a,d);c=d||c.ca!=b.ca?c.isVisible()&&b.isVisible():c.isVisible();b=!b.isVisible()||b.h;this.g.update(c,a,b)};function Qk(a){return a.g.g>=1E3};if(Ub&&Ub.URL){var Rk=Ub.URL,Sk;if(Sk=!!Rk){var Tk;a:{if(Rk){var Uk=RegExp(".*[&#?]google_debug(=[^&]*)?(&.*)?$");try{var Vk=Uk.exec(decodeURIComponent(Rk));if(Vk){Tk=Vk[1]&&Vk[1].length>1?Vk[1].substring(1):"true";break a}}catch(a){}}Tk=""}Sk=Tk.length>0}Cj.Zd=!Sk}function Wk(a,b,c,d){var e=e===void 0?!1:e;c=Fj(d,c);qd(a,b,c,{capture:e})};var Xk=new G(0,0,0,0);function Yk(a,b){b=Zk(b);return b===0?0:Zk(a)/b}function Zk(a){return Math.max(a.g-a.top,0)*Math.max(a.h-a.left,0)}function $k(a,b){if(!a||!b)return!1;for(var c=0;a!==null&&c++<100;){if(a===b)return!0;try{if(a=a.parentElement||a){var d=Vc(a),e=d&&(d?d.defaultView:window),f=e&&e.frameElement;f&&(a=f)}}catch(g){break}}return!1}function al(a,b,c){if(!a||!b)return!1;b=kd(a.clone(),-b.left,-b.top);a=(b.left+b.h)/2;b=(b.top+b.g)/2;Bd(window.top)&&window.top&&window.top.document&&(window=window.top);if(!Vj())return!1;a=window.document.elementFromPoint(a,b);if(!a)return!1;b=(b=(b=Vc(c))&&b.defaultView&&b.defaultView.frameElement)&&$k(b,a);var d=a===c;a=!d&&a&&Yc(a,function(e){return e===c});return!(b||d||a)}function bl(a,b,c,d){return ek().A?!1:a.Za()<=0||a.Xa()<=0?!0:c&&d?Ij(208,function(){return al(a,b,c)}):!1};var cl=new G(0,0,0,0);function dl(a,b,c){Ek.call(this);this.position=cl.clone();this.g=new Pk;this.Sb=-2;Date.now();this.Ie=-1;this.ve=b;this.Sa=null;this.V=!1;this.Zc=null;this.bc=-1;this.T=c;this.Hb=!1;this.Gb=function(){};this.Je=function(){};this.H=new Zc;this.H.P=a;this.H.g=a;this.m=!1;this.A={Rd:null,Qd:null};this.Wb=!0;this.da=null;K().G++;this.h=new rb;this.Ae=this.kb=-1;this.Zf=0;this.N=null;this.Ec=this.se=!1;this.j=new Kb;Tb(this.j);el(this);this.T==1?Nb(this.j,1):Nb(this.j,0)}y(dl,Ek);n=dl.prototype;n.qc=function(){fl(this);this.da&&this.da.dispose();this.N&&this.N.dispose();delete this.g;delete this.Gb;delete this.Je;delete this.H.P;delete this.H.g;delete this.A;delete this.da;delete this.N;delete this.j;Ek.prototype.qc.call(this)};function gl(a){return a.N?a.N.F:a.position}n.nd=function(a){var b=K();typeof a==="string"&&a.length!=0&&Qb(b.g,a)};function el(a){(a=a.H.P)&&a.getAttribute&&Mc(a,"googleAvInapp")&&(ek().l=!0)}n.Xd=function(){this.V=!0};n.sc=function(){return this.V};n.Vd=function(){this.h.W=0};function hl(a,b){if(a.N){if(b.ka()===a.N.ka())return;a.N.dispose();a.N=null}b=b.create(a.H.g,a.j,!1);if(b=b!=null&&b.observe()?b:null)a.N=b}function il(a,b,c){if(a.N){ok(a.N);var d=a.N.m,e=d.l,f=e.g;if(d.o!=null){var g=d.j;a.Zc=new Nc(g.left-f.left,g.top-f.top)}f=a.cd()?Math.max(d.h,d.m):d.h;g={};e.volume!==null&&(g.volume=e.volume);a.Sa&&a.ve!=-1&&d.g!==-1&&a.Sa.g!==-1?(e=d.g-a.Sa.g,e=e>1E4?0:e):e=0;a.Sa=d;a.ee(f,b,c,!1,g,e,d.A)}}function jl(a){if(a.sc()&&a.da){var b=Ob(a.j,"od")==1,c=ek().g,d=a.da,e=a.N?a.N.ka():"ns",f=a.Zc,g=new Oc(c.Za(),c.Xa());c=a.cd();a={Fg:e,Zc:f,Og:g,cd:c,W:a.h.W,Kg:b};if(b=d.h){ok(b);e=b.m;f=e.l.g;var h=g=null;e.o!=null&&f&&(g=e.j,g=new Nc(g.left-f.left,g.top-f.top),h=new Oc(f.h-f.left,f.g-f.top));c={Fg:b.ka(),Zc:g,Og:h,cd:c,Kg:!1,W:c?Math.max(e.h,e.m):e.h}}else c=null;c&&Fk(d,a,c)}}n.ee=function(a,b,c,d,e,f,g){this.m||(this.sc()&&(e=new rb,e.h=c,e.g=Xj(),e.W=this.bc===0&&Ob(this.j,"opac")===1?0:a,e.ca=this.ca(),e.j=g,a=d&&this.h.W>=(this.ca()?.3:.5),this.g.update(f,e,this.h,a),this.ve=b,e.W>0&&(-1===this.kb&&(this.kb=b),this.Ae=b),this.Ie==-1&&Qk(this.g)&&(this.Ie=b),this.Sb==-2&&(this.Sb=Zk(gl(this))?e.W:-1),this.h=e),this.Gb(this))};n.ca=function(){return!1};n.cd=function(){return this.se||!1};function kl(a){var b=a.V;b=(a.Ec||a.Rb())&&!b;var c=K().Aa!==2||a.Hb;return a.m||c&&b?2:Qk(a.g)?4:3}function fl(a){a.H.g&&(a.A.Rd&&(rd(a.H.g,"mouseover",a.A.Rd),a.A.Rd=null),a.A.Qd&&(rd(a.H.g,"mouseout",a.A.Qd),a.A.Qd=null))}function ll(a,b,c){b&&(a.Gb=b);c&&(a.Je=c)};function ml(){this.j=!0;this.h=[]}ml.prototype.isVisible=function(){return this.j};ml.prototype.g=function(){return!1};ml.prototype.l=function(a){this.h.push(a);return!0};ml.prototype.m=function(a){this.h=Wa(this.h,function(b){return a!==b})};function nl(a){return jc()?(a=(a=Vc(a))&&(a?a.defaultView:window),!!(a&&a.location&&a.location.ancestorOrigins&&a.location.ancestorOrigins.length>0&&a.location.origin==a.location.ancestorOrigins[0])):!0};function ol(){}ol.prototype.next=function(){return pl};var pl={done:!0,value:void 0};ol.prototype.ke=function(){return this};function ql(a){if(a instanceof ol)return a;if(typeof a.ke=="function")return a.ke(!1);if(Oa(a)){var b=0,c=new ol;c.next=function(){for(;;){if(b>=a.length)return pl;if(b in a)return{value:a[b++],done:!1};b++}};return c}throw Error("Not implemented");}function rl(a,b){if(Oa(a))Va(a,b);else for(a=ql(a);;){var c=a.next();if(c.done)break;b.call(void 0,c.value,void 0,a)}}function sl(a,b){var c=1;rl(a,function(d){c=b.call(void 0,c,d)});return c}function tl(a,b){var c=ql(a);a=new ol;a.next=function(){var d=c.next(),e=d.value;return d.done?pl:b.call(void 0,e,void 0,c)?{value:e,done:!1}:pl};return a}function ul(a){var b=ql(a);a=new ol;var c=100;a.next=function(){return c-- >0?b.next():pl};return a};function vl(a,b){this.j=b;this.h=a==null;this.g=a}y(vl,ol);vl.prototype.next=function(){if(this.h)return pl;var a=this.g||null;this.h=a==null;var b;if(b=a){b=this.j;if(Fc(a,"parentElement")&&a.parentElement!=null&&a!=a.parentElement)var c=a.parentElement;else if(b){var d=d===void 0?nl:d;if(d(a))try{var e=Vc(a),f=e&&(e?e.defaultView:window),g=f&&f.frameElement;c=g==null?null:g}catch(h){c=null}else c=null}else c=null;b=c}this.g=b;return{value:a,done:!1}};function wl(a){var b=1;a=ul(new vl(a,!0));a=tl(a,function(){return b>0});return sl(a,function(c,d){var e=1;if(Fc(d,"style")&&d.style){var f=parseFloat;a:{var g=Vc(d);if(g.defaultView&&g.defaultView.getComputedStyle&&(g=g.defaultView.getComputedStyle(d,null))){g=g.opacity||g.getPropertyValue("opacity")||"";break a}g=""}if(!g){g=d.style[yc()];if(typeof g!=="undefined")d=g;else{g=d.style;var h=ld.opacity;if(!h){var k=yc();h=k;d.style[k]===void 0&&(k=(Ic?"Webkit":Hc?"Moz":null)+Ac(k),d.style[k]!==void 0&&(h=k));ld.opacity=h}d=g[h]||""}g=d}f=f(g);typeof f!=="number"||isNaN(f)||(e=f)}return b=c*e})};function xl(){this.h=!1}xl.prototype.g=function(a,b){b=b===void 0?{}:b;this.h||(this.h=this.j(a,b))};xl.prototype.j=function(){return!1};function yl(a,b,c,d,e,f,g,h,k){h=h===void 0?[]:h;k=k===void 0?"":k;dl.call(this,c,d,e);this.Fb=b;this.Jb=0;this.zf=null;this.Oa=this.ta="";this.D=[];this.Na=[];this.ja=this.cc="";this.qe=!1;this.kg=[];this.G=this.o="";this.Qa=!1;this.jb=[];this.l=this.Ne=!1;this.Ra=0;this.ea=this.dc=Xj();this.Ta=0;this.Ua=f;this.Fc=this.ye=!1;this.og=k;this.lg=h;if(a=this.H.P)this.Jb==0?this.H.P?(b=this.H.P._adk_,b||(b=(b=Lc(this.H.P,"googleAvAdk"))&&!/[^0-9]/.test(b)?parseInt(b,10):0)):b=0:b=this.Jb,this.Jb=b,this.ta==""&&(this.ta=String(a._avi_||"")),this.Oa==""&&(this.Oa=a._avihost_?String(a._avihost_):"pagead2.googlesyndication.com"),this.D.length||(this.D=zl(a,"_avicxn_","googleAvCxn")),this.Na.length||(this.Na=zl(a,"_avieoscxn_","googleEOSAvCxn")),this.cc==""&&(this.cc=String(a._aviextcxn_||Lc(a,"googleAvExtCxn")||"")),this.ja==""&&(this.ja=String(a._cid_||"")),this.qe||(this.qe=!!a._imm_||Mc(a,"googleAvImmediate")),this.G==""&&(this.G=String(a._cvu_||Lc(a,"googleAvCpmav")||"")),this.o==""&&(this.o=String(Lc(a,"googleAvBtr")||"")),this.jb.length||(this.jb=zl(a,"","googleAvVrus")),this.nd(String(a._avm_||Lc(a,"googleAvMetadata")||"")),a=String(Lc(a,"googleAvFlags")||""),b=K(),typeof a==="string"&&a.length!=0&&ij(b.flags,a);Sb(K().g,this.Fb)}y(yl,dl);function zl(a,b,c){return(a=String(a[b]||Lc(a,c)||""))?a.split("|"):[]}n=yl.prototype;n.qc=function(){delete this.kg;dl.prototype.qc.call(this)};function Al(a){var b={},c=K();(Ob(c.g,"omid")!==1||c.j)&&Va(a.lg,function(d){return d.g(a,b)})}n.sc=function(){return this.V&&!(this.Ta==1||this.Ta==3)};n.Vd=function(){dl.prototype.Vd.call(this)};n.Xd=function(){this.V||(oj(),this.Ua!==void 0&&this.Ua(!1,this.Sb),this.o!=null&&this.o!=""&&(Sd(this.o),this.o=""));dl.prototype.Xd.call(this)};n.nd=function(a){if(typeof a==="string"&&a.length!=0){var b=new Kb,c=K();Lb(b,"omid",sb);Qb(b,a);b=Ob(b,"omid");b!==null&&(c.g.h.omid=b);a=Qb(this.j,a);c=a.split("&");for(b=0;b<c.length;b++){var d=c[b];d=="ts=0"?this.Wb=!1:d.lastIndexOf("la=",0)==0?(d=d.split("=")[1],d=="0"?this.Ra=2:d=="1"&&(this.Ra=1)):d.lastIndexOf("cr=",0)==0&&d.split("=")[1]=="1"&&(this.se=!0)}this.h.ca=this.ca();dl.prototype.nd.call(this,a)}};n.ee=function(a,b,c,d,e,f,g){var h=Qk(this.g),k=Math.floor(this.h.W*100);this.Ra=Zk(gl(this))>=242500?1:2;dl.prototype.ee.call(this,a,b,c,d,e,f,g);this.ea==-1&&this.h.g!=-1?this.ea=this.h.g:this.ea==0&&this.h.g==1&&(this.ea=1);a=Qk(this.g);b=Math.floor(this.h.W*100);Ob(K().g,"gen204simple")&&!a&&!this.Fc&&k>=50&&b<50&&(Wd("https://pagead2.googlesyndication.com/pagead/gen_204?id=av-yt&bin=simple&type=nonVisible&mcvt="+(this.g.g.g+"&uuid="+this.og)),this.Fc=!0);(!h&&a||b!=k)&&this.Ua!==void 0&&this.Ua(a,b);try{this.bc=wl(this.H.g)}catch(l){}};n.ca=function(){return Jc?!1:this.Ra==1};function Bl(a,b){switch(b){case 1:if(a.D.length)return a.D;break;case 2:if(a.Na.length)return a.Na;if(a.D.length)return a.D}return[]}function Cl(a){var b=ek(),c=Pb(a.j),d=b.frameOffset,e=gl(a);c.p=[e.top+d.y,e.left+d.x,e.g+d.y,e.h+d.x];d=a.g;c.tos=Lk(d.Ma);e=Nk(d.Ma);c.mtos=e;c.mcvt=d.g.g;c.rs=a.T;(e=a.T==5)||(c.ht=a.Zf);a.kb>=0&&(c.tfs=a.kb,c.tls=a.Ae);c.mc=Math.floor(d.h*100)/100;c.lte=Math.floor(a.Sb*100)/100;c.bas=a.dc;c.bac=a.ea;b.A&&(c["if"]=a.m?0:1);c.met=a.H.h;e&&a.Fb&&(c.req=encodeURIComponent(a.Fb).substring(0,100));a.ca()&&(c.la="1");c.avms=a.N?a.N.ka():"ns";a.N&&Eb(c,a.N.rb());a.ye&&(c.radf="1");a.Ta!=0&&(c.md=a.Ta);c.btr=a.o!=null&&a.o!=""?1:0;c.cpmav=Dl(a)?1:0;Xd().g.isVisible()||(c.pv=0);c.abdbg=[a.m?0:1,a.sc()?1:0].join(";");c.tm=a.g.hb;c.tu=a.g.ib;return c}function Dl(a){return a.G!=null&&a.G.match(/\/pagead\/adview\?.*ai=.*&vt=\d+/i)!=null};function El(a){var b=[0,2,4],c=!1,d=Fl;c=c===void 0?!0:c;d=d===void 0?function(){return!0}:d;return function(e){var f=e[a];if(Array.isArray(f)&&d(e))return Gl(f,b,c)}}function Hl(a,b){return function(c){return b(c)?c[a]:void 0}}function Il(a){return function(b){for(var c=0;c<a.length;c++)if(a[c]===b.e||a[c]===void 0&&!b.hasOwnProperty("e"))return!0;return!1}}function Gl(a,b,c){return c===void 0||c?Wa(a,function(d,e){return Ua(b,e)>=0}):Xa(b,function(d,e,f){return a.slice(e>0?f[e-1]+1:0,d+1).reduce(function(g,h){return g+h},0)})};var Jl=Il([void 0,1,2,3,4,8,16]),Fl=Il([void 0,4,8,16]);Object.assign({},{sv:"sv",v:"v",cb:"cb",e:"e",nas:"nas",msg:"msg","if":"if",sdk:"sdk",p:"p",p0:Hl("p0",Fl),p1:Hl("p1",Fl),p2:Hl("p2",Fl),p3:Hl("p3",Fl),cp:"cp",tos:"tos",mtos:"mtos",amtos:"amtos",mtos1:El("mtos1"),mtos2:El("mtos2"),mtos3:El("mtos3"),mcvt:"mcvt",ps:"ps",scs:"scs",bs:"bs",vht:"vht",mut:"mut",a:"a",a0:Hl("a0",Fl),a1:Hl("a1",Fl),a2:Hl("a2",Fl),a3:Hl("a3",Fl),ft:"ft",dft:"dft",at:"at",dat:"dat",as:"as",vpt:"vpt",gmm:"gmm",std:"std",efpf:"efpf",swf:"swf",nio:"nio",px:"px",nnut:"nnut",vmer:"vmer",vmmk:"vmmk",vmiec:"vmiec",nmt:"nmt",tcm:"tcm",bt:"bt",pst:"pst",vpaid:"vpaid",dur:"dur",vmtime:"vmtime",dtos:"dtos",dtoss:"dtoss",dvs:"dvs",dfvs:"dfvs",dvpt:"dvpt",fmf:"fmf",vds:"vds",is:"is",i0:"i0",i1:"i1",i2:"i2",i3:"i3",ic:"ic",cs:"cs",c:"c",c0:Hl("c0",Fl),c1:Hl("c1",Fl),c2:Hl("c2",Fl),c3:Hl("c3",Fl),mc:"mc",nc:"nc",mv:"mv",nv:"nv",qmt:Hl("qmtos",Jl),qnc:Hl("qnc",Jl),qmv:Hl("qmv",Jl),qnv:Hl("qnv",Jl),raf:"raf",rafc:"rafc",lte:"lte",ces:"ces",tth:"tth",femt:"femt",femvt:"femvt",emc:"emc",emuc:"emuc",emb:"emb",avms:"avms",nvat:"nvat",qi:"qi",psm:"psm",psv:"psv",psfv:"psfv",psa:"psa",pnk:"pnk",pnc:"pnc",pnmm:"pnmm",pns:"pns",ptlt:"ptlt",pngs:"pings",veid:"veid",ssb:"ssb",ss0:Hl("ss0",Fl),ss1:Hl("ss1",Fl),ss2:Hl("ss2",Fl),ss3:Hl("ss3",Fl),dc_rfl:"urlsigs",obd:"obd",omidp:"omidp",omidr:"omidr",omidv:"omidv",omida:"omida",omids:"omids",omidpv:"omidpv",omidam:"omidam",omidct:"omidct",omidia:"omidia",omiddc:"omiddc",omidlat:"omidlat",omiddit:"omiddit",nopd:"nopd",co:"co",tm:"tm",tu:"tu"},{avid:function(a){return function(){return a}}("audio"),avas:"avas",vs:"vs"});function Kl(a,b,c,d){nk.call(this,a,b,c,d)}y(Kl,nk);Kl.prototype.xd=function(a){var b;return Yk(a,(b=this.h)!=null?b:this.F)};Kl.prototype.wd=function(a){return Yk(a,ek().g)};Kl.prototype.vd=function(a,b){return Yk(a,b)};function ok(a){a.timestamp=Pj();if(a.element){var b=a.element,c=a.g.g.l;try{try{var d=b.getBoundingClientRect();var e=new G(d.top,d.right,d.bottom,d.left)}catch(q){e=new G(0,0,0,0)}var f=e.h-e.left,g=e.g-e.top,h=nd(b,c),k=h.x,l=h.y;var m=new G(Math.round(l),Math.round(k+f),Math.round(l+g),Math.round(k))}catch(q){m=Xk.clone()}a.containerGeometry=m;m=a.containerGeometry;m=a.h?new G(Math.max(m.top+a.h.top,m.top),Math.min(m.left+a.h.h,m.h),Math.min(m.top+a.h.g,m.g),Math.max(m.left+a.h.left,m.left)):m.clone();a.F=m}a.element&&typeof a.element.videoWidth==="number"&&typeof a.element.videoHeight==="number"&&(m=a.element,g=new Oc(m.videoWidth,m.videoHeight),m=a.F,b=m.Za(),c=m.Xa(),f=g.width,g=g.height,f<=0||g<=0||b<=0||c<=0||(f/=g,g=b/c,m=m.clone(),f>g?(b/=f,c=(c-b)/2,c>0&&(c=m.top+c,m.top=Math.round(c),m.g=Math.round(c+b))):(c*=f,b=Math.round((b-c)/2),b>0&&(b=m.left+b,m.left=Math.round(b),m.h=Math.round(b+c)))),a.F=m);a.o=a.g.h.g;m=a.F;b=a.o;m=m.left<=b.h&&b.left<=m.h&&m.top<=b.g&&b.top<=m.g?new G(Math.max(m.top,b.top),Math.min(m.h,b.h),Math.min(m.g,b.g),Math.max(m.left,b.left)):new G(0,0,0,0);b=m.top>=m.g||m.left>=m.h?new G(0,0,0,0):m;m=a.g.h;g=f=c=0;(a.F.g-a.F.top)*(a.F.h-a.F.left)>0&&(bl(b,a.o,a.element,Ob(a.D,"od")==1)?b=new G(0,0,0,0):(c=ek().j,g=new G(0,c.height,c.width,0),c=a.xd(b),f=a.wd(b),g=a.vd(b,g)));b=b.top>=b.g||b.left>=b.h?new G(0,0,0,0):kd(b,-a.F.left,-a.F.top);dk()||(f=c=0);a.m=new Sj(m,a.element,a.F,b,c,f,a.timestamp,g)}Kl.prototype.ka=function(){return this.g.ka()};function Ll(){this.h=[];this.g=[]}function Ml(a){var b=Nl;return a?ab(b.g,function(c){return c.H.P==a}):null}function Ol(){var a=Nl;return a.h.length==0?a.g:a.g.length==0?a.h:cb(a.g,a.h)}Ll.prototype.reset=function(){this.h=[];this.g=[]};function Pl(a){var b=[];Va(a,function(c){c.H.P&&Ml(c.H.P)==null&&(Nl.g.push(c),b.push(c))})}var Nl=Ud(Ll);function Ql(){this.g=this.h=null}function Rl(a,b){function c(d,e){b(d,e)}if(a.h==null)return!1;a.g=ab(a.h,function(d){return d!=null&&d.ld()});a.g&&(a.g.init(c)?jk(a.g.g):b(a.g.g.Lb(),a.g));return a.g!=null};function Sl(a){a=Tl(a);pk.call(this,a.length?a[a.length-1]:new gk(D,0));this.j=a;this.h=null}y(Sl,pk);n=Sl.prototype;n.ka=function(){return(this.h?this.h:this.g).ka()};n.rb=function(){return(this.h?this.h:this.g).rb()};n.Ya=function(){return(this.h?this.h:this.g).Ya()};n.init=function(a){var b=!1;Va(this.j,function(c){c.Zb()&&(b=!0)});b&&(this.l=a,ik(this.g,this));return b};n.dispose=function(){Va(this.j,function(a){a.dispose()});pk.prototype.dispose.call(this)};n.ld=function(){return Za(this.j,function(a){return a.Dc()})};n.kd=function(){return Za(this.j,function(a){return a.Pa()})};n.we=function(a,b,c){return new Kl(a,this.g,b,c)};n.Ha=function(a){this.h=a.l};function Tl(a){if(!a.length)return[];a=Wa(a,function(c){return c!=null&&c.Dc()});for(var b=1;b<a.length;b++)ik(a[b-1],a[b]);return a};function Ul(){};function Vl(){this.done=!1;this.g={sd:0,rd:0,Th:0,ze:0,Id:-1,If:0,Hf:0,Jf:0,Ze:0};this.h=null;this.o=!1;this.l=null;this.D=0;this.j=new fk(this);this.m=null}function Wl(){var a=Xl;a.o||(a.o=!0,Yl(a,function(){return a.A.apply(a,A(C.apply(0,arguments)))}),a.A())}Vl.prototype.sample=function(){Zl(this,Ol(),!1)};function $l(){Ud(Ul);var a=Ud(Ql);a.g!=null&&a.g.g?jk(a.g.g):ek().update(D)}function Zl(a,b,c){if(!a.done&&(a.j.cancel(),b.length!=0)){a.l=null;try{$l();var d=Pj();K().D=d;if(Ud(Ql).g!=null)for(var e=0;e<b.length;e++)il(b[e],d,c);for(d=0;d<b.length;d++)jl(b[d]);++a.g.ze}finally{c?Va(b,function(f){return f.Vd()}):a.j.I()}}}function Yl(a,b){a.h||(b=Fj(142,b),Xd().g.l(b)&&(a.h=b))}Vl.prototype.A=function(){var a=dk(),b=Pj();a?(Oj||(Mj=b,Va(Nl.h,function(c){return c.g.m(b,!c.l())})),Oj=!0):(this.D=am(this,b),Oj=!1,Va(Nl.h,function(c){c.sc()&&c.g.l(b)}));Zl(this,Ol(),!a)};function bm(a){var b=Xl;if(!b.l||a){var c=D.document,d=Nj>=0?Pj()-Nj:-1;a=Pj();b.g.Id==-1&&(d=a);var e=ek(),f=K(),g=Pb(f.g),h=Ol();try{if(h.length>0){var k=e.g;k&&(g.bs=[k.Za(),k.Xa()]);var l=e.o;l&&(g.ps=[l.width,l.height]);D.screen&&(g.scs=[D.screen.width,D.screen.height])}else g.url=encodeURIComponent(D.location.href.substring(0,512)),c.referrer&&(g.referrer=encodeURIComponent(c.referrer.substring(0,512)));g.tt=d;g.pt=Nj;g.bin=f.Aa;D.google_osd_load_pub_page_exp!==void 0&&(g.olpp=D.google_osd_load_pub_page_exp);k=-1;b.m!==null&&(k=b.m()?1:0);var m=[1,b.g.sd,b.g.rd,b.g.ze,b.g.Id,0,b.j.h,b.g.If,b.g.Hf,b.g.Jf,b.g.Ze,k].join(";");g.deb=m;g.tvt=am(b,a);e.l&&(g.inapp=1);if(D!==null&&D!=D.top){h.length>0&&(g.iframe_loc=encodeURIComponent(D.location.href.substring(0,512)));var q=e.h;g.is=[q.Za(),q.Xa()]}}catch(t){g.error=1}b.l=g}b=Cb(b.l);m=K().h;if(Ob(m.j,"prf")==1){q=new cj;a=m.g;e=0;a.g>-1&&(e=a.j.h.now()-a.g);a=a.l+e;if(a!=null&&typeof a!=="number")throw Error("Value of float/double field must be a number, found "+typeof a+": "+a);q=Eg(q,1,a,0);a=m.g;q=Eg(q,5,Hf(a.g>-1?a.h+1:a.h),0);q=Tg(q,2,m.h.h.j());q=Tg(q,3,m.h.h.h());m=Tg(q,4,m.h.h.g());q={};m=(q.pf=ke(m.g()),q)}else m={};Eb(b,m);return b}function cm(){Va(Ol(),function(a){a.H.P&&Ud(Ul)})}function dm(){var a=Ud(Ql);if(a.g!=null){var b=Ol(),c=a.g;Va(b,function(d){return hl(d,c)})}}function am(a,b){a=a.D;Oj&&(a+=b-Mj);return a}function em(){var a=a===void 0?function(){return{}}:a;Cj.Fe="av-js";Aj.g=.01;Hj([function(b){var c=K(),d={};Eb(b,(d.bin=c.Aa,d.type="error",d),Pb(c.g),bm(),a())}])}var Xl=Ud(Vl);var fm=null;function gm(a){var b=fm||D;if(!b)return"";var c=[];if(a===void 0||!a){if(!b.location||!b.location.href)return"";c.push("url="+encodeURIComponent(b.location.href.substring(0,512)))}b.document&&b.document.referrer&&c.push("referrer="+encodeURIComponent(b.document.referrer.substring(0,512)));return c.join("&")};function hm(a){var b={};b.adk=a.Jb||1;Eb(b,Cl(a));Xl.g.sd=D.__google_lidar_;var c=bm();Eb(b,c);c=gm(c.url!==void 0);xj(c,function(d,e){return b[d]=e});b.itpl=Number(Lc(a.H.P,"googleAvItpl"))||0;return b};var im=/(?:\[|%5B)([a-zA-Z0-9_]+)(?:\]|%5D)/g;function jm(a,b){return a.replace(im,function(c,d){try{var e=b!==null&&d in b?b[d]:void 0;if(e==null||e.toString()==null)return c;e=e.toString();if(e==""||!/^[\s\xa0]*$/.test(e==null?"":String(e)))return encodeURIComponent(e).replace(/%2C/g,",")}catch(f){}return c})};function km(a){this.h=a}function lm(a,b,c){return c.T===14||c.T===16||c.T===17?(c={},c.VIEWABILITY=b,jm(a,c)):a+"&"+b}km.prototype.g=function(a,b,c){var d=this.h(a);Eb(d,yb(c,function(e,f){return f!="id"}));d=d!==void 0?yk(xk(new vk,d)):"";b=Bk(c.id,Bl(a,b),a.ta,a.ja,a.Oa);b=z(b);for(c=b.next();!c.done;c=b.next())if(c=c.value)c=lm(c,d,a),Ob(a.j,"sbeos")==1?Ck(c)||Wd((c.toString()+"&sberr=1").toString()):Wd(c.toString());return!0};function mm(){}function nm(a,b,c){return c.T===14||c.T===16||c.T===17?(c={},c.VIEWABILITY=b,jm(a,c)):a+"&"+b}mm.prototype.g=function(a,b,c){var d=hm(a);Eb(d,yb(c,function(e,f){return f!="id"}));d=d!==void 0?yk(xk(new vk,d)):"";b=Bk(c.id,Bl(a,b),a.ta,a.ja,a.Oa);b=z(b);for(c=b.next();!c.done;c=b.next())if(c=c.value)c=nm(c,d,a),Wd(c.toString());return!0};function om(a,b){this.h=!1;this.o=a;this.m=b}y(om,xl);om.prototype.j=function(a,b){b.id=this.m;b.vs=kl(a);var c=this.m==="lidar2"?1:2;return this.l(a)?this.o.g(a,c,b):!1};om.prototype.l=function(){return!0};function pm(a){om.call(this,a,"lidartos")}y(pm,om);pm.prototype.j=function(a,b){var c="";a.l&&(c+="a");a.Qa&&(c+="c");b.sent=c;return om.prototype.j.call(this,a,b)};pm.prototype.l=function(a){return a.Wb&&!a.m&&Qk(a.g)};function qm(a){om.call(this,a,"lidar2")}y(qm,om);qm.prototype.l=function(a){return a.m};function rm(a,b,c){var d=gm(b.url!==void 0);xj(d,function(e,f){return b[e]=f});Va(a,function(e,f){var g=kl(e);if(g!=3||e.T!=5)b.adk=e.Jb||f+1,Eb(b,Cl(e)),c&&(b.avms=c.ka()),b.vs=g,b.itpl=Number(Lc(e.H.P,"googleAvItpl"))||0,f=new km(function(){return Cb(b)}),e.Wb&&!e.m&&Qk(e.g)?(g={},f.g(e,2,(g.id="lidar2",g.tsf=1,g)),e.Wb=!1):(g={},f.g(e,1,(g.id="lidar2",g)),e.l=!0)})}function sm(a,b){Va(a,function(c,d){(new pm(new km(function(){b.adk=c.Jb||d+1;Eb(b,Cl(c));b.vs=kl(c);b.itpl=Number(Lc(c.H.P,"googleAvItpl"))||0;return b}))).g(c);c.Wb=!1})};function tm(a){om.call(this,a,"lidar2")}y(tm,om);tm.prototype.g=function(a,b){b=b===void 0?{}:b;b.r="v";om.prototype.g.call(this,a,b);a.l=a.l||this.h};tm.prototype.l=function(a){return Qk(a.g)&&!a.l};var um=["FRAME","IMG","IFRAME"],vm=/^[01](px)?$/;function wm(){this.h=this.g=!1}function xm(){var a=new wm;a.g=!0;return a}function ym(){var a=xm();a.h=!0;return a}function zm(a){return typeof a==="string"?document.getElementById(a):a}function Am(a){var b=!1;b=b===void 0?!1:b;if(a.tagName==="IMG"){if(a.complete&&(!a.naturalWidth||!a.naturalHeight))return!0;var c;if(b&&((c=a.style)==null?void 0:c.display)==="none")return!0}var d,e;return vm.test((d=a.getAttribute("width"))!=null?d:"")&&vm.test((e=a.getAttribute("height"))!=null?e:"")}function Bm(a,b){if(a.tagName==="IMG")return a.naturalWidth&&a.naturalHeight?!0:!1;try{if(a.readyState)var c=a.readyState;else{var d,e;c=(d=a.contentWindow)==null?void 0:(e=d.document)==null?void 0:e.readyState}return c==="complete"}catch(f){return b===void 0?!1:b}}function Cm(a){a||(a=function(b,c,d){b.addEventListener(c,d)});return a}function Dm(a,b,c,d){c=c===void 0?new wm:c;if(a=zm(a)){d=Cm(d);for(var e=!1,f=function(v){e||(e=!0,b(v))},g,h=2,k=0;k<um.length;++k)if(um[k]===a.tagName){h=3;g=[a];break}g||(g=a.querySelectorAll(um.join(",")));var l=0,m=0,q=!0,t=a=!1;k={};for(var r=0;r<g.length;k={Xc:void 0},r++){var w=g[r];if(!Am(w))if(k.Xc=w.tagName==="IMG",Bm(w,c.g))a=!0,k.Xc&&(q=!0);else{l++;var u=function(v){return function(B){l--;!l&&q&&f(h);v.Xc&&(B=B&&B.type==="error",m--,B||(q=!0),!m&&t&&q&&f(h))}}(k);d(w,"load",u);k.Xc&&(m++,d(w,"error",u))}}m===0&&(q=!0);g=null;g=Ma.document.readyState==="complete";if(l===0&&!a&&g)h=5;else if(l||!a){d(Ma,"load",function(){!c.h||!m&&q?f(4):t=!0});return}f(h)}};function Em(){var a=this;this.g=this.j=!1;em();Xl.m=function(){return Fm(a)}}function Gm(a){a.j||(a.j=!0,Wk(D,"unload",function(){Hm("u")},171),Ob(K().g,"phell")==1&&Wk(D,"pagehide",function(){Hm("ph")},498))}function Im(){return Za(Nl.g,function(a){return!a.l||a.Wb||(Zk(gl(a))<=0?!1:Dl(a)&&!a.Qa)})}function Jm(){if(!Im()){Xl.done=!0;Nl.reset();var a=Xl;a.o=!1;a.g.Ze++;a.h&&(Xd().g.m(a.h),a.h=null);a=Ol();for(var b,c=0;c<a.length;++c)b=a[c],b.H.g&&fl(b);a=Ud(Ql);a.g!=null&&(a.g.dispose(),a.g=null)}}function Km(a,b){if(K().m){var c=K().m;a.h(b,c)}else b.Xd()}function Lm(a){var b=b===void 0?!0:b;try{if(Fm(a)){a.g=!0;var c=ek(),d=Pj();Nj=d;var e=K();e.A=947190542;fm=Ed(D);var f=Xl.g;f.Id=Pj()-d;f.rd=0;b&&Mm(a,d,a.Tc());var g=Nl.g;f.rd=g.length;D.__google_lidar_adblocks_count_=g.length;if(g.length<1)Hm("n");else{cm();var h=Ud(Ql);if(h.h==null){var k=Nm(e.g);h.h=k}Rl(h,function(l,m){Om(l);Hm(l,m)})?Xl.done||(Pm(),dm(),Wl()):c.l?(Om("w"),Hm("w")):(Om("i"),Hm("i"))}}}catch(l){throw Nl.reset(),Hm("x"),l;}}function Om(a){var b=Nl.g;K().m=a;Va(b,function(c){return c.m=!0})}function Pm(){Xd().setTimeout(Jj(176,function(){return Hm("t")}),36E5)}function Fm(a){if(a.g||Xl.done)return!1;Xd();a=D.document;return a&&a.body&&a.body.getBoundingClientRect&&typeof D.setInterval==="function"&&typeof D.clearInterval==="function"&&typeof D.setTimeout==="function"&&typeof D.clearTimeout==="function"?!0:(Hm("c"),!1)}function Hm(a,b){var c=Nl.g;Va(c,function(f){f.Ec=!0});var d=K();if(Ob(d.g,"omid")===1){if(a!=="w"&&a!=="i"&&!d.j)return}else if(d.o&&!d.l)return;Xl.j.cancel();if(!Xl.done&&(Zl(Xl,c,!0),!Xl.done)){d=Wa(c,function(f){return!f.l});var e={};a=(e.r=a,e);b&&Eb(a,b.rb());Xl.g.sd=D.__google_lidar_;e=bm(!1);Eb(a,e);d.length==0||rm(d,a,b);sm(c,a);Xl.done=!0}}function Qm(a,b,c,d,e){var f=K(),g=new yl(D,"",b,d,c,hd,[],[new tm(new mm)],e);d=f.h.g;ll(g,ej(d,function(){return a.l.apply(a,A(C.apply(0,arguments)))}),ej(d,function(){return a.h.apply(a,A(C.apply(0,arguments)))}));Pl([g]);Dm(b,ej(d,function(){if(g&&!g.Rb()){Km(a,g);if(g.H.P){var h=g.H,k=!0,l=!0;k=k===void 0?!1:k;l=l===void 0?!1:l;h.g=h.P;h.h="mue";if(!$c(h.P)){var m=cd(h.P),q=cd(h.P,!1);$c(q);$c(m)?(h.g=m,h.h="ie"):k&&(l||D!==D.top)&&(k=dd(),k.length==1&&(h.g=k[0],h.h="ce"))}}ek().l||Zj()||!Xd().g.g()||c==17?a.g?(h=Ud(Ql),h.g!=null&&hl(g,h.g),g.H.P&&Ud(Ul),Zl(Xl,Ol(),!1)):Lm(a):(Om("pv"),Hm("pv"))}}),xm(),function(h,k,l){Wk(h,k,l,177)});return g}Em.prototype.Tc=function(){return[]};function Mm(a,b,c){var d=[];Va(c,function(e){var f=b,g="";g=g===void 0?"":g;f=f===void 0?Pj():f;Gm(a);var h=Rm(e);h==0||Ml(e)!=null?e=null:(Ob(K().g,"gen204simple")&&Wd("https://pagead2.googlesyndication.com/pagead/gen_204?id=av-yt&bin=simple&type=registerAd&uuid="+g),e=Qm(a,e,h,f,g));e&&d.push(e)});return d}function Rm(a){if(!a)return 0;var b=Lc(a,"googleAvRs");if(b!=null)switch(Number(b)){case 6:return 5;case 9:return K().o=!0,8;case 15:return 14;case 16:return 15;case 17:return 16;case 18:return 17;default:return 0}if(!a.id)return 0;a=a.id;return a.lastIndexOf("DfaVisibilityIdentifier",0)==0?5:a.lastIndexOf("YtKevlarVisibilityIdentifier",0)==0?14:a.lastIndexOf("YtSparklesVisibilityIdentifier",0)==0?16:a.lastIndexOf("YtKabukiVisibilityIdentifier",0)==0?17:0}Em.prototype.h=function(a,b){if(a&&!Xl.done&&(a.m=!0,!a.l)){var c=new qm(new mm),d=kl(a),e={};c.g(a,(e.vs=d,e.r=b,e));a.l=c.h}Jm()};Em.prototype.l=function(a){if(a){if(!Xl.done&&a instanceof yl&&(Al(a),!Xl.done&&Qk(a.g)&&(Zk(gl(a))<=0?0:Dl(a)&&!a.Qa)&&a.G&&(Sd(a.G),a.Qa=!0),!Xl.done&&Qk(a.g)&&a.jb.length&&!a.Ne)){for(var b=z(a.jb),c=b.next();!c.done;c=b.next())(c=c.value)&&Sd(c);a.Ne=!0}Jm()}};function Sm(a,b){this.key=a;this.defaultValue=b===void 0?!1:b;this.valueType="boolean"}function Tm(a){this.key=a;this.defaultValue=0;this.valueType="number"};var Um=new Sm("45653435"),Vm=new Sm("100006"),Wm=new Tm("45362137"),Xm=new Sm("45377435"),Ym=new Sm("45618478"),Zm=new Sm("45377430"),$m=new Sm("45661569"),an=new Sm("45642405"),bn=new Sm("45372163"),cn=new Sm("45407241"),dn=new Sm("45382077"),en=new Sm("45658589"),fn=new Sm("45407239"),gn=new Sm("45407240",!0),hn=new Sm("45430682");function jn(a,b,c){nk.call(this,null,a,b,c);this.j=this.l=null}y(jn,Kl);n=jn.prototype;n.ka=function(){return"omid"};n.Ha=function(a){this.F=ek().h||new G(0,0,0,0);hj(K().flags,Vm)&&(this.l=a.h,this.l!==null&&(this.j=this.l*Zk(this.F)));Kl.prototype.Ha.call(this,a)};n.xd=function(a){return this.l!==null?this.l:Kl.prototype.xd.call(this,a)};n.wd=function(a){return this.j!==null?(a=Zk(ek().g),a===0?0:this.j/a):Kl.prototype.wd.call(this,a)};n.vd=function(a,b){return this.j!==null?(a=Zk(b),a===0?0:this.j/a):Kl.prototype.vd.call(this,a,b)};function kn(a,b){if(!b)throw Error("Value for "+a+" is undefined, null or blank.");if(typeof b!=="string"&&!(b instanceof String))throw Error("Value for "+a+" is not a string.");if(b.trim()==="")throw Error("Value for "+a+" is empty string.");}function ln(a){if(!a)throw Error("functionToExecute must not be truthy.");}function mn(a,b){if(b==null)throw Error(a+" must not be null or undefined.");if(typeof b!=="number"||isNaN(b))throw Error("Value for "+a+" is not a number");if(b<0)throw Error(a+" must be a positive number.");};function nn(){return/\d+\.\d+\.\d+(-.*)?/.test("1.5.2-google_20241009")}function on(){for(var a=["1","5","2"],b=["1","0","3"],c=0;c<3;c++){var d=parseInt(a[c],10),e=parseInt(b[c],10);if(d>e)break;else if(d<e)return!1}return!0};var pn={Tg:"app",ph:"web"};function qn(a,b,c,d){this.g=a;this.method=b;this.version=c;this.args=d}function rn(a){return!!a&&a.omid_message_guid!==void 0&&a.omid_message_method!==void 0&&a.omid_message_version!==void 0&&typeof a.omid_message_guid==="string"&&typeof a.omid_message_method==="string"&&typeof a.omid_message_version==="string"&&(a.omid_message_args===void 0||a.omid_message_args!==void 0)}function sn(a){return new qn(a.omid_message_guid,a.omid_message_method,a.omid_message_version,a.omid_message_args)}function tn(a){var b={};b=(b.omid_message_guid=a.g,b.omid_message_method=a.method,b.omid_message_version=a.version,b);a.args!==void 0&&(b.omid_message_args=a.args);return b};function un(a){this.g=a};function vn(a,b){try{return a.frames&&!!a.frames[b]}catch(c){return!1}}function wn(a){return["omid_v1_present","omid_v1_present_web","omid_v1_present_app"].some(function(b){return vn(a,b)})}function xn(a){for(var b=z(Object.values(pn)),c=b.next();!c.done;c=b.next()){c=c.value;var d={};d=(d.app="omid_v1_present_app",d.web="omid_v1_present_web",d)[c];if(vn(a,d))return c}return null};function yn(a,b){return a&&(a[b]||(a[b]={}))};function zn(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(a){var b=Math.random()*16|0;return a==="y"?(b&3|8).toString(16):b.toString(16)})};function An(){var a=C.apply(0,arguments);Bn(function(){throw new (Function.prototype.bind.apply(Error,[null,"Could not complete the test successfully - "].concat(A(a))));},function(){return console.error.apply(console,A(a))})}function Bn(a,b){typeof jasmine!=="undefined"&&jasmine?a():typeof console!=="undefined"&&console&&console.error&&b()};var Cn=function(){if(typeof omidGlobal!=="undefined"&&omidGlobal)return omidGlobal;if(typeof global!=="undefined"&&global)return global;if(typeof window!=="undefined"&&window)return window;if(typeof globalThis!=="undefined"&&globalThis)return globalThis;var a=Function("return this")();if(a)return a;throw Error("Could not determine global object context.");}();function Dn(a){this.g=a;this.handleExportedMessage=Dn.prototype.h.bind(this)}y(Dn,un);Dn.prototype.sendMessage=function(a,b){b=b===void 0?this.g:b;if(!b)throw Error("Message destination must be defined at construction time or when sending the message.");b.handleExportedMessage(tn(a),this)};Dn.prototype.h=function(a,b){if(rn(a)&&this.onMessage)this.onMessage(sn(a),b)};function En(a){return a!=null&&typeof a.top!=="undefined"&&a.top!=null}function Fn(a){if(a===Cn)return!1;try{if(typeof a.location.hostname==="undefined")return!0}catch(b){return!0}return!1}function Gn(){var a;typeof a==="undefined"&&typeof window!=="undefined"&&window&&(a=window);return En(a)?a:Cn};function Hn(a,b){this.g=b=b===void 0?Cn:b;var c=this;a.addEventListener("message",function(d){if(typeof d.data==="object"){var e=d.data;if(rn(e)&&d.source&&c.onMessage)c.onMessage(sn(e),d.source)}})}y(Hn,un);Hn.prototype.sendMessage=function(a,b){b=b===void 0?this.g:b;if(!b)throw Error("Message destination must be defined at construction time or when sending the message.");b.postMessage(tn(a),"*")};var In=["omid","v1_VerificationServiceCommunication"],Jn=["omidVerificationProperties","serviceWindow"];function Kn(a,b){return b.reduce(function(c,d){return c&&c[d]},a)};function Ln(a){if(!a){a=Gn();var b=b===void 0?wn:b;var c=[],d=Kn(a,Jn);d&&c.push(d);c.push(En(a)?a.top:Cn);a:{c=z(c);for(var e=c.next();!e.done;e=c.next()){b:{d=a;e=e.value;var f=b;if(!Fn(e))try{var g=Kn(e,In);if(g){var h=new Dn(g);break b}}catch(k){}h=f(e)?new Hn(d,e):null}if(d=h){a=d;break a}}a=null}}if(this.g=a)this.g.onMessage=this.Vf.bind(this);else if(b=(b=Cn.omid3p)&&typeof b.registerSessionObserver==="function"&&typeof b.addEventListener==="function"?b:null)this.j=b;this.o=this.A=0;this.l={};this.m=[];this.h=(b=Cn.omidVerificationProperties)?b.injectionId:void 0}n=Ln.prototype;n.O=function(){var a=Gn();var b=(b=Cn.omidVerificationProperties)&&b.injectionSource?b.injectionSource:void 0;return(b||xn(a)||xn(En(a)?a.top:Cn))!=="web"||this.h?!(!this.g&&!this.j):!1};function Mn(a,b,c){ln(b);a.j?a.j.registerSessionObserver(b,c,a.h):a.Vb("addSessionListener",b,c,a.h)}n.addEventListener=function(a,b){kn("eventType",a);ln(b);this.j?this.j.addEventListener(a,b,this.h):this.Vb("addEventListener",b,a,this.h)};function Nn(a,b,c,d){kn("url",b);Cn.document&&Cn.document.createElement?On(a,b,c,d):a.Vb("sendUrl",function(e){e&&c?c():!e&&d&&d()},b)}function On(a,b,c,d){function e(g){var h=a.m.indexOf(f);h>=0&&a.m.splice(h,1);g&&g()}var f=Cn.document.createElement("img");a.m.push(f);f.addEventListener("load",e.bind(a,c));f.addEventListener("error",e.bind(a,d));f.src=b}n.setTimeout=function(a,b){ln(a);mn("timeInMillis",b);if(Pn())return Cn.setTimeout(a,b);var c=this.A++;this.Vb("setTimeout",a,c,b);return c};n.clearTimeout=function(a){mn("timeoutId",a);Pn()?Cn.clearTimeout(a):this.Ye("clearTimeout",a)};n.setInterval=function(a,b){ln(a);mn("timeInMillis",b);if(Qn())return Cn.setInterval(a,b);var c=this.o++;this.Vb("setInterval",a,c,b);return c};n.clearInterval=function(a){mn("intervalId",a);Qn()?Cn.clearInterval(a):this.Ye("clearInterval",a)};function Pn(){return typeof Cn.setTimeout==="function"&&typeof Cn.clearTimeout==="function"}function Qn(){return typeof Cn.setInterval==="function"&&typeof Cn.clearInterval==="function"}n.Vf=function(a){var b=a.method,c=a.g;a=a.args;if(b==="response"&&this.l[c]){var d=nn()&&on()?a?a:[]:a&&typeof a==="string"?JSON.parse(a):[];this.l[c].apply(this,d)}b==="error"&&window.console&&An(a)};n.Ye=function(a){this.Vb.apply(this,[a,null].concat(A(C.apply(1,arguments))))};n.Vb=function(a,b){var c=C.apply(2,arguments);if(this.g){var d=zn();b&&(this.l[d]=b);var e="VerificationService."+a;c=nn()&&on()?c:JSON.stringify(c);this.g.sendMessage(new qn(d,e,"1.5.2-google_20241009",c))}};var Rn=void 0;if(Rn=Rn===void 0?typeof omidExports==="undefined"?null:omidExports:Rn){var Sn=["OmidVerificationClient"];Sn.slice(0,Sn.length-1).reduce(yn,Rn)[Sn[Sn.length-1]]=Ln};Ln.wb=void 0;Ln.g=function(){return Ln.wb?Ln.wb:Ln.wb=new Ln};var Tn={},Un=(Tn.notFound=!0,Tn.hidden=!0,Tn.backgrounded=!0,Tn.noOutputDevice=!0,Tn);function Vn(){gk.call(this,D,2,"omid");this.Va=Ln.g();this.bc=Ln.g().O();this.D=[];this.G=this.ta=this.da=this.A=this.V=this.R=this.Na=this.j=this.Fc=this.Qa=void 0;this.cc="normal";this.dc=this.Hb=!1;this.Oa=void 0;this.Ta=!1;this.Fb=this.Sa=void 0}y(Vn,gk);function Wn(a){a.Va.addEventListener("geometryChange",function(b){Xn(397,function(){return Yn(a,b)})})}function Zn(a){function b(c){Xn(399,function(){return $n(a,c)})}Va("loaded start firstQuartile midpoint thirdQuartile complete pause resume bufferStart bufferFinish skipped volumeChange playerStateChange adUserInteraction impression".split(" "),function(c){a.Va.addEventListener(c,b)})}function ao(a){Mn(a.Va,function(b){Xn(398,function(){return bo(a,b)})},"doubleclickbygoogle.com")}function Yn(a,b){co(b,function(c,d,e,f){a.G!==c&&(c=Error("Received geometry event from a different session. Expected session id: "+(a.G+", Received session id: ")+(c+". Creative type: ")+(a.R+", Omid partner: ")+(a.A+", App info: ")+JSON.stringify(a.j)),Kj(1071,c));e=f.viewport;c=ek().j;d=ek().g;e!=null&&e.width!=null&&e.height!=null&&(c=(new Oc(e.width,e.height)).floor(),d=(new G(0,e.width,e.height,0)).floor());var g=f.adView,h=g.geometry,k=g.onScreenGeometry;f=new G(0,0,0,0);e=new G(0,0,0,0);var l=null;eo(k)&&eo(h)&&(f=(new G(k.y,k.x+k.width,k.y+k.height,k.x)).floor(),e=(new G(h.y,h.x+h.width,h.y+h.height,h.x)).floor(),hj(K().flags,Vm)?l=g.percentageInView/100:h.width>0&&h.height>0&&(l=k.width*k.height/(h.width*h.height)));a.D=g.reasons||[];(g=!Za(a.D,function(m){return Un[m]}))&&l!==null&&l>0&&(a.dc=!0,a.Hb&&(K().j=!0));h=a.D.includes("noOutputDevice");a.Ta=a.Ta||h;fo(a,c,d,e,f,l,g,a.h.volume,h)})}function $n(a,b){co(b,function(c,d,e,f){if(e==="impression")a.Hb=!0,a.dc&&(K().j=!0);else{d=a.h.volume;c=!1;if(Ua(["start","volumeChange"],e)>=0){d=typeof(f==null?void 0:f.deviceVolume)==="number"?f==null?void 0:f.deviceVolume:a.V&&a.V=="web"?1:null;var g=typeof(f==null?void 0:f.mediaPlayerVolume)==="number"?f==null?void 0:f.mediaPlayerVolume:typeof f.videoPlayerVolume==="number"?f==null?void 0:f.videoPlayerVolume:null;d=typeof d==="number"&&typeof g==="number"?d*g:null;d!=null&&(a.h.volume=d,c=!0)}e=="playerStateChange"&&f.state!=null&&(a.cc=f.state,c=!0);g=ek();c&&fo(a,g.j,g.g,g.h,a.h.g,a.h.h,a.h.j,d,null)}if(e=="impression"||e=="loaded")f.creativeType?a.R=f.creativeType:f.mediaType&&(a.R=f.mediaType=="video"?"video":a.ta=="native"?"nativeDisplay":"htmlDisplay")})}function bo(a,b){co(b,function(c,d,e,f){e=="sessionStart"&&f.context&&(a.Fc=f.verificationParameters,a.j=f.context.app,a.ta=f.context.adSessionType,a.Na=f.context.accessMode,a.V=f.context.environment,a.Oa=f.context.deviceCategory,f.context.omidNativeInfo&&f.context.omidNativeInfo.partnerName?a.A=f.context.omidNativeInfo.partnerName:f.context.omidJsInfo&&f.context.omidJsInfo.partnerName&&(a.A=f.context.omidJsInfo.partnerName),f.context.omidNativeInfo&&f.context.omidNativeInfo.partnerVersion?a.da=f.context.omidNativeInfo.partnerVersion:f.context.omidJsInfo&&f.context.omidJsInfo.partnerVersion&&(a.da=f.context.omidJsInfo.partnerVersion));["sessionStart","sessionError"].includes(e);e=="sessionFinish"&&typeof a.Qa==="function"&&a.Qa();a.G===void 0?a.G=c:a.G!==c&&Kj(1072,Error("Received session event from a different session. Expected session id: "+(a.G+", Received session id: ")+(c+".  partner: ")+a.A+(" partner version: "+a.da)));e=="sessionStart"&&f.lastActivity&&f.lastActivity.timestamp&&typeof f.lastActivity.timestamp==="number"&&(a.Sa=f.lastActivity.timestamp,a.Fb=Math.round((d-a.Sa)/1E3))})}function eo(a){return a!=null&&$a(function(b){return a.hasOwnProperty(b)})}function co(a,b){a!=null&&a.adSessionId!=null&&a.timestamp!=null&&a.type!=null?b(a.adSessionId,a.timestamp,a.type,a.data||{}):(a=Error("OMSDK event missing some data: "+JSON.stringify(a)),Kj(543,a))}function Xn(a,b){try{b.apply()}catch(c){Kj(a,c)}}function fo(a,b,c,d,e,f,g,h,k){var l=Ud(ml);if(l.j!==g){l.j=g;l=z(l.h);for(var m=l.next();!m.done;m=l.next())m=m.value,m(null)}k!==null&&Ud(ml);a.cc!="minimized"&&g||(e=new G(0,0,0,0));k=ek();e=e||new G(0,0,0,0);l=kk(a);k.j=b;k.g=c;k.h=d;l.g=e;l.h=f;l.j=g;l.volume=h;a.Ha(l)}n=Vn.prototype;n.hd=function(){};n.jd=function(){};n.ie=function(){};n.je=function(){};n.Dc=function(){var a=K();return a.Aa===6||a.Aa===5?this.Pa():Ob(a.g,"omid")==1&&this.Pa()};n.Pa=function(){return this.bc};n.Ed=function(){var a={},b=K();this.Pa()&&Ob(b.g,"sloi")&&(Oa(this.D)&&this.D.length>0&&(a.omidr=Xa(fb(this.D,0,5),function(c){return String(c).slice(0,2)}).join(",")),this.j&&(this.j.libraryVersion&&(a.omidv=this.j.libraryVersion),this.j.appId&&(a.omida=this.j.appId)),this.A&&(a.omidp=this.A),this.ta&&(a.omids=this.ta.charAt(0)),this.da&&(a.omidpv=this.da.substring(0,30)),this.Na&&(a.omidam=this.Na.charAt(0)),this.R&&(a.omidct=this.R.charAt(0)),this.V&&(a.omidia=this.V=="app"?1:0),this.Oa&&(a.omiddc=this.Oa),this.Ta&&(a.nopd=1),this.Sa&&(a.omiddit=this.Fb));return a};n.Zb=function(){var a=this;if(this.ea||!this.bc)return!this.Nb();this.ea=!0;Xn(391,function(){return ao(a)});Xn(390,function(){return Wn(a)});Xn(392,function(){return Zn(a)});return!0};function go(a){pk.call(this,Ud(Vn));Ud(Vn).Qa=a}y(go,pk);n=go.prototype;n.ka=function(){return"omid"};n.we=function(a,b,c){return new jn(this.g,b,c)};n.kd=function(){return this.g.Pa()};n.ld=function(){return this.g.Dc()};n.init=function(){this.g.Zb();return!0};n.dispose=function(){this.g.dispose();pk.prototype.dispose.call(this)};function ho(){gk.call(this,D,2,"mraid");this.R=0;this.D=this.G=!1;this.A=null;this.j=Uj(this.l);this.h.g=new G(0,0,0,0);this.V=!1}y(ho,gk);n=ho.prototype;n.Pa=function(){return this.j.ya!=null};n.Ed=function(){var a={};this.R&&(a.mraid=this.R);this.G&&(a.mlc=1);a.mtop=this.j.La;this.A&&(a.mse=this.A);this.V&&(a.msc=1);a.mcp=this.j.Ea;return a};n.fb=function(a){var b=C.apply(1,arguments);try{return this.j.ya[a].apply(this.j.ya,b)}catch(c){Kj(538,c,.01,function(d){d.method=a})}};function io(a,b,c){a.fb("addEventListener",b,c)}n.Zb=function(){var a=this;if(this.ea)return!this.Nb();this.ea=!0;if(this.j.Ea===2)return this.A="ng",this.fail("w"),!1;if(this.j.Ea===1)return this.A="mm",this.fail("w"),!1;ek().D=!0;this.l.document.readyState&&this.l.document.readyState=="complete"?jo(this):Wk(this.l,"load",function(){Xd().setTimeout(Jj(292,function(){return jo(a)}),100)},292);return!0};function jo(a){K().l=!!a.fb("isViewable");io(a,"viewableChange",ko);a.fb("getState")==="loading"?io(a,"ready",lo):mo(a)}function mo(a){typeof a.j.ya.AFMA_LIDAR==="string"?(a.G=!0,no(a)):(a.j.Ea=3,a.A="nc",a.fail("w"))}function no(a){a.D=!1;var b=Ob(K().g,"rmmt")==1,c=!!a.fb("isViewable");(b?!c:1)&&Xd().setTimeout(Jj(524,function(){a.D||(oo(a),Kj(540,Error()),a.A="mt",a.fail("w"))}),500);po(a);io(a,a.j.ya.AFMA_LIDAR,qo)}function po(a){var b=Ob(K().g,"sneio")==1,c=a.j.ya.AFMA_LIDAR_EXP_1!==void 0,d=a.j.ya.AFMA_LIDAR_EXP_2!==void 0;(b=b&&d)&&(a.j.ya.AFMA_LIDAR_EXP_2=!0);c&&(a.j.ya.AFMA_LIDAR_EXP_1=!b)}function oo(a){a.fb("removeEventListener",a.j.ya.AFMA_LIDAR,qo);a.G=!1}n.hd=function(){var a=ek(),b=ro(this,"getMaxSize");a.g=new G(0,b.width,b.height,0)};n.jd=function(){ek().j=ro(this,"getScreenSize")};function ro(a,b){if(a.fb("getState")==="loading")return new Oc(-1,-1);b=a.fb(b);if(!b)return new Oc(-1,-1);a=parseInt(b.width,10);b=parseInt(b.height,10);return isNaN(a)||isNaN(b)?new Oc(-1,-1):new Oc(a,b)}n.dispose=function(){oo(this);gk.prototype.dispose.call(this)};function lo(){try{var a=Ud(ho);a.fb("removeEventListener","ready",lo);mo(a)}catch(b){Kj(541,b)}}function qo(a,b){try{var c=Ud(ho);c.D=!0;var d=a?new G(a.y,a.x+a.width,a.y+a.height,a.x):new G(0,0,0,0);var e=kk(c);e.g=d;e.volume=b;c.Ha(e)}catch(f){Kj(542,f)}}function ko(a){var b=K(),c=Ud(ho);a&&!b.l&&(b.l=!0,c.V=!0,c.A&&c.fail("w",!0))};function so(){Em.call(this)}y(so,Em);n=so.prototype;n.mg=function(){var a=this;if(D.__google_lidar_){if(D.__google_lidar_+=1,D.__google_lidar_adblocks_count_){var b=D.__google_lidar_radf_;b&&typeof b==="function"&&b()}}else{D.__google_lidar_=1;Gm(this);b=K();b.Aa=2;b=b.h.g;D.__google_lidar_radf_=ej(b,function(){return a.yg.apply(a,A(C.apply(0,arguments)))});var c=D.document.readyState;c&&c==="complete"?this.Od():(qd(D,"load",ej(b,function(){Xd().setTimeout(Jj(172,function(){return a.Wf.apply(a,A(C.apply(0,arguments)))}),100)})),Wk(D,"DOMContentLoaded",function(){return a.Od.apply(a,A(C.apply(0,arguments)))},173))}};n.Wf=function(){var a=this;Va(Nl.g,function(b){return Km(a,b)});this.Od()};n.Od=function(){var a=Pj(),b=this.Tc();if(b.length)if(this.g)try{Mm(this,a,b).forEach(function(c){hj(K().flags,Zm)&&(c.Hb=!0)})}catch(c){}else Lm(this)};function Nm(a){return Ob(a,"omid")==1?[new go(function(){return Hm("u")})]:[new Sl([Ud(ho)])]}n.yg=function(){var a=this.Tc();if(a.length)try{var b=Pj(),c=Mm(this,b,a);Va(c,function(d){hj(K().flags,Zm)&&(d.Hb=!0);d.ye=!0})}catch(d){}};n.Tc=function(){return kb(Xa(Tj,function(a){a=document.querySelectorAll("."+a);return eb(a)}))};function to(){this.ff=0}to.prototype.Xb=function(a,b){var c=this;return function(){var d=C.apply(0,arguments);c.ff=a;return b.apply(null,A(d))}};function uo(){var a={};this.Ca=(a[3]=[],a[2]=[],a[1]=[],a);this.Ld=!1}function vo(a,b,c){var d=wo(a,c);a.Ca[c].push(b);d&&a.Ca[c].length===1&&a.flush()}function wo(a,b){return Object.keys(a.Ca).map(function(c){return Number(c)}).filter(function(c){return!isNaN(c)&&c>b}).every(function(c){return a.Ca[c].length===0})}uo.prototype.flush=function(){if(!this.Ld){this.Ld=!0;try{for(;Object.values(this.Ca).some(function(a){return a.length>0});)xo(this,3),xo(this,2),xo(this,1)}catch(a){throw Object.values(this.Ca).forEach(function(b){return void b.splice(0,b.length)}),a;}finally{this.Ld=!1}}};function xo(a,b){for(;wo(a,b)&&a.Ca[b].length>0;)a.Ca[b][0](),a.Ca[b].shift()}ea.Object.defineProperties(uo.prototype,{Cg:{configurable:!0,enumerable:!0,get:function(){return Object.values(this.Ca).some(function(a){return a.length>0})}}});function yo(){this.g=new Map}function zo(a,b){var c=a.g.get(b);if(c)return c;var d;c=(d=b.description)!=null?d:Math.floor(Math.random()***********).toString(36)+Math.abs(Math.floor(Math.random()***********)^Date.now()).toString(36);a.g.set(b,c);return c};/* 
 
 
 Copyright (c) 2015-2018 Google, Inc., Netflix, Inc., Microsoft Corp. and contributors 
 Licensed under the Apache License, Version 2.0 (the "License"); 
 you may not use this file except in compliance with the License. 
 You may obtain a copy of the License at 
     http://www.apache.org/licenses/LICENSE-2.0 
 Unless required by applicable law or agreed to in writing, software 
 distributed under the License is distributed on an "AS IS" BASIS, 
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. 
 See the License for the specific language governing permissions and 
 limitations under the License. 
*/ 
function Ao(a){var b=Error.call(this,a?a.length+" errors occurred during unsubscription:\n"+a.map(function(c,d){return d+1+") "+c.toString()}).join("\n  "):"");this.message=b.message;"stack"in b&&(this.stack=b.stack);this.errors=a;Object.setPrototypeOf(this,this.constructor.prototype);this.name="UnsubscriptionError"}y(Ao,Error);function Bo(a,b){a&&(b=a.indexOf(b),0<=b&&a.splice(b,1))};function L(a){return typeof a==="function"};function Co(a){this.m=a;this.closed=!1;this.j=this.h=null}n=Co.prototype;n.unsubscribe=function(){if(!this.closed){this.closed=!0;var a=this.h;if(Array.isArray(a))for(var b=z(a),c=b.next();!c.done;c=b.next())c.value.remove(this);else a==null||a.remove(this);b=this.m;if(L(b))try{b()}catch(f){var d=f instanceof Ao?f.errors:[f]}var e=this.j;if(e)for(this.j=null,b=z(e),c=b.next();!c.done;c=b.next()){c=c.value;try{L(c)?c():c.unsubscribe()}catch(f){c=void 0,d=(c=d)!=null?c:[],f instanceof Ao?d=[].concat(A(d),A(f.errors)):d.push(f)}}if(d)throw new Ao(d);}};n.add=function(a){if(a&&a!==this)if(this.closed)L(a)?a():a.unsubscribe();else{if(a instanceof Co){if(a.closed||a.uf(this))return;a.tf(this)}var b;(this.j=(b=this.j)!=null?b:[]).push(a)}};n.uf=function(a){var b=this.h;return b===a||Array.isArray(b)&&b.includes(a)};n.tf=function(a){var b=this.h;this.h=Array.isArray(b)?(b.push(a),b):b?[b,a]:a};n.vf=function(a){var b=this.h;b===a?this.h=null:Array.isArray(b)&&Bo(b,a)};n.remove=function(a){var b=this.j;b&&Bo(b,a);a instanceof Co&&a.vf(this)};var Do=new Co;Do.closed=!0;Co.g=Do;function Eo(a){return a instanceof Co||a&&"closed"in a&&L(a.remove)&&L(a.add)&&L(a.unsubscribe)};function Fo(){setTimeout.apply(null,A(C.apply(0,arguments)))};function Go(){};function Ho(a){Fo(function(){throw a;})};function Io(a){Co.call(this);this.g=!1;this.destination=a instanceof Io?a:new Jo(!a||L(a)?{next:a!=null?a:void 0}:a);Eo(a)&&a.add(this)}y(Io,Co);Io.g=Co.g;Io.create=function(a,b,c){return new Ko(a,b,c)};n=Io.prototype;n.next=function(a){this.g||this.pd(a)};n.error=function(a){this.g||(this.g=!0,this.me(a))};n.complete=function(){this.g||(this.g=!0,this.Gc())};n.unsubscribe=function(){this.closed||(this.g=!0,Co.prototype.unsubscribe.call(this))};n.pd=function(a){this.destination.next(a)};n.me=function(a){this.destination.error(a);this.unsubscribe()};n.Gc=function(){this.destination.complete();this.unsubscribe()};function Jo(a){this.g=a}Jo.prototype.next=function(a){var b=this.g;if(b.next)try{b.next(a)}catch(c){Ho(c)}};Jo.prototype.error=function(a){var b=this.g;if(b.error)try{b.error(a)}catch(c){Ho(c)}else Ho(a)};Jo.prototype.complete=function(){var a=this.g;if(a.complete)try{a.complete()}catch(b){Ho(b)}};function Ko(a,b,c){Io.call(this);var d;L(a)||!a?d={next:a!=null?a:void 0,error:b!=null?b:void 0,complete:c!=null?c:void 0}:d=a;this.destination=new Jo(d)}y(Ko,Io);Ko.g=Io.g;Ko.create=Io.create;var Lo=typeof Symbol==="function"&&Symbol.observable||"@@observable";function Mo(a){return a};function M(){return No(C.apply(0,arguments))}function No(a){return a.length===0?Mo:a.length===1?a[0]:function(b){return a.reduce(function(c,d){return d(c)},b)}};function N(a){a&&(this.Da=a)}n=N.prototype;n.zb=function(a){var b=new N;b.source=this;b.A=a;return b};n.subscribe=function(a,b,c){a=a&&a instanceof Io||a&&L(a.next)&&L(a.error)&&L(a.complete)&&Eo(a)?a:new Ko(a,b,c);b=this.A;c=this.source;a.add(b?b.call(a,c):c?this.Da(a):this.qd(a));return a};n.qd=function(a){try{return this.Da(a)}catch(b){a.error(b)}};n.forEach=function(a,b){var c=this;b=Oo(b);return new b(function(d,e){var f=c.subscribe(function(g){try{a(g)}catch(h){e(h),f==null||f.unsubscribe()}},e,d)})};n.Da=function(a){var b;return(b=this.source)==null?void 0:b.subscribe(a)};N.prototype[Lo]=function(){return this};N.prototype.i=function(){var a=C.apply(0,arguments);return a.length?No(a)(this):this};N.create=function(a){return new N(a)};function Oo(a){var b;return(b=a!=null?a:void 0)!=null?b:Promise};function Po(){var a=Error.call(this,"object unsubscribed");this.message=a.message;"stack"in a&&(this.stack=a.stack);Object.setPrototypeOf(this,this.constructor.prototype);this.name="ObjectUnsubscribedError"}y(Po,Error);function O(){this.h=[];this.j=this.g=this.closed=!1;this.o=null}y(O,N);n=O.prototype;n.zb=function(a){var b=new Qo(this,this);b.A=a;return b};n.lb=function(){if(this.closed)throw new Po;};n.next=function(a){this.lb();if(!this.g){var b=this.h.slice();b=z(b);for(var c=b.next();!c.done;c=b.next())c.value.next(a)}};n.error=function(a){this.lb();if(!this.g){this.j=this.g=!0;this.o=a;for(var b=this.h;b.length;)b.shift().error(a)}};n.complete=function(){this.lb();if(!this.g){this.g=!0;for(var a=this.h;a.length;)a.shift().complete()}};n.unsubscribe=function(){this.g=this.closed=!0;this.h=null};n.qd=function(a){this.lb();return N.prototype.qd.call(this,a)};n.Da=function(a){this.lb();this.le(a);return this.pe(a)};n.pe=function(a){var b=this,c=this.g,d=this.h;return this.j||c?Co.g:(d.push(a),new Co(function(){return Bo(b.h,a)}))};n.le=function(a){var b=this.o,c=this.g;this.j?a.error(b):c&&a.complete()};O.create=function(a,b){return new Qo(a,b)};function Qo(a,b){O.call(this);this.destination=a;this.source=b}y(Qo,O);Qo.create=O.create;Qo.prototype.next=function(a){var b,c;(b=this.destination)==null||(c=b.next)==null||c.call(b,a)};Qo.prototype.error=function(a){var b,c;(b=this.destination)==null||(c=b.error)==null||c.call(b,a)};Qo.prototype.complete=function(){var a,b;(a=this.destination)==null||(b=a.complete)==null||b.call(a)};Qo.prototype.Da=function(a){var b,c;return(c=(b=this.source)==null?void 0:b.subscribe(a))!=null?c:Co.g};function Ro(a){O.call(this);this.l=a}y(Ro,O);Ro.create=O.create;Ro.prototype.Da=function(a){var b=O.prototype.Da.call(this,a);!b.closed&&a.next(this.l);return b};Ro.prototype.next=function(a){O.prototype.next.call(this,this.l=a)};ea.Object.defineProperties(Ro.prototype,{value:{configurable:!0,enumerable:!0,get:function(){var a=this.o,b=this.l;if(this.j)throw a;this.lb();return b}}});var So=new N(function(a){return a.complete()});function To(a,b){return new N(function(c){var d=0;return b.I(function(){d===a.length?c.complete():(c.next(a[d++]),c.closed||this.I())})})};function Uo(a,b){if(!a)throw Error("Iterable cannot be null");return new N(function(c){var d=new Co;d.add(b.I(function(){var e=a[Symbol.asyncIterator]();d.add(b.I(function(){var f=this;e.next().then(function(g){g.done?c.complete():(c.next(g.value),f.I())})}))}));return d})};var Vo=typeof Symbol==="function"&&Symbol.iterator?Symbol.iterator:"@@iterator";function Wo(a,b,c){b=b.I(function(){try{c.call(this)}catch(d){a.error(d)}},0);a.add(b)};function Xo(a,b){return new N(function(c){var d;c.add(b.I(function(){d=a[Vo]();Wo(c,b,function(){var e=d.next(),f=e.value;e.done?c.complete():(c.next(f),this.I())})}));return function(){var e;return L((e=d)==null?void 0:e.return)&&d.return()}})};function Yo(a,b){return new N(function(c){var d=new Co;d.add(b.I(function(){var e=a[Lo]();d.add(e.subscribe({next:function(f){d.add(b.I(function(){return c.next(f)}))},error:function(f){d.add(b.I(function(){return c.error(f)}))},complete:function(){d.add(b.I(function(){return c.complete()}))}}))}));return d})};function Zo(a,b){return new N(function(c){return b.I(function(){return a.then(function(d){c.add(b.I(function(){c.next(d);c.add(b.I(function(){return c.complete()}))}))},function(d){c.add(b.I(function(){return c.error(d)}))})})})};function $o(a){return a&&typeof a.length==="number"&&typeof a!=="function"};function ap(a){return new TypeError("You provided "+(a!==null&&typeof a==="object"?"an invalid object":"'"+a+"'")+" where a stream was expected. You can provide an Observable, Promise, Array, AsyncIterable, or Iterable.")};function bp(a,b){if(a!=null){if(L(a[Lo]))return Yo(a,b);if($o(a))return To(a,b);if(L(a==null?void 0:a.then))return Zo(a,b);if(Symbol.asyncIterator&&L(a==null?void 0:a[Symbol.asyncIterator]))return Uo(a,b);if(L(a==null?void 0:a[Vo]))return Xo(a,b)}throw ap(a);};function cp(a,b){return b?bp(a,b):dp(a)}function dp(a){if(a instanceof N)return a;if(a!=null){if(L(a[Lo]))return ep(a);if($o(a))return fp(a);if(L(a==null?void 0:a.then))return gp(a);if(Symbol.asyncIterator&&L(a==null?void 0:a[Symbol.asyncIterator]))return hp(a);if(L(a==null?void 0:a[Vo]))return ip(a)}throw ap(a);}function ep(a){return new N(function(b){var c=a[Lo]();if(L(c.subscribe))return c.subscribe(b);throw new TypeError("Provided object does not correctly implement Symbol.observable");})}function fp(a){return new N(function(b){for(var c=0;c<a.length&&!b.closed;c++)b.next(a[c]);b.complete()})}function gp(a){return new N(function(b){a.then(function(c){b.closed||(b.next(c),b.complete())},function(c){return b.error(c)}).then(null,Ho)})}function ip(a){return new N(function(b){for(var c=a[Vo]();!b.closed;){var d=c.next(),e=d.value;d.done?b.complete():b.next(e)}return function(){return L(c==null?void 0:c.return)&&c.return()}})}function hp(a){return new N(function(b){jp(a,b).catch(function(c){return b.error(c)})})}function jp(a,b){var c,d,e,f,g,h;return Ga(function(k){switch(k.g){case 1:va(k,2,3);var l=a[Symbol.asyncIterator];f=l!==void 0?l.call(a):new Ha(z(a));case 5:return ua(k,f.next(),8);case 8:d=k.h;if(d.done){k.Ja(3);break}g=d.value;b.next(g);k.Ja(5);break;case 3:xa(k);k.l=0;k.m=9;if(!d||d.done||!(e=f.return)){k.Ja(9);break}return ua(k,e.call(f),9);case 9:xa(k,0,0,1);if(c)throw c.error;ya(k,10,1);break;case 10:ya(k,4);break;case 2:h=wa(k);c={error:h};k.Ja(3);break;case 4:b.complete(),k.g=0}})};function kp(a,b){return b?To(a,b):fp(a)};function lp(a){return L(a[a.length-1])?a.pop():void 0}function mp(a){var b=a[a.length-1];return b&&L(b.I)?a.pop():void 0};function Q(){var a=C.apply(0,arguments),b=mp(a);return b?To(a,b):kp(a)};function np(a){var b=L(a)?a:function(){return a};return new N(function(c){return c.error(b())})};var op={now:function(){return(op.Nf||Date).now()},Nf:void 0};function pp(a,b,c){a=a===void 0?Infinity:a;b=b===void 0?Infinity:b;c=c===void 0?op:c;O.call(this);this.D=a;this.R=b;this.G=c;this.l=[];this.m=b===Infinity;this.D=Math.max(1,a);this.R=Math.max(1,b)}y(pp,O);pp.create=O.create;pp.prototype.next=function(a){var b=this.l,c=this.m,d=this.G,e=this.R;this.g||(b.push(a),!c&&b.push(d.now()+e));qp(this);O.prototype.next.call(this,a)};pp.prototype.Da=function(a){this.lb();qp(this);for(var b=this.pe(a),c=this.m,d=this.l.slice(),e=0;e<d.length&&!a.closed;e+=c?1:2)a.next(d[e]);this.le(a);return b};function qp(a){var b=a.D,c=a.G,d=a.l;a=a.m;var e=(a?1:2)*b;b<Infinity&&e<d.length&&d.splice(0,d.length-e);if(!a){b=c.now();c=0;for(a=1;a<d.length&&d[a]<=b;a+=2)c=a;c&&d.splice(0,c+1)}};function rp(a,b){b=b===void 0?sp:b;this.g=a;this.now=b}rp.prototype.I=function(a,b,c){b=b===void 0?0:b;return(new this.g(this,a)).I(c,b)};var sp=op.now;function tp(){var a=Error.call(this,"no elements in sequence");this.message=a.message;"stack"in a&&(this.stack=a.stack);Object.setPrototypeOf(this,this.constructor.prototype);this.name="EmptyError"}y(tp,Error);function up(a){return new Promise(function(b,c){var d=new Ko({next:function(e){b(e);d.unsubscribe()},error:c,complete:function(){c(new tp)}});a.subscribe(d)})};function vp(a,b,c,d,e){Io.call(this,a);this.l=e;b&&(this.pd=function(f){try{b(f)}catch(g){this.destination.error(g)}});c&&(this.me=function(f){try{c(f)}catch(g){this.destination.error(g)}this.unsubscribe()});d&&(this.Gc=function(){try{d()}catch(f){this.destination.error(f)}this.unsubscribe()})}y(vp,Io);vp.g=Io.g;vp.create=Io.create;vp.prototype.unsubscribe=function(){var a;this.closed||(a=this.l)!=null&&a.call(this);Io.prototype.unsubscribe.call(this)};function wp(a){return function(b){if(L(b==null?void 0:b.zb))return b.zb(function(c){try{return a(c,this)}catch(d){this.error(d)}});throw new TypeError("Unable to lift unknown Observable type");}};function xp(){return wp(function(a,b){var c=null;a.Hc++;var d=new vp(b,void 0,void 0,void 0,function(){if(!a||a.Hc<=0||0<--a.Hc)c=null;else{var e=a.Ib,f=c;c=null;!e||f&&e!==f||e.unsubscribe();b.unsubscribe()}});a.subscribe(d);d.closed||(c=a.connect())})};function yp(a,b){this.source=a;this.af=b;this.g=null;this.Hc=0;this.Ib=null}y(yp,N);yp.create=N.create;yp.prototype.Da=function(a){return zp(this).subscribe(a)};function zp(a){var b=a.g;if(!b||b.g)a.g=a.af();return a.g}yp.prototype.h=function(){this.Hc=0;var a=this.Ib;this.g=this.Ib=null;a==null||a.unsubscribe()};yp.prototype.connect=function(){var a=this,b=this.Ib;if(!b){b=this.Ib=new Co;var c=zp(this);b.add(this.source.subscribe(new vp(c,void 0,function(d){a.h();c.error(d)},function(){a.h();c.complete()},function(){return a.h()})));b.closed&&(this.Ib=null,b=Co.g)}return b};function Ap(){var a=Bp;var b=b===void 0?0:b;return wp(function(c,d){d.add(a.I(function(){return c.subscribe(d)},b))})};function R(a){return wp(function(b,c){var d=0;b.subscribe(new vp(c,function(e){c.next(a.call(void 0,e,d++))}))})};var Cp=Array.isArray;function Dp(a){return R(function(b){return Cp(b)?a.apply(null,A(b)):a(b)})};var Ep=Array.isArray,Fp=Object,Gp=Fp.getPrototypeOf,Hp=Fp.prototype,Ip=Fp.keys;function Jp(a){if(a.length===1){var b=a[0];if(Ep(b))return{args:b,keys:null};if(b&&typeof b==="object"&&Gp(b)===Hp)return a=Ip(b),{args:a.map(function(c){return b[c]}),keys:a}}return{args:a,keys:null}};function S(){var a=C.apply(0,arguments),b=mp(a),c=lp(a);a=Jp(a);var d=a.args,e=a.keys;if(d.length===0)return cp([],b);b=new N(Kp(d,b,e?function(f){for(var g={},h=0;h<f.length;h++)g[e[h]]=f[h];return g}:Mo));return c?b.i(Dp(c)):b}function Lp(a,b,c){Io.call(this,a);this.pd=b;this.l=c}y(Lp,Io);Lp.g=Io.g;Lp.create=Io.create;Lp.prototype.Gc=function(){this.l()?Io.prototype.Gc.call(this):this.unsubscribe()};function Kp(a,b,c){c=c===void 0?Mo:c;return function(d){Mp(b,function(){for(var e=a.length,f=Array(e),g=e,h=a.map(function(){return!1}),k=!0,l={sb:0};l.sb<e;l={sb:l.sb},l.sb++)Mp(b,function(m){return function(){cp(a[m.sb],b).subscribe(new Lp(d,function(q){f[m.sb]=q;k&&(h[m.sb]=!0,k=!h.every(Mo));k||d.next(c(f.slice()))},function(){return--g===0}))}}(l),d)},d)}}function Mp(a,b,c){a?c.add(a.I(b)):b()};function Np(a,b,c,d){function e(l){g++;dp(c(l,h++)).subscribe(new vp(b,function(m){b.next(m)},void 0,function(){g--;for(var m={};f.length&&g<d;m={ue:void 0})m.ue=f.shift(),e(m.ue);!k||f.length||g||b.complete()}))}var f=[],g=0,h=0,k=!1;a.subscribe(new vp(b,function(l){return g<d?e(l):f.push(l)},void 0,function(){k=!0;!k||f.length||g||b.complete()}));return function(){f=null}};function Op(a,b){var c=c===void 0?Infinity:c;if(L(b))return Op(function(d,e){return R(function(f,g){return b(d,f,e,g)})(dp(a(d,e)))},c);typeof b==="number"&&(c=b);return wp(function(d,e){return Np(d,e,a,c)})};function Pp(a){a=a===void 0?Infinity:a;return Op(Mo,a)};function Qp(){var a=C.apply(0,arguments);return Pp(1)(kp(a,mp(a)))};function Rp(a){return new N(function(b){dp(a()).subscribe(b)})};var Sp=["addListener","removeListener"],Tp=["addEventListener","removeEventListener"],Up=["on","off"];function Vp(a,b,c){if(L(c)){var d=c;c=void 0}if(d)return Vp(a,b,c).i(Dp(d));d=z(L(a.addEventListener)&&L(a.removeEventListener)?Tp.map(function(g){return function(h){return a[g](b,h,c)}}):L(a.addListener)&&L(a.removeListener)?Sp.map(Wp(a,b)):L(a.Mh)&&L(a.zh)?Up.map(Wp(a,b)):[]);var e=d.next().value,f=d.next().value;return!e&&$o(a)?Op(function(g){return Vp(g,b,c)})(kp(a)):new N(function(g){function h(){var k=C.apply(0,arguments);return g.next(1<k.length?k:k[0])}if(!e)throw new TypeError("Invalid event target");e(h);return function(){return f(h)}})}function Wp(a,b){return function(c){return function(d){return a[c](b,d)}}};function $p(){Co.call(this)}y($p,Co);$p.g=Co.g;$p.prototype.I=function(){return this};function aq(a,b){return setInterval.apply(null,[a,b].concat(A(C.apply(2,arguments))))};function bq(a,b){Co.call(this);this.g=a;this.l=b;this.pending=!1}y(bq,$p);bq.g=$p.g;n=bq.prototype;n.I=function(a,b){b=b===void 0?0:b;if(this.closed)return this;this.state=a;a=this.id;var c=this.g;a!=null&&(this.id=cq(this,a,b));this.pending=!0;this.delay=b;this.id=this.id||this.Ud(c,this.id,b);return this};n.Ud=function(a,b,c){c=c===void 0?0:c;return aq(a.flush.bind(a,this),c)};function cq(a,b,c){c=c===void 0?0:c;if(c!=null&&a.delay===c&&a.pending===!1)return b;clearInterval(b)}n.execute=function(a,b){if(this.closed)return Error("executing a cancelled action");this.pending=!1;if(a=this.ne(a,b))return a;this.pending===!1&&this.id!=null&&(this.id=cq(this,this.id,null))};n.ne=function(a){var b=!1;try{this.l(a)}catch(d){b=!0;var c=!!d&&d||Error(d)}if(b)return this.unsubscribe(),c};n.unsubscribe=function(){if(!this.closed){var a=this.id,b=this.g.actions;this.l=this.state=this.g=null;this.pending=!1;Bo(b,this);a!=null&&(this.id=cq(this,a,null));this.delay=null;$p.prototype.unsubscribe.call(this)}};function dq(a,b){b=b===void 0?sp:b;rp.call(this,a,b);this.actions=[];this.active=!1}y(dq,rp);dq.prototype.flush=function(a){var b=this.actions;if(this.active)b.push(a);else{var c;this.active=!0;do if(c=a.execute(a.state,a.delay))break;while(a=b.shift());this.active=!1;if(c){for(;a=b.shift();)a.unsubscribe();throw c;}}};function eq(){var a=C.apply(0,arguments),b=mp(a);var c=typeof a[a.length-1]==="number"?a.pop():Infinity;return a.length?a.length===1?dp(a[0]):Pp(c)(kp(a,b)):So};var fq=new N(Go);var gq=Array.isArray;function hq(a){return a.length===1&&gq(a[0])?a[0]:a};function iq(){var a=hq(C.apply(0,arguments));return wp(function(b,c){function d(){if(!c.closed)if(e.length>0){try{var f=dp(e.shift())}catch(h){d();return}var g=new vp(c,void 0,Go,Go);c.add(f.subscribe(g));g.add(d)}else c.complete()}var e=[b].concat(A(a));d()})};function T(a){return wp(function(b,c){var d=0;b.subscribe(new vp(c,function(e){return a.call(void 0,e,d++)&&c.next(e)}))})};function jq(){var a=C.apply(0,arguments);a=hq(a);return a.length===1?dp(a[0]):new N(kq(a))}function kq(a){return function(b){for(var c=[],d={Ob:0};c&&!b.closed&&d.Ob<a.length;d={Ob:d.Ob},d.Ob++)c.push(dp(a[d.Ob]).subscribe(new vp(b,function(e){return function(f){if(c){for(var g=0;g<c.length;g++)g!==e.Ob&&c[g].unsubscribe();c=null}b.next(f)}}(d))))}};function lq(){var a=C.apply(0,arguments),b=lp(a),c=hq(a);return c.length?new N(function(d){var e=c.map(function(){return[]}),f=c.map(function(){return!1});d.add(function(){e=f=null});for(var g={gb:0};!d.closed&&g.gb<c.length;g={gb:g.gb},g.gb++)dp(c[g.gb]).subscribe(new vp(d,function(h){return function(k){e[h.gb].push(k);e.every(function(l){return l.length})&&(k=e.map(function(l){return l.shift()}),d.next(b?b.apply(null,A(k)):k),e.some(function(l,m){return!l.length&&f[m]})&&d.complete())}}(g),void 0,function(h){return function(){f[h.gb]=!0;!e[h.gb].length&&d.complete()}}(g)));return function(){e=f=null}}):So};function mq(a,b){bq.call(this,a,b);this.g=a;this.l=b}y(mq,bq);mq.g=bq.g;mq.prototype.I=function(a,b){b=b===void 0?0:b;if(b>0)return bq.prototype.I.call(this,a,b);this.delay=b;this.state=a;this.g.flush(this);return this};mq.prototype.execute=function(a,b){return b>0||this.closed?bq.prototype.execute.call(this,a,b):this.ne(a,b)};mq.prototype.Ud=function(a,b,c){c=c===void 0?0:c;return c!=null&&c>0||c==null&&this.delay>0?bq.prototype.Ud.call(this,a,b,c):a.flush(this)};function nq(){dq.apply(this,arguments)}y(nq,dq);var Bp=new nq(mq);function oq(){this.K=new to;this.u=new uo;this.g=Symbol();this.oc=new yo}oq.prototype.Fd=function(){return fq};function pq(a,b){a.Ga!==null&&a.Ga.next(b)}function qq(a){if((typeof a==="bigint"||typeof a==="number"||typeof a==="string")&&typeof BigInt==="function")return BigInt(a)}ea.Object.defineProperties(oq.prototype,{Db:{configurable:!0,enumerable:!0,get:function(){return this.g}}});var rq=["sessionStart","sessionError","sessionFinish"];function sq(a,b){this.ga=a;this.gd=b;this.ready=!1;this.g=[];this.m=function(){};this.o=function(){};this.j=function(){};this.l=function(){};this.h=function(){}}function tq(a,b){a.m=b}function uq(a,b){a.o=b}function vq(a,b){a.j=b}function wq(a,b){a.l=b}function xq(a,b){a.h=b;a.h(a.g.length)}function yq(a){for(var b=z("geometryChange impression loaded start firstQuartile midpoint thirdQuartile complete pause resume bufferStart bufferFinish skipped volumeChange playerStateChange adUserInteraction".split(" ")),c=b.next();!c.done;c=b.next())a.ga.addEventListener(c.value,function(d){zq(a,d)});Mn(a.ga,function(d){d.type!=="sessionStart"&&zq(a,d)},a.gd);Mn(a.ga,function(d){d.type==="sessionStart"&&(zq(a,d),Aq(a),Bq(a))},a.gd)}function zq(a,b){a.g.push(b);a.h(a.g.length);Bq(a)}function Bq(a){if(a.ready)for(;a.g.length>0;){var b=a.g.pop();b!==void 0&&(b.type==="geometryChange"?a.j(b):b.type==="impression"?a.l(b):rq.includes(b.type)?a.m(b):a.o(b));a.h(a.g.length)}}function Aq(a){a.ready||(a.ready=!0,a.g.sort(function(b,c){return c.timestamp-b.timestamp}))};function Cq(a){return wp(function(b,c){var d=null,e=!1,f;d=b.subscribe(new vp(c,void 0,function(g){f=dp(a(g,Cq(a)(b)));d?(d.unsubscribe(),d=null,f.subscribe(c)):e=!0}));e&&(d.unsubscribe(),d=null,f.subscribe(c))})};function Dq(a,b,c){return function(d,e){var f=c,g=b,h=0;d.subscribe(new vp(e,function(k){var l=h++;g=f?a(g,k,l):(f=!0,k);e.next(g)},void 0,void 0))}};function Eq(){var a=C.apply(0,arguments),b=lp(a);return b?M(Eq.apply(null,A(a)),Dp(b)):wp(function(c,d){Kp([c].concat(A(hq(a))))(d)})}function Fq(){return Eq.apply(null,A(C.apply(0,arguments)))};function Gq(a){a=a===void 0?null:a;return wp(function(b,c){var d=!1;b.subscribe(new vp(c,function(e){d=!0;c.next(e)},void 0,function(){d||c.next(a);c.complete()}))})};function Hq(){return wp(function(a,b){a.subscribe(new vp(b,Go))})};function Iq(a){return wp(function(b,c){b.subscribe(new vp(c,function(){return c.next(a)}))})};function Jq(a){return a<=0?function(){return So}:wp(function(b,c){var d=0;b.subscribe(new vp(c,function(e){++d<=a&&(c.next(e),a<=d&&c.complete())}))})};function Kq(a){return Op(function(b,c){return a(b,c).i(Jq(1),Iq(b))})};function Lq(a){return wp(function(b,c){var d=new Set;b.subscribe(new vp(c,function(e){var f=a?a(e):e;d.has(f)||(d.add(f),c.next(e))}))})};function U(a){var b=b===void 0?Mo:b;var c;a=(c=a)!=null?c:Mq;return wp(function(d,e){var f,g=!0;d.subscribe(new vp(e,function(h){var k=b(h);if(g||!a(f,k))g=!1,f=k,e.next(h)}))})}function Mq(a,b){return a===b};function Nq(a){a=a===void 0?Oq:a;return wp(function(b,c){var d=!1;b.subscribe(new vp(c,function(e){d=!0;c.next(e)},void 0,function(){return d?c.complete():c.error(a())}))})}function Oq(){return new tp};function Pq(){var a=C.apply(0,arguments);return function(b){return Qp(b,Q.apply(null,A(a)))}};function Qq(a){return wp(function(b,c){var d=0;b.subscribe(new vp(c,function(e){a.call(void 0,e,d++,b)||(c.next(!1),c.complete())},void 0,function(){c.next(!0);c.complete()}))})};function Rq(){return wp(function(a,b){var c=[];a.subscribe(new vp(b,function(d){c.push(d);1<c.length&&c.shift()},void 0,function(){for(var d=z(c),e=d.next();!e.done;e=d.next())b.next(e.value);b.complete()},function(){c=null}))})};function Sq(a,b){var c=arguments.length>=2;return function(d){return d.i(a?T(function(e,f){return a(e,f,d)}):Mo,Rq(),c?Gq(b):Nq(function(){return new tp}))}};function Tq(a){var b=L(a)?a:function(){return a};return L()?wp(function(c,d){var e=b();(void 0)(e).subscribe(d).add(c.subscribe(e))}):function(c){var d=new yp(c,b);L(c==null?void 0:c.zb)&&(d.zb=c.zb);d.source=c;d.af=b;return d}};function Uq(a){var b=new pp(a,void 0,void 0);return function(c){return Tq(function(){return b})(c)}};function Vq(){var a=a===void 0?Infinity:a;return a<=0?function(){return So}:wp(function(b,c){function d(){var g=!1;f=b.subscribe(new vp(c,void 0,void 0,function(){++e<a?f?(f.unsubscribe(),f=null,d()):g=!0:c.complete()}));g&&(f.unsubscribe(),f=null,d())}var e=0,f;d()})};function Wq(a,b){return wp(Dq(a,b,arguments.length>=2))};function Xq(){var a=a||{};var b=a.Ef===void 0?function(){return new O}:a.Ef,c=a.zg===void 0?!0:a.zg,d=a.Ag===void 0?!0:a.Ag,e=a.Bg===void 0?!0:a.Bg;return function(f){function g(){h=k=null;m=q=!1}var h=null,k=null,l=0,m=!1,q=!1;return wp(function(t,r){l++;var w;k=(w=k)!=null?w:b();r.add(function(){l--;if(e&&!l&&!q&&!m){var u=h;g();u==null||u.unsubscribe()}});k.subscribe(r);!h&&l>0&&(h=new Ko({next:function(u){return k.next(u)},error:function(u){q=!0;var v=k;d&&g();v.error(u)},complete:function(){m=!0;var u=k;c&&g();u.complete()}}),cp(t).subscribe(h))})(f)}};function V(){var a=C.apply(0,arguments),b=mp(a);return wp(function(c,d){(b?Qp(a,c,b):Qp(a,c)).subscribe(d)})};function X(a){return wp(function(b,c){var d=null,e=0,f=!1;b.subscribe(new vp(c,function(g){var h;(h=d)==null||h.unsubscribe();h=e++;dp(a(g,h)).subscribe(d=new vp(c,function(k){return c.next(k)},void 0,function(){d=null;f&&!d&&c.complete()}))},void 0,function(){(f=!0,!d)&&c.complete()}))})};function Yq(a,b){b=b===void 0?!1:b;return wp(function(c,d){var e=0;c.subscribe(new vp(d,function(f){var g=a(f,e++);(g||b)&&d.next(f);!g&&d.complete()}))})};function Zq(a,b,c){var d=L(a)||b||c?{next:a,error:b,complete:c}:a;return d?wp(function(e,f){e.subscribe(new vp(f,function(g){var h;(h=d.next)==null||h.call(d,g);f.next(g)},function(g){var h;(h=d.error)==null||h.call(d,g);f.error(g)},function(){var g;(g=d.complete)==null||g.call(d);f.complete()}))}):Mo};function $q(){var a=C.apply(0,arguments),b=lp(a);return wp(function(c,d){for(var e=a.length,f=Array(e),g=a.map(function(){return!1}),h=!1,k={ab:0};k.ab<e;k={ab:k.ab},k.ab++)dp(a[k.ab]).subscribe(new vp(d,function(l){return function(m){f[l.ab]=m;h||g[l.ab]||(g[l.ab]=!0,(h=g.every(Mo))&&(g=null))}}(k),void 0,Go));c.subscribe(new vp(d,function(l){h&&(l=[l].concat(A(f)),d.next(b?b.apply(null,A(l)):l))}))})};function ar(a){this.ga=a}ar.prototype.O=function(a){return(a==null?0:a.fc)?!0:(a==null?void 0:a.na)==="POST"||(a==null?0:a.pb)||(a==null?0:a.Oc)?!1:this.ga.O()};ar.prototype.ping=function(){var a=this,b=Q.apply(null,A(C.apply(0,arguments))).i(Op(function(c){return br(a,c)}),Qq(function(c){return c}),Uq(1));b.connect();return b};function br(a,b){var c=new pp(1);Nn(a.ga,b,function(){c.next(!0);c.complete()},function(){c.next(!1);c.complete()});return c}ar.prototype.ad=function(a,b,c){this.ping.apply(this,A(C.apply(3,arguments)))};function cr(a,b){var c=!1;return new N(function(d){var e=a.setTimeout(function(){c=!0;d.next(!0);d.complete()},b);return function(){c||a.clearTimeout(e)}})};function dr(a,b){b=Error.call(this,b?a+": "+b:String(a));this.message=b.message;"stack"in b&&(this.stack=b.stack);this.code=a;this.__proto__=dr.prototype;this.name=String(a)}y(dr,Error);function er(a){dr.call(this,1E3,'sfr:"'+a+'"');this.g=a;this.__proto__=er.prototype}y(er,dr);function fr(){dr.call(this,1003);this.__proto__=fr.prototype}y(fr,dr);function gr(){dr.call(this,1009);this.__proto__=gr.prototype}y(gr,dr);function hr(){dr.call(this,1011);this.__proto__=hr.prototype}y(hr,dr);function ir(){dr.call(this,1007);this.__proto__=fr.prototype}y(ir,dr);function jr(){dr.call(this,1008);this.__proto__=fr.prototype}y(jr,dr);function kr(){dr.call(this,1001);this.__proto__=kr.prototype}y(kr,dr);function lr(a){dr.call(this,1004,String(a));this.g=a;this.__proto__=lr.prototype}y(lr,dr);function mr(a){dr.call(this,1010,a);this.__proto__=nr.prototype}y(mr,dr);function nr(a){dr.call(this,1005,a);this.__proto__=nr.prototype}y(nr,dr);var or=Symbol("time-origin"),pr=Symbol("date");function qr(a,b){this.value=a;this.timeline=b}function rr(a,b){if(b.timeline!==a.timeline)throw new ir;}function sr(a,b){rr(a,b);return a.value-b.value}n=qr.prototype;n.equals=function(a){return sr(this,a)===0};n.maximum=function(a){rr(this,a);return this.value>=a.value?this:a};n.round=function(){return new qr(Math.round(this.value),this.timeline)};n.add=function(a){return new qr(this.value+a,this.timeline)};n.toString=function(){return String(this.value)};function tr(a){this.ga=a;this.timeline=pr}n=tr.prototype;n.setTimeout=function(a,b){return Number(this.ga.setTimeout(function(){return a()},b))};n.clearTimeout=function(a){this.ga.clearTimeout(a)};n.now=function(){return new qr(Date.now(),this.timeline)};n.interval=function(a,b){var c=this.Ia(a).subscribe(b);return function(){return void c.unsubscribe()}};n.Ia=function(a){return cr(this,a).i(Vq(),Wq(function(b){return b+1},-1))};n.ma=function(){return!0};function ur(a,b){this.context=a;this.g=b}ur.prototype.O=function(a){return this.g.O(a)};ur.prototype.M=function(a,b){if(!this.O(b))throw new gr;return new vr(this.context,this.g,b!=null?b:void 0,a)};function vr(a,b,c,d){var e=this;this.h=b;this.properties=c;this.url=d;this.g=!0;this.pb=new Map;this.body=void 0;var f;this.method=(f=c==null?void 0:c.na)!=null?f:"GET";this.j=a.Fd().subscribe(function(){e.sendNow()})}vr.prototype.deactivate=function(){this.g=!1};vr.prototype.sendNow=function(){if(this.g)if(this.j.unsubscribe(),this.h.O(this.properties))try{if(this.pb.size>0||this.body!==void 0){var a,b;this.h.ad((a=this.properties)!=null?a:{},this.pb,(b=this.body)!=null?b:"",this.url)}else this.h.ping(this.url);this.g=!1}catch(c){}else this.g=!1};function wr(a,b,c,d,e,f){this.mode=a;this.B=b;this.j=c;this.g=d;this.m=e;this.l=f;this.h=!1;this.id=this.mode===0?xr(this):0}function xr(a){return a.B.setTimeout(function(){yr(a)},a.g)}function zr(a,b){var c=sr(b,a.j);c>=a.g?yr(a):(a.j=b,a.g-=c)}function yr(a){try{a.m(a.j.add(a.g))}finally{a.h=!0,a.l()}}wr.prototype.fe=function(a,b){this.h||(this.mode===1&&a===1?zr(this,b):this.mode===1&&a===0?(this.mode=a,zr(this,this.B.now()),this.h||(this.id=xr(this))):this.mode===0&&a===1&&(this.mode=a,this.clear(),zr(this,b)))};wr.prototype.clear=function(){this.h||this.B.clearTimeout(this.id)};function Ar(a){this.Pc=a;this.eg=this.mode=0;this.Qb={};this.timeline=a.timeline;this.yb=a.now()}n=Ar.prototype;n.fe=function(a,b){this.mode=a;rr(this.yb,b);this.yb=b;Object.values(this.Qb).forEach(function(c){return void c.fe(a,b)})};n.now=function(){return this.mode===1?this.yb:this.Pc.now()};n.setTimeout=function(a,b){var c=this,d=++this.eg,e=this.mode===1?this.yb:this.Pc.now();this.Qb[d]=new wr(this.mode,this.Pc,e,b,function(f){var g=c.yb;c.mode===1&&(c.yb=f);a();c.yb=g},function(){delete c.Qb[d]});return d};n.clearTimeout=function(a){this.Qb[a]&&(this.Qb[a].clear(),delete this.Qb[a])};n.interval=function(){throw Error("'interval' is not supported for DualModeTimeProvider");};n.Ia=function(){throw Error("'intervalObservable' is not supported for DualModeTimeProvider");};n.ma=function(){return this.Pc.ma()};function Br(a,b){var c=new Ar(a);a=b.subscribe(function(d){c.fe(d.value?1:0,d.timestamp)});return{B:c,yh:a}};function Cr(a){var b=Object.assign({},a);delete b.timestamp;return{timestamp:new qr(a.timestamp,pr),value:b}};function Dr(a){return a!==void 0&&typeof a.x==="number"&&typeof a.y==="number"&&typeof a.width==="number"&&typeof a.height==="number"};var Er=na(["https://www.googleadservices.com/pagead/managed/js/activeview/","/reach_worklet.html"]),Fr=na(["./reach_worklet.js"]),Gr=na(["./reach_worklet.js"]),Hr=na(["./reach_worklet.html"]),Ir=na(["./reach_worklet.js"]),Jr=na(["./reach_worklet.js"]);function Kr(){var a={};return a[0]=Pc(Er,"current"),a[1]=Pc(Fr),a[2]=Pc(Gr),a}Pc(Hr);Pc(Ir);Pc(Jr);function Lr(a,b,c,d){c=c===void 0?null:c;d=d===void 0?Kr():d;oq.call(this);this.ga=a;this.gd=b;this.Ga=c;this.ae=d;this.eb=null;this.Wd=new pp(3);this.Wd.i(T(function(e){return e.value.type==="sessionStart"}));this.Dg=this.Wd.i(T(function(e){return e.value.type==="sessionFinish"}));this.Oe=new pp(1);this.Ng=new pp;this.Ke=new pp(10);this.L=new ur(this,new ar(a));this.jg=this.ga.O();this.B=Mr(this,new tr(this.ga))}y(Lr,oq);function Nr(a){a.eb!==null&&yq(a.eb)}Lr.prototype.validate=function(){return this.jg};function Mr(a,b){a.eb=new sq(a.ga,a.gd);var c=new pp;tq(a.eb,function(f){f=Cr(f);c.next({timestamp:f.timestamp,value:!0});a.Wd.next(f)});vq(a.eb,function(f){if(f===void 0)var g=!1;else{g=f.data;var h;(h=g===void 0)||(h=g.viewport,h=h===void 0||h!==void 0&&typeof h.width==="number"&&typeof h.height==="number");h?(g=g.adView,g=g!==void 0&&typeof g.percentageInView==="number"&&(g.geometry===void 0||Dr(g.geometry))&&(g.onScreenGeometry===void 0||Dr(g.onScreenGeometry))):g=!1}g?(f=Cr(f),c.next({timestamp:f.timestamp,value:!0}),a.Ke.next(f)):.01>=Math.random()&&(f="https://pagead2.googlesyndication.com/pagead/gen_204?id=av-js&type=error&name=invalid_geo&context=1092&msg="+JSON.stringify(f),a.L.M(f).sendNow())});uq(a.eb,function(f){f=Cr(f);c.next({timestamp:f.timestamp,value:!0});a.Ng.next(f)});wq(a.eb,function(f){f=Cr(f);c.next({timestamp:f.timestamp,value:!0});a.Oe.next(f)});var d=0;xq(a.eb,function(f){d+=f;d>0&&f===0&&c.next({timestamp:a.B.now(),value:!1})});var e=c.i(Yq(function(f){return f.value},!0));return Br(b,e).B}ea.Object.defineProperties(Lr.prototype,{global:{configurable:!0,enumerable:!0,get:function(){return Or}}});var Or={};function Pr(a){try{var b=new Ln;return new Lr(b,"doubleclickbygoogle.com-omid",void 0,a)}catch(c){}};function Qr(a,b){return function(c){return new N(function(d){return c.subscribe(function(e){a.Xb(b,function(){d.next(e)})()},function(e){a.Xb(b,function(){d.error(e)})()},function(){a.Xb(b,function(){d.complete()})()})})}};function Rr(){for(var a=z(C.apply(0,arguments)),b=a.next();!b.done;b=a.next())if(b=b.value,b.ma()){this.B=b;return}this.B=new Sr}n=Rr.prototype;n.ma=function(){return this.B.ma()};n.now=function(){return this.B.now()};n.setTimeout=function(a,b){return this.B.setTimeout(a,b)};n.clearTimeout=function(a){this.B.clearTimeout(a)};n.interval=function(a,b){var c=this.Ia(a).subscribe(b);return function(){return void c.unsubscribe()}};n.Ia=function(a){return this.B.Ia(a)};ea.Object.defineProperties(Rr.prototype,{timeline:{configurable:!0,enumerable:!0,get:function(){return this.B.timeline}}});function Sr(){this.timeline=Symbol()}n=Sr.prototype;n.ma=function(){return!1};n.now=function(){return new qr(0,this.timeline)};n.setTimeout=function(){return 0};n.clearTimeout=function(){};n.interval=function(){return function(){}};n.Ia=function(){return fq};function Tr(a,b){this.S=a;this.K=b}n=Tr.prototype;n.setTimeout=function(a,b){return this.S.setTimeout(this.K.Xb(734,a),b)};n.clearTimeout=function(a){this.S.clearTimeout(a)};n.interval=function(a,b){var c=this.Ia(a).subscribe(b);return function(){return void c.unsubscribe()}};n.Ia=function(a){var b=this;return new N(function(c){var d=0,e=b.S.setInterval(function(){c.next(d++)},a);return function(){b.S.clearInterval(e)}})};n.ma=function(){return!!this.S.clearTimeout&&"setTimeout"in this.S&&"setInterval"in this.S&&!!this.S.clearInterval};function Ur(a,b){Tr.call(this,a,b);this.timeline=pr}y(Ur,Tr);Ur.prototype.now=function(){return new qr(this.S.Date.now(),this.timeline)};Ur.prototype.ma=function(){return!!this.S.Date&&!!this.S.Date.now&&Tr.prototype.ma.call(this)};function Vr(a,b){Tr.call(this,a,b);this.timeline=or}y(Vr,Tr);Vr.prototype.now=function(){return new qr(this.S.performance.now(),this.timeline)};Vr.prototype.ma=function(){return!!this.S.performance&&!!this.S.performance.now&&Tr.prototype.ma.call(this)};function Wr(a){var b=C.apply(1,arguments),c=this;this.g=[];this.g.push(a);b.forEach(function(d){c.g.push(d)})}Wr.prototype.O=function(a){return this.g.some(function(b){return b.O(a)})};Wr.prototype.M=function(a,b){for(var c=0;c<this.g.length;c++)if(this.g[c].O(b))return this.g[c].M(a,b);throw new gr;};function Xr(a){a=a.global;if(a.fetchLater)return a.fetchLater.bind(a)}function Yr(a){this.context=a;if(Zr===void 0)a:{var b,c,d=(b=a.global)==null?void 0:(c=b.document)==null?void 0:c.createElement("meta");if(d)try{d.httpEquiv="origin-trial";d.content="AxjhRadLCARYRJawRjMjq4U8V8okQvSnrBIJWdMajuEkN3/DfVAcLcFhMVrUWnOXagwlI8dQD84FwJDGj9ohqAYAAABveyJvcmlnaW4iOiJodHRwczovL2dvb2dsZWFkc2VydmljZXMuY29tOjQ0MyIsImZlYXR1cmUiOiJGZXRjaExhdGVyQVBJIiwiZXhwaXJ5IjoxNzI1NDA3OTk5LCJpc1RoaXJkUGFydHkiOnRydWV9";a.global.document.head.append(d);Zr=d;break a}catch(e){}Zr=void 0}}var Zr;Yr.prototype.O=function(a){return Xr(this.context)!==void 0&&!(a==null||!a.Ce)&&!$r(this.context)&&!(a==null?0:a.fc)&&!(a==null?0:a.pb)&&!(a==null?0:a.Oc)};Yr.prototype.M=function(a,b){if(!this.O(b))throw new gr;return new as(this.context,a,b)};function as(a,b,c){this.context=a;this.properties=c;this.j=b;var d;this.na=(d=c==null?void 0:c.na)!=null?d:"GET";a=Xr(this.context);if(a===void 0)throw Error();this.fetchLater=a;bs(this,cs(this))}function bs(a,b){a.g&&a.g.activated||(a.h=new AbortController,a.g=a.fetchLater(b,{method:a.na,cache:"no-cache",mode:"no-cors",signal:a.h.signal,activateAfter:96E4}))}function cs(a){a=a.j;return(a.slice(-1)[0]==="&"?a:a+"&")+"flapi=1"}as.prototype.deactivate=function(){this.g&&!this.g.activated&&this.h&&(this.h.abort(),this.g=void 0)};as.prototype.sendNow=function(){};ea.Object.defineProperties(as.prototype,{url:{configurable:!0,enumerable:!0,get:function(){return this.j},set:function(a){this.j=a;a=cs(this);this.g&&this.g.activated||!this.h||(this.h.abort(),this.g=void 0);bs(this,a)}},method:{configurable:!0,enumerable:!0,get:function(){return this.na}}});function ds(a){this.context=a}ds.prototype.O=function(){return!$r(this.context)&&!!this.context.global.fetch};ds.prototype.ping=function(){var a=this;return eq.apply(null,A(C.apply(0,arguments).map(function(b){return cp(a.context.global.fetch(b,{method:"GET",cache:"no-cache",keepalive:!0,mode:"no-cors"})).i(R(function(c){return c.status===200}))}))).i(Qq(function(b){return b}),Sq())};ds.prototype.ad=function(a,b,c){for(var d=C.apply(3,arguments),e=this,f=new Headers,g=z(b.entries()),h=g.next();!h.done;h=g.next()){var k=z(h.value);h=k.next().value;k=k.next().value;f.set(h,k)}var l,m=(l=a.keepAlive)!=null?l:!1;eq.apply(null,A(d.map(function(q){return cp(e.context.global.fetch(q,Object.assign({},{method:String(a.na),cache:"no-cache"},m?{keepalive:!0}:{},{mode:"no-cors",headers:f,body:c}))).i(R(function(t){return t.status===200}))}))).i(Qq(function(q){return q}),Sq())};function es(a){this.context=a}es.prototype.O=function(a){return(a==null?0:a.fc)||(a==null?void 0:a.na)==="POST"||(a==null?0:a.pb)||(a==null?0:a.Oc)||(a==null?0:a.keepAlive)?!1:!$r(this.context)};es.prototype.ping=function(){var a=this;return Q(C.apply(0,arguments).map(function(b){try{return Rd(a.context.global,b,!1,!1,!1),!0}catch(c){return!1}}).every(function(b){return b}))};es.prototype.ad=function(a,b,c){this.ping.apply(this,A(C.apply(3,arguments)))};function fs(a){a=a.global;if(a.PendingGetBeacon)return a.PendingGetBeacon}function gs(a){this.context=a}gs.prototype.O=function(a){return hs&&!$r(this.context)&&fs(this.context)!==void 0&&!(a==null?0:a.fc)&&(a==null?void 0:a.na)!=="POST"&&!(a==null?0:a.pb)&&!(a==null?0:a.Oc)};gs.prototype.M=function(a,b){if(!this.O(b))throw new gr;return new is(this.context,a)};var hs=!1;function is(a,b){this.context=a;this.g=b;a=fs(this.context);if(a===void 0)throw Error();this.h=new a(js(this),{})}function js(a){a=a.g;return(a.slice(-1)[0]==="&"?a:a+"&")+"pbapi=1"}is.prototype.deactivate=function(){this.h.deactivate()};is.prototype.sendNow=function(){this.h.sendNow()};ea.Object.defineProperties(is.prototype,{url:{configurable:!0,enumerable:!0,get:function(){return this.g},set:function(a){this.g=a;this.h.setURL(js(this))}},method:{configurable:!0,enumerable:!0,get:function(){return"GET"},set:function(a){if(a!=="GET")throw new gr;}}});function ks(a){this.context=a}ks.prototype.O=function(a){if((a==null?0:a.fc)||(a==null?void 0:a.na)==="GET"||(a==null?0:a.pb)||(a==null?0:a.Oc)||(a==null?0:a.keepAlive))return!1;var b;return!$r(this.context)&&((b=this.context.global.navigator)==null?void 0:b.sendBeacon)!==void 0};ks.prototype.ping=function(){var a=this;return Q(C.apply(0,arguments).map(function(b){var c;return(c=a.context.global.navigator)==null?void 0:c.sendBeacon(b)}).every(function(b){return b}))};ks.prototype.ad=function(a,b,c){this.ping.apply(this,A(C.apply(3,arguments)))};function ls(a){var b=b===void 0?{}:b;if(typeof Event==="function")return new Event(a,b);if(typeof document!=="undefined"){var c=document.createEvent("CustomEvent");c.initCustomEvent(a,b.bubbles||!1,b.cancelable||!1,b.detail);return c}throw Error();};function ms(a){this.value=a;this.j=new O}function ns(a){a.j.next();a.j.complete();a.value=void 0}ea.Object.defineProperties(ms.prototype,{g:{configurable:!0,enumerable:!0,get:function(){return this.value}},h:{configurable:!0,enumerable:!0,get:function(){return this.j}}});function os(a,b,c){if(a)for(var d=0;a!=null&&d<500&&!c(a);++d)a=b(a)}function ps(a,b){os(a,function(c){try{return c===c.parent?null:c.parent}catch(d){}return null},b)}function qs(a,b){if(a.tagName=="IFRAME")b(a);else{a=a.querySelectorAll("IFRAME");for(var c=0;c<a.length&&!b(a[c]);++c);}}function rs(a){return(a=a.ownerDocument)&&(a.parentWindow||a.defaultView)||null}function ss(a,b,c){try{var d=JSON.parse(c.data)}catch(g){}if(typeof d==="object"&&d&&d.type==="creativeLoad"){var e=rs(a);if(c.source&&e){var f;ps(c.source,function(g){try{if(g.parent===e)return f=g,!0}catch(h){}});f&&qs(a,function(g){if(g.contentWindow===f)return b(d),!0})}}}function ts(a){return typeof a==="string"?document.getElementById(a):a}function us(a,b){var c=ts(a);if(c)if(c.onCreativeLoad)c.onCreativeLoad(b);else{var d=b?[b]:[],e=function(f){for(var g=0;g<d.length;++g)try{d[g](1,f)}catch(h){}d={push:function(h){h(1,f)}}};c.onCreativeLoad=function(f){d.push(f)};c.setAttribute("data-creative-load-listener","");c.addEventListener("creativeLoad",function(f){e(f.detail)});Ma.addEventListener("message",function(f){ss(c,e,f)})}};function vs(a,b){var c=this;this.global=a;this.g=b;this.j=this.document?eq(Q(!0),Vp(this.document,"visibilitychange")).i(Qr(this.g.K,748),R(function(){return c.document?c.document.visibilityState:"visible"}),U()):Q("visible");this.h=this.document?Vp(this.document,"DOMContentLoaded").i(Qr(this.g.K,739),Jq(1)):Q(ls("DOMContentLoaded"))}function ws(a){return a.document?a.document.readyState:"complete"}function xs(a){return a.document!==null&&a.document.visibilityState!==void 0}vs.prototype.querySelector=function(a){return this.document?this.document.querySelector(a):null};vs.prototype.querySelectorAll=function(a){return this.document?eb(this.document.querySelectorAll(a)):[]};function ys(a,b,c){function d(){e.next(b)}c=c===void 0?!1:c;if(b.g===void 0||!a.document)return Q(b).i(Qr(a.g.K,749));var e=new pp(1);c||us(b.g,d);Dm(b.g,d,ym());return e.i(Qr(a.g.K,749),Jq(1))}function zs(a,b){a=a.document;if(!a)return Q(!1);var c=eq(Q(null),Vp(a,"DOMContentLoaded",{once:!0}),Vp(a,"load",{once:!0})),d=new ms({document:a,element:b});return c.i(R(function(){if(!d.g)return!1;var e=d.g,f=e.document;e=e.element;var g,h,k=(h=(g=f.body)!=null?g:f.children[0])!=null?h:f;try{k.appendChild(e),ns(d)}catch(l){}return!d.g}),T(function(e){return e}),Jq(1),Gq(!1),Zq({complete:function(){return void ns(d)}}))}function As(a,b,c){var d,e,f;return Ga(function(g){if(g.g==1){d=a.global.document.createElement("iframe");e=new Promise(function(k){d.onload=k;d.onerror=k});if(b instanceof qc)var h=b.g;else throw Error("");d.src=h.toString();return ua(g,up(zs(a,d)),2)}if(g.g!=3){if(!g.h)return g.return();d.style.display="none";return ua(g,e,3)}f=d.contentWindow;if(!f)return g.return();f.postMessage(c,"*");return g.return(d)})}ea.Object.defineProperties(vs.prototype,{document:{configurable:!0,enumerable:!0,get:function(){return Fc(this.global,"document")?this.global.document||null:null}}});function Bs(a){return function(b){return b.i(Cs(a,Tq(new O)))}}function Y(a,b){return function(c){return c.i(Cs(a,Uq(b)))}}function Cs(a,b){function c(d){return new N(function(e){return d.subscribe(function(f){vo(a,function(){return void e.next(f)},3)},function(f){vo(a,function(){return void e.error(f)},3)},function(){vo(a,function(){return void e.complete()},3)})})}return M(c,Ap(),b,xp(),c)};var Ds={left:0,top:0,width:0,height:0};function Es(a,b){return a.left===b.left&&a.top===b.top&&a.width===b.width&&a.height===b.height}function Fs(a,b){return{left:Math.max(a.left,b.left),top:Math.max(a.top,b.top),width:Math.max(0,Math.min(a.left+a.width,b.left+b.width)-Math.max(a.left,b.left)),height:Math.max(0,Math.min(a.top+a.height,b.top+b.height)-Math.max(a.top,b.top))}}function Gs(a,b){return{left:Math.round(a.left+b.x),top:Math.round(a.top+b.y),width:a.width,height:a.height}};function Lg(a){this.C=jg(a)}y(Lg,$h);Lg.prototype.Me=function(){return Qg(this)};function Hs(a){this.C=jg(a)}y(Hs,$h);function Is(a,b){return Ug(a,2,b)}function Js(a,b){return Ug(a,3,b)}function Ks(a,b){return Ug(a,4,b)}function Ls(a,b){return Ug(a,5,b)}function Ms(a,b){return Ug(a,9,b)}function Ns(a,b){qg(a);var c=a.C,d=c[H]|0;if(b==null)vg(c,d,10);else{for(var e=b===Ne?7:b[H]|0,f=e,g=Dg(e),h=g||Object.isFrozen(b),k=!0,l=!0,m=0;m<b.length;m++){var q=b[m];g||(q=Ve(q),k&&(k=!q),l&&(l=q))}g||(e=k?13:5,e=l?e&-4097:e|4096);h&&e===f||(b=vf(b),f=0,e=Bg(e,d));e!==f&&Pe(b,e);d=vg(c,d,10,b);2&e||!(4096&e||16&e)||rg(c,d)}return a}function Os(a,b){return Rg(a,11,b)}function Ps(a,b){return Ug(a,1,b)}function Qs(a,b){return Rg(a,7,b)};var Rs="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Ss(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function Ts(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function Us(a){if(!Ts(a))return null;var b=Ss(a);if(b.uach_promise)return b.uach_promise;a=a.navigator.userAgentData.getHighEntropyValues(Rs).then(function(c){b.uach!=null||(b.uach=c);return c});return b.uach_promise=a}function Vs(a){var b;return Os(Ns(Ls(Is(Ps(Ks(Qs(Ms(Js(new Hs,a.architecture||""),a.bitness||""),a.mobile||!1),a.model||""),a.platform||""),a.platformVersion||""),a.uaFullVersion||""),((b=a.fullVersionList)==null?void 0:b.map(function(c){var d=new Lg;d=Ug(d,1,c.brand);return Ug(d,2,c.version)}))||[]),a.wow64||!1)}function Ws(a){var b,c;return(c=(b=Us(a))==null?void 0:b.then(function(d){return Vs(d)}))!=null?c:null};function Xs(a,b,c,d){a=a===void 0?window:a;b=b===void 0?null:b;c=c===void 0?new to:c;d=d===void 0?Kr():d;oq.call(this);var e=this;this.global=a;this.Ga=b;this.K=c;this.ae=d;this.vg=Rp(function(){return Vp(e.global,"pagehide")}).i(Qr(this.K,941));this.Xe=Rp(function(){return Vp(e.global,"load")}).i(Qr(this.K,738),Jq(1));this.wg=Rp(function(){return Vp(e.global,"resize")}).i(Qr(this.K,741));this.onMessage=Rp(function(){return Vp(e.global,"message")}).i(Qr(this.K,740));this.document=new vs(this.global,this);this.B=new Rr(new Vr(this.S,this.K),new Ur(this.S,this.K));this.L=new Wr(new Yr(this),new gs(this),new ur(this,new ds(this)),new ur(this,new ks(this)),new ur(this,new es(this)))}y(Xs,oq);function Ys(a){try{return!!a.global.sharedStorage}catch(b){return b}}function $r(a){var b=a.global;return!!a.global.HTMLFencedFrameElement&&!!b.fence&&typeof b.fence.reportEvent==="function"}n=Xs.prototype;n.Tb=function(a){$r(this)&&this.global.fence.reportEvent(a)};n.Fd=function(){return this.vg.i(Qr(this.K,942),Y(this.u,1),R(function(){}))};function Zs(a){var b=new Xs(a.global.top,a.Ga);b.L=a.L;return b}function $s(a,b){b.start();return Vp(b,"message").i(Qr(a.K,740))}n.postMessage=function(a,b,c){c=c===void 0?[]:c;this.global.postMessage(a,b,c)};n.Za=function(){return Bd(this.global)?this.global.width:0};n.Xa=function(){return Bd(this.global)?this.global.height:0};function at(a,b){try{var c=Wj(b,a.global,Ad()||zd());return{left:c.left,top:c.top,width:c.Za(),height:c.Xa()}}catch(d){return Ds}}n.validate=function(){var a=this.L.O()||$r(this);return this.global&&this.B.ma()&&a};function bt(a){return(a=Ws(a.global))?cp(a):null}ea.Object.defineProperties(Xs.prototype,{sharedStorage:{configurable:!0,enumerable:!0,get:function(){try{return this.global.sharedStorage}catch(a){}}},S:{configurable:!0,enumerable:!0,get:function(){return window}},rc:{configurable:!0,enumerable:!0,get:function(){return!Bd(this.global.top)}},Hd:{configurable:!0,enumerable:!0,get:function(){return this.rc||this.global.top!==this.global}},scrollY:{configurable:!0,enumerable:!0,get:function(){return this.global.scrollY}},MutationObserver:{configurable:!0,enumerable:!0,get:function(){return this.S.MutationObserver}},ResizeObserver:{configurable:!0,enumerable:!0,get:function(){return this.S.ResizeObserver}},Cf:{configurable:!0,enumerable:!0,get:function(){return"vu"in this.global||"vv"in this.global}}});function ct(a){this.children=a;this.m=!1;this.j=[]}ct.prototype.complete=function(){var a=this;this.m=!0;this.j.forEach(function(b){return void b(a)});this.j.splice(0)};function dt(a,b){a.m?b(a):a.j.push(b)}ct.prototype.nb=function(a){var b=this.children.map(function(c){return c.nb(a)});return b.find(function(c){return c!==2})===void 0?2:this.g?0:b.some(function(c){return c===1})?1:0};ea.Object.defineProperties(ct.prototype,{g:{configurable:!0,enumerable:!0,get:function(){return this.m}}});function et(){var a=C.apply(0,arguments);ct.call(this,a);var b=this;this.events=a;var c=this.events.length;this.events.forEach(function(d){dt(d,function(){--c===0&&b.complete()})})}y(et,ct);et.prototype.clone=function(){return new (Function.prototype.bind.apply(et,[null].concat(A(this.events.map(function(a){return a.clone()})))))};et.prototype.o=function(a,b){var c=this;if(!this.g){var d=this.events.find(function(e){return e.nb(a)===1});d!==void 0&&d.o(a,function(){c.g||b()})}};function ft(a,b){ct.call(this,[]);this.l=a;this.h=Symbol(b);this.A=a}y(ft,ct);ft.prototype.clone=function(){var a=new ft(this.A,this.h.description);a.h=this.h;return a};ft.prototype.nb=function(a){return a!==this.event?2:this.g||this.l===0?0:1};ft.prototype.o=function(a,b){this.nb(a)===1&&(this.l--,b(),this.l===0&&this.complete())};ea.Object.defineProperties(ft.prototype,{event:{configurable:!0,enumerable:!0,get:function(){return this.h}}});function gt(a){ft.call(this,1,a)}y(gt,ft);function ht(a){var b=this;this.g=!1;this.j=[];this.h=[];a(function(c){b.g=!0;b.resolution=c;jt(b)},function(c){b.l=c;jt(b)})}function kt(a){return new ht(function(b,c){var d=[],e=0;a.forEach(function(f,g){lt(f.then(function(h){d[g]=h;++e===a.length&&b(d)}),function(h){c(h)})})})}function jt(a){var b=a.resolution,c=a.l;if(c!==void 0||a.g)a.g&&a.j.forEach(function(d){return void d(b)}),c!==void 0&&a.h.forEach(function(d){return void d(c)}),a.j=[],a.h=[]}ht.prototype.then=function(a){this.j.push(a);jt(this);return this};function lt(a,b){a.h.push(b);jt(a)};function mt(a,b,c){var d=C.apply(3,arguments);this.g=a;this.m=b;this.l=c;this.j=new Set;this.h=d;if(this.g.U)this.context=this.g.U;else if(this.g.ha)this.context=this.g.ha;else throw Error("Mediator requires a Web or OMID context.");var e=d.reduce(function(h,k){k.subscribedEvents.forEach(function(l){return void h.add(l)});return h},new Set);e=z(e.values());for(var f=e.next(),g={};!f.done;g={Ge:void 0},f=e.next()){g.Ge=f.value;f=d.filter(function(h){return function(k){return k.controlledEvents.indexOf(h.Ge)>=0}}(g));if(f.length===0)throw Error("Event missing corresponding producing colleague.");if(f.length>1)throw Error("Event has one too many producers.");}}mt.prototype.start=function(){var a=this;this.h.forEach(function(b){return void b.start(a.g)});this.l.start(this.g,this.o.bind(this),this.Mb.bind(this),function(){})};mt.prototype.dispose=function(){var a=this;this.l.dispose();this.j.forEach(function(b){return void a.Mb(b)});this.h.forEach(function(b){return void b.dispose()})};function nt(a,b){b={measuringCreativeIds:[].concat(A(a.j.values())).map(function(c){return zo(a.context.oc,c)}),hasCreativeSourceCompleted:!!a.l.bd,colleagues:a.h.map(function(c){return{name:c.name,controlledEvents:c.controlledEvents.map(function(d){var e;return(e=d.description)!=null?e:"n/a"}),subscribedEvents:c.subscribedEvents.map(function(d){var e;return(e=d.description)!=null?e:"n/a"})}}),ephemeralCreativeStateChanges:b};b={specMajor:2,specMinor:0,specPatch:0,instanceId:zo(a.context.oc,a.context.Db),timestamp:sr(a.context.B.now(),new qr(0,a.context.B.timeline)),mediatorState:b};pq(a.context,b)}function ot(a,b,c,d,e){var f={};nt(a,(f[b]={events:[{timestamp:c,description:d,status:e}]},f))}mt.prototype.o=function(a,b,c){var d=this;if(!this.j.has(a)){var e=this.m.clone();this.j.add(a);nt(this,{});var f=!1,g=[];this.h.forEach(function(h){function k(l,m,q){var t=zo(d.context.oc,a),r=sr(d.context.B.now(),new qr(0,d.context.B.timeline)),w,u=(w=l.description)!=null?w:"n/a";if(h.controlledEvents.indexOf(l)<0||e.nb(l)!==1)return q(!1),ot(d,t,r,u,1),new ht(function(B){return void B()});var v=new ht(function(B){e.o(l,function(){d.h.filter(function(P){return P.subscribedEvents.indexOf(l)>=0}).forEach(function(P){return void P.handleEvent(a,l,m)});B()})});return new ht(function(B){v.then(function(){q(!0);ot(d,t,r,u,2);B()})})}h.Gd(a,b,c,function(l,m,q){return f?k(l,m,q):new ht(function(t){g.push(function(){k(l,m,q).then(function(){t()})})})},function(l){try{d.context.L.M("https://pagead2.googlesyndication.com/pagead/gen_204?id=av-js&type=colleague-executed&name="+l,{na:"GET"}).sendNow()}catch(m){}})});f=!0;g.forEach(function(h){return void h()})}};mt.prototype.Mb=function(a){this.j.delete(a);this.h.forEach(function(b){b.Mb(a)});nt(this,{})};var pt={},qt=(pt["data-google-av-cxn"]="_avicxn_",pt["data-google-av-cpmav"]="_cvu_",pt["data-google-av-metadata"]="_avm_",pt["data-google-av-adk"]="_adk_",pt["data-google-av-btr"]=void 0,pt["data-google-av-override"]=void 0,pt["data-google-av-dm"]=void 0,pt["data-google-av-immediate"]=void 0,pt["data-google-av-aid"]=void 0,pt["data-google-av-naid"]=void 0,pt["data-google-av-inapp"]=void 0,pt["data-google-av-slift"]=void 0,pt["data-google-av-itpl"]=void 0,pt["data-google-av-ext-cxn"]=void 0,pt["data-google-av-rs"]=void 0,pt["data-google-av-flags"]=void 0,pt["data-google-av-turtlex"]=void 0,pt["data-google-av-ufs-integrator-metadata"]=void 0,pt["data-google-av-vattr"]=void 0,pt["data-google-av-vrus"]=void 0,pt),rt={},st=(rt["data-google-av-adk"]="googleAvAdk",rt["data-google-av-btr"]="googleAvBtr",rt["data-google-av-cpmav"]="googleAvCpmav",rt["data-google-av-dm"]="googleAvDm",rt["data-google-av-ext-cxn"]="googleAvExtCxn",rt["data-google-av-immediate"]="googleAvImmediate",rt["data-google-av-inapp"]="googleAvInapp",rt["data-google-av-itpl"]="googleAvItpl",rt["data-google-av-metadata"]="googleAvMetadata",rt["data-google-av-naid"]="googleAvNaid",rt["data-google-av-override"]="googleAvOverride",rt["data-google-av-rs"]="googleAvRs",rt["data-google-av-slift"]="googleAvSlift",rt["data-google-av-cxn"]="googleAvCxn",rt["data-google-av-aid"]=void 0,rt["data-google-av-flags"]="googleAvFlags",rt["data-google-av-turtlex"]="googleAvTurtlex",rt["data-google-av-ufs-integrator-metadata"]="googleAvUfsIntegratorMetadata",rt["data-google-av-vattr"]="googleAvVattr",rt["data-google-av-vrus"]="googleAvVurs",rt);function tt(a,b){if(a.g===void 0)return null;try{var c;var d=(c=a.g.getAttribute(b))!=null?c:null;if(d!==null)return d}catch(g){}try{var e=qt[b];if(e&&(d=a.g[e],d!==void 0))return d}catch(g){}try{var f=st[b];if(f)return Lc(a.g,f)}catch(g){}return null}function ut(a){return R(function(b){return tt(b,a)})};var vt=M(function(a){return R(function(b){return a.map(function(c){return tt(b,c)})})}(["data-google-av-cxn","data-google-av-turtlex"]),R(function(a){var b=z(a);a=b.next().value;b=b.next().value;if(!a){if(b!==null)return[];throw new kr;}return a.split("|")}));function wt(){return M(Op(function(a){return a.element.i(vt,Cq(function(){return Q([""])})).i(R(function(b){return{ra:b,Mc:a}}))}),Lq(function(a){return a.ra.sort().join(";")}),R(function(a){return a.Mc}))};function Z(a){this.value=a}function xt(a,b){return Q(a.value).i(Y(b,1))}var yt=new Z(!1);function zt(){return Op(function(a){return cp(At(a)).i(Bs(a.u))})}function At(a){return a.document.querySelectorAll(".GoogleActiveViewElement,.GoogleActiveViewClass").map(function(b){return new ms(b)})};function Bt(a){var b=a.Xe,c=a.document.h;return eq(Q({}),c,b).i(R(function(){return a}))};function Ct(){return M(T(function(a){return a!==void 0}),R(function(a){return a}))};function Dt(){return function(a){var b=[];return a.i(T(function(c){if(c.g===void 0||b.some(function(d){return d.g===c.g}))return!1;b.push(c);return!0}))}};function Et(a,b){b=b===void 0?So:b;return eq(Bt(a),b).i(zt(),Dt(),Ct(),Y(a.u,1))};function Ft(a){a=a.global;if(typeof a.__google_lidar_==="undefined")return a.__google_lidar_=1,!1;a.__google_lidar_=Number(a.__google_lidar_)+1;var b=a.__google_lidar_adblocks_count_;if(typeof b==="number"&&b>0&&(a=a.__google_lidar_radf_,typeof a==="function"))try{a()}catch(c){}return!0}function Gt(a){var b=a.global;b.osdlfm=function(){return b.__google_lidar_radf_};if(b.__google_lidar_radf_!==void 0)return So;b.__google_lidar_adblocks_count_=1;var c=new O;b.__google_lidar_radf_=function(){return void c.next(a)};return c.i(Qr(a.K,743))};var Ht={considerOmidZOrderOcclusions:[Vm,!1],extraPings:[Wm,0],extrapolators:[Xm,!1],rxlidarStatefulBeacons:[bn,!1],shouldIgnoreAdChoicesIcon:[dn,!1],dedicatedViewableAttributionPing:[new Tm("45389692"),0],useReachIntegrationPolyfill:[fn,!1],useReachIntegrationSharedStorage:[gn,!0],sendBrowserIdInsteadOfVPID:[cn,!1],waitForImpressionColleague:[hn,!1],fetchLaterBeacons:[Ym,!1],rxInNonrx:[an,!1],addQueryIdToErrorPing:[Um,!1],shouldSendExplicitDisplayMeasurablePing:[en,!1],reachUseCreateWorklet:[$m,!1]};function It(a){return Object.entries(Ht).reduce(function(b,c){var d=z(c);c=d.next().value;var e=z(d.next().value);d=e.next().value;e=e.next().value;var f;b[c]=(f=a==null?void 0:hj(a,d))!=null?f:e;return b},{})};function Ig(a){this.C=jg(a)}y(Ig,$h);function Jt(a){return Uf(sg(a,3))}function Kt(a){return Cf(sg(a,5))};var Lt=[0,Ui,-2,Ti,-1,Pi];function Mt(a){this.C=jg(a)}y(Mt,$h);var Nt=bj(Mt);function Ot(a){this.C=jg(a)}y(Ot,$h);var Pt=[0,Vi,-4,Xi,Ti,Pi,Ki,Vi,Ki,Vi,Pi,Vi,-1,[0,Pi,-3],Wi,Oi,Vi,Ni,-1,Pi,-1,Ni,Ki,[0,Ni,Pi,-1,Xi,Ki,Ni],Ii,Vi];var Qt=[0,Ui,-1,Ti,Pt,Qi,-1,Yi,Pi,Ti,Pi];function Rt(a){this.C=jg(a)}y(Rt,$h);var St=[0,Ui,Pi];function Tt(a){this.C=jg(a)}y(Tt,$h);function Ut(a){return Jg(a,Ot,1)};var Vt=$i(Tt,[0,Qt,Lt,St]);function Wt(a){this.h=a===void 0?!1:a;this.xe=new Map}Wt.prototype.start=function(a,b,c,d){var e=this;if(this.bd===void 0&&a.U){var f=a.U;this.g=d;c=!this.h&&Ft(f);d=this.h?So:Gt(f);d=Et(f,d);this.bd=(c?So:d.i(R(function(g){var h=h===void 0?Symbol():h;return Object.freeze({Db:h,element:xt(new Z(g),f.u)})}),wt())).subscribe(function(g){var h=g.Db;e.xe.set(h,g);g.element.i(Jq(1)).subscribe(function(k){var l=tt(k,"data-google-av-flags"),m=new gj;l!==null&&ij(m,l);l=It(m);k=tt(k,"data-google-av-ufs-integrator-metadata");a:{if(k!==null)try{var q=Vt(k);break a}catch(t){}q=new Tt}b(h,q,l)})});c&&this.dispose();a.ha&&Nr(a.ha)}};Wt.prototype.dispose=function(){var a,b;(a=this.bd)==null||(b=a.unsubscribe)==null||b.call(a);this.bd=void 0;var c;(c=this.g)==null||c.call(this);this.g=void 0};function Xt(a){var b=bt(a);return b===null?new Z(null):b.i(R(function(c){c=ai(c);if(je)c=Ma.btoa(c);else{for(var d=[],e=0,f=0;f<c.length;f++){var g=c.charCodeAt(f);g>255&&(d[e++]=g&255,g>>=8);d[e++]=g}c=ke(d)}return c}),Jq(1),Y(a.u,1))};function Yt(a){var b,c,d;return!!a&&typeof a.active==="boolean"&&typeof((b=a.clock)==null?void 0:b.now)==="function"&&((c=a.clock)==null?void 0:c.timeline)!==void 0&&!((d=a.J)==null||!d.timestamp)&&typeof a.ba==="function"&&typeof a.pa==="function"&&typeof a.va==="function"&&typeof a.map==="function"&&typeof a.xa==="function"};function Zt(a){function b(c){return typeof c==="boolean"||typeof c==="string"||typeof c==="number"||c===void 0||c===null}return b(a)?!0:Array.isArray(a)?a.every(b):typeof a==="object"?Object.keys(a).every(function(c){return typeof c==="string"})&&Object.values(a).every(function(c){return Array.isArray(c)?c.every(b):b(c)}):!1}function $t(a){if(Zt(a))return a;if(Yt(a))return{J:{value:$t(a.J.value),timestamp:sr(a.J.timestamp,new qr(0,a.J.timestamp.timeline))},active:a.active};try{return JSON.parse(JSON.stringify(a))}catch(b){}return String(a)};function au(a,b){return new N(function(c){function d(q){l.add(q);vo(a,function(){l.size===b.length&&c.complete()},1)}function e(q,t){l.add(t);k.add(t);vo(a,function(){c.error(q)},1)}function f(q,t){a.Cg?(h[t]=q,k.add(t),g||(g=!0,vo(a,function(){g=!1;c.next(eb(h))},1))):c.error(new lr(t))}var g=!1,h=Array(b.length);h.fill(void 0);var k=new Set,l=new Set,m=b.map(function(q,t){return q.subscribe(function(r){return void f(r,t)},function(r){return void e(r,t)},function(){return void d(t)})});return function(){m.forEach(function(q){return void q.unsubscribe()})}})};function bu(a,b,c){function d(){if(b.Ga){var v=b.Ga,B=v.next;var P={creativeId:zo(b.oc,c),requiredSignals:e,signals:Object.assign({},f),hasPrematurelyCompleted:g,errorMessage:h,erroredSignalKey:k};P={specMajor:2,specMinor:0,specPatch:0,timestamp:sr(b.B.now(),new qr(0,b.B.timeline)),instanceId:zo(b.oc,b.Db),creativeState:P};B.call(v,P)}}for(var e=Object.keys(a),f={},g=!1,h=null,k=null,l={},m=new Set,q=[],t=[],r=z(e),w=r.next(),u={};!w.done;u={qa:void 0},w=r.next())u.qa=w.value,w=a[u.qa],w instanceof Z?(l[u.qa]=w.value,m.add(u.qa),b.Ga&&(f[String(u.qa)]=$t(w.value))):(w=w.i(U(function(v,B){return Yt(v)||Yt(B)?!1:v===B}),R(function(v){return function(B){b.Ga&&(f[String(v.qa)]=$t(B),d());var P={};return P[v.qa]=B,P}}(u)),Cq(function(v){return function(B){if(B instanceof lr)throw new mr(String(v.qa));throw B;}}(u)),Zq(function(v){return function(){m.add(v.qa)}}(u),function(v){return function(B){k=String(v.qa);h=String(B);d()}}(u),function(v){return function(){m.has(v.qa)||(g=!0,d())}}(u))),t.push(u.qa),q.push(w));(a=Object.keys(f).length>0)&&d();r=au(b.u,q).i(Cq(function(v){if(v instanceof lr)throw new nr(String(t[v.g]));throw v;}),R(function(v){return Object.freeze(Object.assign.apply(Object,[{},l].concat(A(v))))}));return(q=q.length>0)&&a?eq(Q(Object.freeze(l)),r):q?r:Q(Object.freeze(l))};function cu(a,b,c,d){var e=du(eu(fu(),gu),hu,iu);return a.K.Xb.bind(a.K)(733,function(){var f={};try{return b.i(Cq(function(g){d(Object.assign({},f,{error:g}));return So}),Op(function(g){try{var h=c(a,g)}catch(l){return d(Object.assign({},f,{error:l instanceof Error?l:String(l)})),So}var k={};return bu(h,a,g.Db).i(Zq(function(l){k=l}),Uq(1),xp()).i(e,Cq(function(l){d(Object.assign({},k,{error:l}));return So}),Pq(void 0),R(function(){return!0}))})).i(Wq(function(g){return g+1},0),Cq(function(g){d(Object.assign({},f,{error:g}));return So}))}catch(g){return d(Object.assign({},f,{error:g})),So}})()};function ju(a,b){return M(X(function(c){var d=a(c),e=b(c),f={};return d&&e&&f?new N(function(g){e(d,f,function(h){g.next(Object.assign({},c,{nb:h}));g.complete()});return function(){}}):fq}),T(function(c){return c.nb}))};var hu=M(T(function(a){var b=a.L;var c=a.jc;var d=a.Yb;var e=a.Tb;var f=a.xb;var g=a.Wa;a=a.hc;return g!==void 0&&a!==void 0&&b!==void 0&&c!==void 0&&d!==void 0&&(!f||e!==void 0)}),Yq(function(a){return!(a.Qe===!1&&a.De!==void 0)},!1),T(function(a){var b=a.Qe;var c=a.Vc;var d=a.Pg;a=a.jc;return d?!!c&&a!==void 0&&(a==null?void 0:a.length)>0:!!b}),ju(function(a){return a.hc},function(a){return a.Wa}),R(function(a){a.xb||a.Yb(a.jc,a).forEach(function(b){a.L.M(b).sendNow()})}),Jq(1),Hq());function ku(a){var b=new Map;if(typeof a!=="object"||a===null)return b;Object.values(a).forEach(function(c){c&&typeof c.pa==="function"&&(b.has(c.clock.timeline)||b.set(c.clock.timeline,c.clock.now()))});return b};function lu(a,b,c){var d=mu,e=nu;c=c===void 0?.01:c;return function(f){c>0&&Math.random()<=c&&(a.global.HTMLFencedFrameElement&&a.global.fence&&typeof a.global.fence.reportEvent==="function"&&a.global.fence.reportEvent({eventType:"active-view-error",eventData:"",destination:["buyer"]}),f=Object.assign({},f,{errorMessage:f.error instanceof Error&&f.error.message?f.error.message:String(f.error),Ee:f.error instanceof Error&&f.error.stack?String(f.error.stack):null,Qf:f.error instanceof Error&&f.error.name?String(f.error.name):null,Of:String(a.K.ff),Pf:f.escapedQueryId}),d(Object.assign({},f,{Z:function(){return function(g){try{return e(Object.assign({},g))}catch(h){return{}}}}(),ra:[b]}),ku(f)).forEach(function(g){a.L.M(g).sendNow()}))}};var iu=M(R(function(a){var b=a.L;var c=a.Sf;if(b===void 0||c===void 0)return!1;if(a.De!==void 0)return!0;if(c===null)return!1;for(a=0;a<c;a++)b.M("https://pagead2.googlesyndication.com/pagead/gen_204?id=av-js&type=extra&rnd="+Math.floor(Math.random()*1E7)).sendNow();return!0}),Yq(function(a){return!a}),Hq());var ou=M(T(function(a){return!!a.Vc}),T(function(a){var b=a.shouldSendExplicitDisplayMeasurablePing;a=a.tb;var c,d;return(d=b&&((c=a==null?void 0:a.length)!=null?c:0)>0)!=null?d:!1}),T(function(a){return a.Z!==void 0&&a.tb!==void 0&&a.Eb!==void 0&&a.Pb!==void 0&&a.L!==void 0}),R(function(a){return Object.assign({},a,{dd:ku(a)})}),R(function(a){a.Eb(Object.assign({},a,{ra:a.tb,Z:a.Z,xc:a.Pb,Cc:3,yc:"m"}),a.dd).forEach(function(b){a.L.M(b).sendNow()});return!0}),Yq(function(a){return!a}),Hq());function nu(a){return{id:a.xc,mcvt:a.uc,p:a.Nc,asp:a.qh,tm:a.hb,tu:a.ib,mtos:a.vc,tos:a.Ma,v:a.Bf,bin:a.Aa,avms:a.N,bs:a.te,mc:a.Ue,"if":a.Kf,vu:a.Mf,app:a.ub,mse:a.Sd,mtop:a.Td,itpl:a.Jd,adk:a.td,exk:a.zf,rs:a.T,la:a.ca,cr:a.Kd,uach:a.Bc,vs:a.Cc,r:a.yc,pay:a.Xf,co:a.Df,rst:a.xf,rpt:a.wf,isd:a.cg,lsd:a.ng,context:a.Of,msg:a.errorMessage,stack:a.Ee,name:a.Qf,ec:a.Yf,sfr:a.be,met:a.kc,wmsd:a.he,pv:a.Nh,epv:a.th,pbe:a.Pe,fle:a.ag,vae:a.bg,spb:a.cf,sfl:a.bf,ffslot:a.hg,reach:a.Eg,io2:a.fd,rxdbg:a.Sh,omida:a.Bh,omidp:a.Ih,omidpv:a.Jh,omidor:a.Hh,omidv:a.Lh,omids:a.Kh,omidam:a.Ah,omidct:a.Ch,omidia:a.Fh,omiddc:a.Dh,omidlat:a.Gh,omiddit:a.Eh,qid:a.Pf}};function du(){var a=C.apply(0,arguments);return function(b){var c=b.i(Uq(1),xp());b=a.map(function(d){return c.i(d,Pq(!0))});return S(b).i(Jq(1),Hq())}};function eu(){var a=C.apply(0,arguments);return function(b){var c=b.i(Uq(1),xp());b=a.map(function(d){return c.i(d,Pq(!0))});return eq.apply(null,A(b)).i(Jq(1),Hq())}};function fu(){var a=du(ou,pu),b=qu;return function(c){var d=c.i(Uq(1),xp());c=d.i(a,Pq(!0));d=d.i(M(b,Uq(),xp()),Pq(!0));c=S([c,d]);return jq(c,d).i(Jq(1),Hq())}};function qu(a){var b=[];return a.i(R(function(c){var d=c.L,e=c.Tf,f=c.Ma,g=c.Ig,h=c.Z,k=c.Hg,l=c.df,m=c.Eb,q=c.ge,t=c.Vc,r=c.Pe,w=c.cf,u=c.bf,v=c.de;if(!c.Le||!t||c.vc===void 0||f===void 0||g===void 0||h===void 0||k===void 0||m===void 0||d===void 0)return!1;if(c.xb){if(l===void 0)return!1;g=c.Tb;if(!g)return!1;g({eventType:"active-view-time-on-screen",eventData:v!=null?v:"",destination:["buyer"]});return!0}if(!(r||u||l))return!1;v=ku(c);var B;q=(B=q==null?void 0:q.wa(v).value)!=null?B:!1;B=m(Object.assign({},c,{xc:k,Cc:q?4:3,yc:l!=null?l:"u",Z:h,ra:g}),v);if(r){for(;b.length>g.length;)c=void 0,(c=b.shift())==null||c.deactivate();B.forEach(function(I,ca){ca>=b.length?b.push(d.M(I)):b[ca].url=I});return w&&e&&l!==void 0?(B.forEach(function(I){e.M(I).sendNow()}),!0):l!==void 0}if(w&&e&&l!==void 0)return B.forEach(function(I){e.M(I).sendNow()}),!0;if(u&&e){for(;b.length>g.length;)w=void 0,(w=b.shift())==null||w.deactivate();var P=m(Object.assign({},c,{xc:k,Cc:q?4:3,yc:l!=null?l:"u",Z:h,ra:["https://pagead2.googlesyndication.com/pagead/gen_204?id=av-js&type=fetch&later&lidartos"]}),v)[0];B.forEach(function(I,ca){ca>=b.length?b.push(d.M(P,{Ce:!0})):b[ca].url=P});return l!==void 0?(B.forEach(function(I){e.M(I).sendNow()}),!0):l!==void 0}return l!==void 0?(B.forEach(function(I){d.M(I).sendNow()}),!0):!1}),Yq(function(c){return!c}),Hq())};function ru(){return function(a){return a.i(R(function(b){return b}))}};function su(a){return function(b){return new N(function(c){var d=!1,e=b.i(ru()).subscribe(function(f){d=!0;c.next(f)},c.error.bind(c),c.complete.bind(c));vo(a,function(){d||c.next(null)},3);return e})}};function tu(a,b){return function(c){return c.i(X(function(d){return new N(function(e){function f(){h.disconnect();k.unsubscribe()}var g=a.MutationObserver;if(g&&d.g!==void 0){var h=new g(function(l){e.next(l)});h.observe(d.g,b);var k=d.h.subscribe(f);return f}})}))}};var uu={mh:0,Xg:1,ah:2,Yg:3,0:"UNKNOWN",1:"DEFER_MEASUREMENT",2:"DO_NOT_DEFER_MEASUREMENT",3:"DEFER_MEASUREMENT_AND_PING"};function vu(a,b){var c=b.i(tu(a,{attributes:!0}),Y(a.u,1));return S([b,c.i(Y(a.u,1),su(a.u))]).i(R(function(d){return z(d).next().value}),ut("data-google-av-dm"),R(wu))}function wu(a){return a&&a in uu?Number(a):2};function xu(a){if(a.qg===3)return null;if(a.df!==void 0){var b=a.Gf===!1?"n":null;if(b!==null)return b}return a.Rc instanceof er?"msf":a.zd instanceof fr?"c":a.Ff===!1?"pv":a.Rc||a.zd?"x":null}var gu=M(T(function(a){return a.tb!==void 0&&a.Z!==void 0&&a.Eb!==void 0&&a.Pb!==void 0&&a.L!==void 0}),T(function(a){return xu(a)!==null}),ju(function(a){return a.Ic},function(a){return a.Wa}),R(function(a){if(a.xb){var b=a.Tb;if(b){var c;b({eventType:"active-view-unmeasurable",eventData:(c=a.de)!=null?c:"",destination:["buyer"]})}}else{c=void 0;var d=xu(a);if(d==="x"){var e,f=(e=a.Rc)!=null?e:a.zd;f&&(b=f.stack,c=f.message)}a.Eb(Object.assign({},a,{ra:a.tb,Z:a.Z,xc:a.Pb,Cc:2,yc:d,errorMessage:c,Ee:b}),ku(a)).forEach(function(g){a.L.M(g).sendNow()})}}),Jq(1),Hq());function yu(){this.startTime=Math.floor(Date.now()/1E3-1704067200);this.g=0}function zu(a){var b=a.g.toString(10).padStart(2,"0");b=""+a.startTime+b;a.g<99&&a.g++;return b};function Au(a,b){return typeof a==="string"?encodeURIComponent(a):typeof a==="number"?String(a):Array.isArray(a)?a.map(function(c){return Au(c,b)}).join(","):a instanceof qr?a.toString():a&&typeof a.pa==="function"?Au(a.wa(b).value,b):a===!0?"1":a===!1?"0":a===void 0||a===null?null:a instanceof yu?zu(a):[a.top,a.left,a.top+a.height,a.left+a.width].join()}function Bu(a,b){a=Object.entries(a).map(function(c){var d=z(c);c=d.next().value;d=d.next().value;d=Au(d,b);return d===null?"":c+"="+d}).filter(function(c){return c!==""});return a.length?a.join("&"):""};function Cu(a,b){var c=Object.assign({},a),d=a.Bc;c=(delete c.Bc,c);c=a.Z(c);var e=Bu(c,b);return Xa(a.ra,function(f){var g="";typeof d==="string"&&(g="&"+Bu({uach:d},b));var h={};return jm(f,(h.VIEWABILITY=e,h))+g})};function mu(a,b){var c=a.Z(a),d=Bu(c,b);return d?Xa(a.ra,function(e){e=e.indexOf("?")>=0?e:e+"?";e="?&".indexOf(e.slice(-1))>=0?e:e+"&";return e+d}):a.ra};function Du(a,b){return Xa(a,function(c){if(typeof b.Bc==="string"){var d="&"+Bu({uach:b.Bc},new Map);return c.substring(c.length-7)=="&adurl="?c.substring(0,c.length-7)+d+"&adurl=":c+d}return c})};var pu=M(T(function(a){return a.Z!==void 0&&a.tb!==void 0&&a.Eb!==void 0&&a.Pb!==void 0&&a.L!==void 0}),R(function(a){return Object.assign({},a,{dd:ku(a)})}),T(function(a){var b=a.ge;var c=a.Vc;a=a.dd;var d;return!!c&&((d=b==null?void 0:b.wa(a).value)!=null?d:!1)}),ju(function(a){return a.Jc},function(a){return a.Wa}),R(function(a){var b=a.L,c=a.de;if(a.xb){var d=a.Tb;if(!d)return!1;d({eventType:"active-view-viewable",eventData:c!=null?c:"",destination:["buyer"]});return!0}c=a.Eb(Object.assign({},a,{ra:a.tb,Z:a.Z,xc:a.Pb,Cc:4,yc:"v"}),a.dd);(d=a.Ad)&&d.length>0&&a.Yb&&a.Yb(d,a).forEach(function(e){b.M(e).sendNow()});(d=a.jb)&&d.length>0&&a.Yb&&a.Yb(d,a).forEach(function(e){b.M(e).sendNow()});c.forEach(function(e){b.M(e,{fc:a.Md}).sendNow()});return!0}),Yq(function(a){return!a}),Hq());function Eu(a,b,c,d){var e=Object.keys(c).map(function(h){return h}),f=e.filter(function(h){var k=c[h];h=d[h];return k instanceof Z&&h instanceof Z&&k.value===h.value}),g=f.reduce(function(h,k){var l={};return Object.assign({},h,(l[k]=c[k],l))},{});return e.reduce(function(h,k){if(f.indexOf(k)>=0)return h;var l={};return Object.assign({},h,(l[k]=b.i(X(function(m){return(m=m?c[k]:d[k])&&(m instanceof N||L(m.zb)&&L(m.subscribe))?m:xt(m,a)})),l))},g)};function Fu(a){return M(R(function(){return!0}),V(!1),Y(a,1))};var Gu={Rg:"asmreq",Sg:"asmres"};function Hu(a){this.C=jg(a)}y(Hu,$h);function Iu(a){this.C=jg(a)}y(Iu,$h);function Ju(a){this.C=jg(a)}y(Ju,$h);var Ku=bj(Ju);function Lu(a,b){var c=c===void 0?Zs(a):c;var d=new MessageChannel;b=b.i(R(function(f){return Number(f)}),T(function(f){return!isNaN(f)&&f!==0}),Zq(function(f){var g=new Hu;Sg(g,1,f);f={type:"asmreq",payload:ai(g)};c.postMessage(f,"*",[d.port2])}),Jq(1));var e=$s(a,d.port1).i(T(function(f){return typeof f.data==="object"}),R(function(f){var g=f.data,h=Object.values(Gu).includes(g.type);g=typeof g.payload==="string";if(!h||!g||f.data.type!=="asmres")return null;try{return Ku(f.data.payload)}catch(k){return null}}),T(function(f){return f!==null}),R(function(f){return f}));return b.i(X(function(f){return Q(f).i(Fq(e))}),T(function(f){var g=z(f);f=g.next().value;g=g.next().value;if(Jf(sg(g,1))!=null){var h=h===void 0?0:h;var k;g=((k=Jf(sg(g,1)))!=null?k:h)===f}else g=!1;return g}),R(function(f){f=z(f);f.next();return f.next().value}),Bs(a.u))};function Mu(a,b,c){var d=b.tc.i(Jq(1),X(function(){return Lu(a,c)}),T(function(f){return Og(f,2)&&wg(f,Iu,3)&&If(sg(f,4))!=null&&If(sg(f,5))!=null}),Jq(1),Bs(a.u));b=d.i(R(function(f){var g=Jg(f,Iu,3);g=Pg(g,2);f=Jg(f,Iu,3);f=Pg(f,1);return{x:g,y:f}}),U(function(f,g){return f.x===g.x&&f.y===g.y}),Y(a.u,1));var e=d.i(R(function(f){return Pg(f,4)}),Y(a.u,1));d=d.i(R(function(f){return Pg(f,5)}),Y(a.u,1));return{cg:e,yf:b,ng:d}};function Nu(a,b){return b.tc.i(Jq(1),R(function(){return a.B.now().round()}))};function Ou(a,b){var c=a.Fd().i(R(function(){return"b"}));return jq(b,c).i(Jq(1),Y(a.u,1))};function Pu(a,b){this.a=a;this.b=b;if(a.clock.timeline!==b.clock.timeline)throw Error();}Pu.prototype.ba=function(a){return a instanceof Pu?this.a.ba(a.a)&&this.b.ba(a.b):!1};Pu.prototype.va=function(a){var b=this.a.va(a).value,c=this.b.va(a).value;return{timestamp:a,value:[b,c]}};ea.Object.defineProperties(Pu.prototype,{active:{configurable:!0,enumerable:!0,get:function(){return this.a.active||this.b.active}},clock:{configurable:!0,enumerable:!0,get:function(){return this.a.clock}},J:{configurable:!0,enumerable:!0,get:function(){var a=this.a.J.timestamp.maximum(this.b.J.timestamp),b=this.a.J.timestamp.equals(a)?this.a.J.value:this.a.va(a).value,c=this.b.J.timestamp.equals(a)?this.b.J.value:this.b.va(a).value;return{timestamp:a,value:[b,c]}}}});function Qu(a,b){this.input=a;this.g=b;this.J={timestamp:this.input.J.timestamp,value:this.g(this.input.J.value)}}Qu.prototype.ba=function(a){return a instanceof Qu?this.input.ba(a.input)&&this.g===a.g:!1};Qu.prototype.va=function(a){a=this.input.va(a);return{timestamp:a.timestamp,value:this.g(a.value)}};ea.Object.defineProperties(Qu.prototype,{active:{configurable:!0,enumerable:!0,get:function(){return this.input.active}},clock:{configurable:!0,enumerable:!0,get:function(){return this.input.clock}}});function Ru(a,b,c){c=c===void 0?function(d,e){return d===e}:c;return a.timestamp.equals(b.timestamp)&&c(a.value,b.value)};function Su(a,b,c){this.clock=a;this.J=b;this.active=c}Su.prototype.ba=function(a){return a instanceof Su?this.active===a.active&&this.clock.timeline===a.clock.timeline&&Ru(this.J,a.J):!1};Su.prototype.va=function(a){return{timestamp:a,value:this.J.value+(this.active?Math.max(0,sr(a,this.J.timestamp)):0)}};function Tu(){}Tu.prototype.pa=function(){return this.va(this.clock.now())};Tu.prototype.wa=function(a){var b=this.clock.timeline,c,d=(c=a.get(b))!=null?c:this.clock.now();a.set(b,d);return this.va(d)};Tu.prototype.map=function(a){return new Uu(this,a)};Tu.prototype.xa=function(a){return new Vu(this,a)};function Vu(){Pu.apply(this,arguments);this.map=Tu.prototype.map;this.xa=Tu.prototype.xa;this.pa=Tu.prototype.pa;this.wa=Tu.prototype.wa}y(Vu,Pu);function Wu(){Su.apply(this,arguments);this.map=Tu.prototype.map;this.xa=Tu.prototype.xa;this.pa=Tu.prototype.pa;this.wa=Tu.prototype.wa}y(Wu,Su);function Uu(){Qu.apply(this,arguments);this.map=Tu.prototype.map;this.xa=Tu.prototype.xa;this.pa=Tu.prototype.pa;this.wa=Tu.prototype.wa}y(Uu,Qu);function Xu(a,b){this.J=b;this.pa=Tu.prototype.pa;this.wa=Tu.prototype.wa;this.map=Tu.prototype.map;this.xa=Tu.prototype.xa;this.clock=a}Xu.prototype.ba=function(a){return a.active};Xu.prototype.va=function(){return this.J};ea.Object.defineProperties(Xu.prototype,{active:{configurable:!0,enumerable:!0,get:function(){return!1}}});function Yu(a,b){return b.i(R(function(c){return new Xu(a.B,{timestamp:a.B.now(),value:c})}))};var Zu=R(function(a){return[a.value.aa.width,a.value.aa.height]});var $u={ia:"ns",oa:Ds,aa:Ds,fa:new O,X:"ns",F:Ds,Y:Ds,sa:{x:0,y:0}};function av(a,b){return Es(a.aa,b.aa)&&Es(a.F,b.F)&&Es(a.oa,b.oa)&&Es(a.Y,b.Y)&&a.X===b.X&&a.fa===b.fa&&a.ia===b.ia&&a.sa.x===b.sa.x&&a.sa.y===b.sa.y};function bv(a){return function(b){var c;return b.i(Zq(function(d){return void(c=d.timestamp)}),R(function(d){return d.value}),a,R(function(d){return{timestamp:c,value:d}}))}};function cv(a){return a.Y.width*a.Y.height/(a.F.width*a.F.height)}var dv=bv(M(R(function(a){var b;return(b=a.Qc)!=null?b:cv(a)}),R(function(a){return isFinite(a)?a:0}))),ev=bv(M(R(function(a){var b;return(b=a.Qc)!=null?b:cv(a)}),R(function(a){return isFinite(a)?a:-1})));function fv(a,b){return a>=1?!0:a<=0?!1:a>=b};function gv(a){return function(b){return b.i($q(a),R(function(c){var d=z(c);c=d.next().value;d=d.next().value;return{timestamp:c.timestamp,value:fv(c.value,d)}}))}};var hv=R(function(a){if(a.value.ia==="omid"){if(a.value.X==="nio")return"omio";if(a.value.X==="geo")return"omgeo"}return a.value.X==="geo"||a.value.X==="nio"?a.value.ia:a.value.X});function iv(){return M(T(function(a,b){return b>0}),jv,V(-1),U())}var jv=M(T(function(a){return!isNaN(a)}),Wq(function(a,b){return isNaN(a)?b:Math.min(a,b)},NaN),U());var kv=bv(M(R(function(a){return a.Y.width*a.Y.height/(a.oa.width*a.oa.height)}),R(function(a){return isFinite(a)?Math.min(1,a):0})));function lv(a,b,c){return a?S([b,c]).i(T(function(d){var e=z(d);d=e.next().value;e=e.next().value;return d.timestamp.equals(e.timestamp)}),R(function(d){var e=z(d);d=e.next().value;e=e.next().value;return d.value>e.value?d:e})):b}function mv(a){return function(b){var c=b.i(dv),d=b.i(kv);return a instanceof N?a.i(X(function(e){return lv(e,c,d)})):lv(a.value,c,d)}};var nv=M(bv(R(function(a){a=a.Qc?a.F.width*a.F.height*a.Qc/(a.aa.width*a.aa.height):a.Y.width*a.Y.height/(a.aa.width*a.aa.height);return isFinite(a)?a:0})));function ov(a,b,c,d){var e=d.Sc,f=d.Cd,g=d.jf,h=d.re,k=d.Nd,l=d.Ve,m=d.Wc;d=d.gf;b=pv(a,c,b);c=qv(a,c);d=rv(b,d);var q=sv(a,e,l,b),t=q.i(R(function(x){return x.value}),U(),Y(a,1),Wq(function(x,W){return Math.max(x,W)},0)),r=q.i(R(function(x){return x.value}),iv(),Y(a,1)),w=b.i(ev,R(function(x){return x.value}),Jq(2),U(),Y(a,1));g=tv(a,b,g,h);var u=g.i(V(!1),U(),R(function(x){return x?k:f}));h=q.i(gv(u),U(),Y(a,1));var v=S([h,b]).i(T(function(x){var W=z(x);x=W.next().value;W=W.next().value;return x.timestamp.equals(W.timestamp)}),R(function(x){var W=z(x);x=W.next().value;W=W.next().value;return{visible:x.value,geometry:W.value.F}}),Wq(function(x,W){return!W.visible&&x.visible?x:W},{visible:!1,geometry:Ds}),R(function(x){return x.geometry}),V(Ds),Y(a,1),U(Es));l=l instanceof N?l.i(U(),Iq()):fq;u=S([l,u]).i(Iq());var B=b.i(T(function(x){return x.value.ia!=="ns"&&x.value.X!=="ns"}),Wq(function(x){return x+1},0),V(0),Y(a,1)),P=c.i(Iq(!0),V(!1),Y(a,1));P=S([m,P]).i(R(function(x){var W=z(x);x=W.next().value;W=W.next().value;return x&&!W}),Y(a,1));var I=b.i(nv,U()),ca=I.i(R(function(x){return x.value}),Wq(function(x,W){return Math.max(x,W)},0),U(),Y(a,1)),E=I.i(R(function(x){return x.value}),iv(),Y(a,1));return{Yd:l,Ac:u,Ba:{xg:b,N:b.i(hv),Nc:v.i(U(Es)),visible:h.i(U(Ru)),ce:q.i(U(Ru)),Ue:t,tg:r,te:b.i(Zu,U(ib)),Jg:I,pg:ca,sg:E,Rc:c,fa:xt(new Z(new O),a),ca:g,Sc:e,Wc:m,Le:P,Lg:B,Sb:w,fd:d}}}function qv(a,b){return b.i(T(function(){return!1}),R(function(c){return c}),Cq(function(c){return xt(new Z(c),a)}))}function rv(a,b){a=S([a,b]).i(R(function(e){var f=z(e);e=f.next().value;if(f.next().value&&e.value.isIntersecting)return e.value.Te}),U());var c=a.i(R(function(e){return e===void 0?!0:e}),Wq(function(e,f){return e||!f},!1)),d=a.i(Wq(function(e,f){return f===void 0?e:f?!1:e!=null?e:!0},void 0),R(function(e){return!!e}));return S([b,lq(a,c,d)]).i(R(function(e){var f=z(e);e=f.next().value;var g=z(f.next().value);f=g.next().value;var h=g.next().value;g=g.next().value;var k=0;if(!e)return 0;if(f===void 0)return 16;f&&(k|=1);f||(k|=2);h&&(k|=4);g&&(k|=8);return k}))}function pv(a,b,c){return b.i(iq(fq),Y(a,1)).i(U(function(d,e){return Ru(d,e,av)}),V({timestamp:c.now(),value:$u}),Y(a,1))}function sv(a,b,c,d){c=d.i(mv(c),bv(R(function(e){return Math.round(e*100)/100})),Y(a,1));return b instanceof Z?c:S([c,b]).i(R(function(e){var f=z(e);e=f.next().value;f=f.next().value;return{timestamp:f.timestamp.maximum(e.timestamp),value:f.value?0:e.value}}),U(Ru),Y(a,10))}function tv(a,b,c,d){b=[b.i(R(function(e){return e.value.F.width*e.value.F.height>=242500}))];c instanceof N&&b.push(c.i(R(function(e){return!!e})));c=S(b);return d?c.i(R(function(e){return e.some(function(f){return f})}),V(!1),U(),Y(a,1)):xt(new Z(!1),a)};function uv(a){return a.length<=0?So:S(a.map(function(b){var c=0;return b.i(R(function(d){return{index:c++,value:d}}))})).i(T(function(b){return b.every(function(c){return c.index===b[0].index})}),R(function(b){return b.map(function(c){return c.value})}))};function vv(a,b){return function(c){return uv(b.map(function(d){return c.i(a(d))}))}};function wv(a,b){a.Fa&&(a.Bb=a.Fa);a.Fa=b;a.Bb&&a.Bb.value?(b=Math.max(0,sr(b.timestamp,a.Bb.timestamp)),a.totalTime+=b,a.ua+=b):a.ua=0;return a}function xv(a){return M(Wq(wv,{totalTime:0,ua:0}),R(function(b){return new Wu(a,{timestamp:b.Fa.timestamp,value:b.totalTime},b.Fa.value)}))}function yv(a){return M(Wq(wv,{totalTime:0,ua:0}),R(function(b){return new Wu(a,{timestamp:b.Fa.timestamp,value:b.ua},b.Fa.value)}))};function zv(a){return M(yv(a),R(function(b){return b.map(function(c){return Math.round(c)})}))};function Av(a){var b=new Wu(a,{timestamp:a.now(),value:0},!1);return M(yv(a),Wq(function(c,d){return c.J.value>d.J.value?new Wu(a,c.J,!1):d},b),R(function(c){return c.map(function(d){return Math.round(d)})}))};function Bv(a){return function(b){return M(gv(Q(b)),Av(a))}};function Cv(a){return function(b){return M(bv(R(function(c){return fv(c,b)})),xv(a),R(function(c){return c.map(function(d){return Math.round(d)})}))}};function Dv(a){return a.map(function(b){return b.map(function(c){return[c]})}).reduce(function(b,c){return b.xa(c).map(function(d){return d.flat()})})}function Ev(a,b){return a.xa(b).map(function(c){var d=z(c);c=d.next().value;d=d.next().value;return c-d})}function Fv(a,b,c,d,e,f){var g=Gv;if(g.length>1)for(var h=0;h<g.length-1;h++)if(g[h]<g[h+1])throw Error();h=f.i(V(void 0),X(function(){return d.i(zv(a))}),U(function(k,l){return k.ba(l)}),Y(b,1));f=f.i(V(void 0),X(function(){return d.i(Av(a))}),U(function(k,l){return k.ba(l)}),Y(b,1));return{hb:e.i(V(void 0),X(function(){return c.i(R(function(k){return{timestamp:k.timestamp,value:!0}}),xv(a))}),U(function(k,l){return k.ba(l)}),Y(b,1)),ib:e.i(V(void 0),X(function(){return c.i(R(function(k){return{timestamp:k.timestamp,value:k.value===0}}),xv(a))}),U(function(k,l){return k.ba(l)}),Y(b,1)),vc:e.i(V(void 0),X(function(){return c.i(vv(Bv(a),g))}),R(Dv),U(function(k,l){return k.ba(l)}),Y(b,1)),Ma:e.i(V(void 0),X(function(){return c.i(vv(Cv(a),g),R(function(k){return k.map(function(l,m){return m>0?Ev(l,k[m-1]):l})}))}),R(Dv),U(function(k,l){return k.ba(l)}),Y(b,1)),uc:f,ob:h.i(U(function(k,l){return k.ba(l)}),Y(b,1))}};function Hv(a){this.B=a;this.g=null;this.timeout=new O}function Iv(a,b){Jv(a);a.g=a.B.setTimeout(function(){return void a.timeout.next()},b)}function Jv(a){a.g!==null&&(a.B.clearTimeout(a.g),a.g=null)};function Kv(a,b,c,d){var e=Lv.ef,f=new Hv(b);c=c.i(V(void 0),X(function(){Jv(f);return d})).i(R(function(g){Jv(f);var h=g.J,k=g.active;h.value>=e||!k||(k=b.now(),k=Math.max(0,sr(k,h.timestamp)),Iv(f,Math.max(0,e-h.value-k)));return g.map(function(l){return l>=e})}));return S([c,eq(f.timeout,Q(void 0))]).i(R(function(g){return z(g).next().value}),Yq(function(g){return!g.pa().value},!0),Y(a,1))};function Mv(a,b,c,d){var e=d.Sc,f=d.Cd,g=d.jf,h=d.re,k=d.Nd,l=d.Ve,m=d.Wc;d=d.gf;b=Nv(a,c,b);c=Ov(a,c);d=Pv(b,d);var q=Qv(a,e,l,b),t=q.i(R(function(E){return E.value}),U(),Y(a,1),Wq(function(E,x){return Math.max(E,x)},0)),r=q.i(R(function(E){return E.value}),iv(),Y(a,1)),w=b.i(ev,R(function(E){return E.value}),Jq(2),U(),Y(a,1));g=Rv(a,b,g,h);var u=g.i(V(!1),U(),R(function(E){return E?k:f}));h=q.i(gv(u),U(),Y(a,1));var v=S([h,b]).i(T(function(E){var x=z(E);E=x.next().value;x=x.next().value;return E.timestamp.equals(x.timestamp)}),R(function(E){var x=z(E);E=x.next().value;x=x.next().value;return{visible:E.value,geometry:x.value.F}}),Wq(function(E,x){return!x.visible&&E.visible?E:x},{visible:!1,geometry:Ds}),R(function(E){return E.geometry}),V(Ds),Y(a,1),U(Es));l=l instanceof N?l.i(U(),Iq()):fq;u=S([l,u]).i(Iq());var B=b.i(T(function(E){return E.value.ia!=="ns"&&E.value.X!=="ns"}),Wq(function(E){return E+1},0),V(0),Y(a,1)),P=c.i(Iq(!0),V(!1),Y(a,1));P=S([m,P]).i(R(function(E){var x=z(E);E=x.next().value;x=x.next().value;return E&&!x}),Y(a,1));var I=b.i(nv,U()),ca=I.i(R(function(E){return E.value}),Wq(function(E,x){return Math.max(E,x)},0),U(),Y(a,1));a=I.i(R(function(E){return E.value}),iv(),Y(a,1));return{Yd:l,Ac:u,Ba:{xg:b,N:b.i(hv),Nc:v.i(U(Es)),visible:h.i(U(Ru)),ce:q.i(U(Ru)),Ue:t,tg:r,te:b.i(Zu,U(ib)),Jg:I,pg:ca,sg:a,Rc:c,fa:b.i(R(function(E){return E.value.fa})),ca:g,Sc:e,Wc:m,Le:P,Lg:B,Sb:w,fd:d}}}function Ov(a,b){return b.i(T(function(){return!1}),R(function(c){return c}),Cq(function(c){return xt(new Z(c),a)}))}function Nv(a,b,c){return b.i(iq(fq),Y(a,1)).i(U(function(d,e){return Ru(d,e,av)}),V({timestamp:c.now(),value:$u}),Y(a,1))}function Qv(a,b,c,d){c=d.i(mv(c),bv(R(function(e){return Math.round(e*100)/100})),Y(a,1));return b instanceof Z?c:S([c,b]).i(R(function(e){var f=z(e);e=f.next().value;f=f.next().value;return{timestamp:f.timestamp.maximum(e.timestamp),value:f.value?0:e.value}}),U(Ru),Y(a,1))}function Rv(a,b,c,d){b=[b.i(R(function(e){return e.value.F.width*e.value.F.height>=242500}))];c instanceof N&&b.push(c.i(R(function(e){return!!e})));c=S(b);return d?c.i(R(function(e){return e.some(function(f){return f})}),V(!1),U(),Y(a,1)):xt(new Z(!1),a)}function Pv(a,b){a=S([a,b]).i(R(function(e){var f=z(e);e=f.next().value;if(f.next().value&&e.value.isIntersecting)return e.value.Te}),U());var c=a.i(R(function(e){return e===void 0?!0:e}),Wq(function(e,f){return e||!f},!1)),d=a.i(Wq(function(e,f){return f===void 0?e:f?!1:e!=null?e:!0},void 0),R(function(e){return!!e}));return S([b,lq(a,c,d)]).i(R(function(e){var f=z(e);e=f.next().value;var g=z(f.next().value);f=g.next().value;var h=g.next().value;g=g.next().value;var k=0;if(!e)return 0;if(f===void 0)return 16;f&&(k|=1);f||(k|=2);h&&(k|=4);g&&(k|=8);return k}))};var Sv=M(ut("data-google-av-itpl"),R(function(a){return Number(a)}),R(function(a){return isNaN(a)?1:a}));var Uv=R(Tv);function Tv(a){var b=Number(tt(a,"data-google-av-rs"));if(!isNaN(b)&&b!==0)return b;var c;return(a=(c=a.g)==null?void 0:c.id)?a.startsWith("DfaVisibilityIdentifier")?6:a.startsWith("YtKevlarVisibilityIdentifier")?15:a.startsWith("YtSparklesVisibilityIdentifier")?17:a.startsWith("YtKabukiVisibilityIdentifier")?18:0:0};function Vv(a,b){return M(ut("data-google-av-metadata"),R(function(c){if(c===null)return b(void 0);c=c.split("&").map(function(d){return d.split("=")}).filter(function(d){return d[0]===a});if(c.length===0)return b(void 0);c=c[0].slice(1).join("=");return b(c)}))};var Wv={kf:"addEventListener",lf:"getMaxSize",mf:"getScreenSize",nf:"getState",qf:"getVersion",sf:"removeEventListener",rf:"isViewable"};function Xv(a,b){this.za=null;this.fg=new O;b=b||this.Mg;var c=a.Hd,d=!a.rc;if(c&&d){var e=a.global.top.mraid;if(e){this.Ea=b(e);this.za=e;this.La=3;return}}(a=a.global.mraid)?(this.Ea=b(a),this.za=a,this.La=c?d?2:1:0):(this.La=-1,this.Ea=2)}n=Xv.prototype;n.addEventListener=function(a,b){return this.Ub("addEventListener",a,b)};n.removeEventListener=function(a,b){return this.Ub("removeEventListener",a,b)};n.Me=function(){var a=this.Ub("getVersion");return typeof a==="string"?a:""};n.getState=function(){var a=this.Ub("getState");return typeof a==="string"?a:""};function Yv(a){a=a.Ub("isViewable");return typeof a==="boolean"?a:!1}function Zv(a){if(a.za)return a=a.za.AFMA_LIDAR,typeof a==="string"?a:void 0}n.Mg=function(a){return a?a.IS_GMA_SDK?Object.values(Wv).every(function(b){return typeof a[b]==="function"})?0:1:2:1};n.Ub=function(a){var b=C.apply(1,arguments);if(this.za)try{return this.za[a].apply(this.za,A(b))}catch(c){this.fg.next(a)}};ea.Object.defineProperties(Xv.prototype,{Be:{configurable:!0,enumerable:!0,get:function(){if(this.za){var a=this.za.AFMA_LIDAR_EXP_1;return a===void 0?void 0:!!a}},set:function(a){this.za&&(this.za.AFMA_LIDAR_EXP_1=a)}}});function $v(a,b){return(new Xv(a)).La!==-1?xt(new Z(!0),a.u):b.i(ut("data-google-av-inapp"),R(function(c){return c!==null}),Y(a.u,1))};function aw(a,b){return{td:b.i(ut("data-google-av-adk")),jc:b.i(ut("data-google-av-btr"),U(),R(function(c){return c===null?[]:c.split("|").filter(function(d){return d!==""})})),Ad:b.i(ut("data-google-av-cpmav"),U(),R(function(c){return c===null?[]:c.split("|").filter(function(d){return d!==""})})),jb:b.i(ut("data-google-av-vrus"),U(),R(function(c){return c===null?[]:c.split("|").filter(function(d){return d!==""})})),Lf:vu(a,b),flags:b.i(ut("data-google-av-flags"),U()),ub:$v(a,b),Kd:b.i(Vv("cr",function(c){return c==="1"}),U()),ig:b.i(Vv("omid",function(c){return c==="1"}),U()),Jd:b.i(Sv),metadata:b.i(ut("data-google-av-metadata")),T:b.i(Uv),ra:b.i(vt),Qg:b.i(Vv("la",function(c){return c==="1"}),U()),xb:b.i(ut("data-google-av-turtlex"),R(function(c){return c!==null}),U()),Md:b.i(ut("data-google-av-vattr"),R(function(c){return c!==null}),U())}};function bw(a,b,c,d,e){c=c.i(R(function(){return!1}));d=S([e,d]).i(X(function(f){f=z(f).next().value;return cw(b,f)}));return eq(Q(!1),c,d).i(U(),Y(a.u,1))}function cw(a,b){return a.i(R(function(c){return b||c===0||c===2}))};function dw(a){var b;if(b=ew(a))b=!fw(a,"abgcp")&&!fw(a,"abgc")&&!(typeof a.id==="string"&&a.id==="abgb")&&!(typeof a.id==="string"&&a.id==="mys-abgc")&&!fw(a,"cbb");return b}function fw(a,b){return a.classList?a.classList.contains(b):(" "+a.className+" ").indexOf(" "+b+" ")>-1}function ew(a){try{var b=a.getBoundingClientRect();return b&&b.height>=30&&b.width>=30}catch(c){return!1}}function gw(a,b){if(a.g===void 0||!a.g.children)return a;for(var c=eb(a.g.children);c.length;){var d=b?c.filter(dw):c.filter(ew);if(d.length===1)return new ms(d[0]);if(d.length>1)break;c=lb(c,function(e){return eb(e.children)})}return a}function hw(a,b,c,d,e){if(c)return{Mc:b,Cb:Q(null)};c=b.element.i(R(function(f){a:if(f.g===void 0||ew(f.g))f={Yc:f,Cb:"mue"};else{var g=gw(f,e);if(g.g!==void 0&&ew(g.g))f={Yc:g,Cb:"ie"};else{if(d||a.Hd)if(g=a.document.querySelector(".GoogleActiveViewInnerContainer")){f={Yc:new ms(g),Cb:"ce"};break a}f={Yc:f,Cb:"mue"}}}return f}),Xq());return{Mc:{Db:b.Db,element:c.i(R(function(f){return f.Yc}))},Cb:c.i(R(function(f){return f.Cb}))}};var iw=[33,32],jw=M(Sv,R(function(a){return iw.indexOf(a)>=0}),U());function kw(a,b,c,d,e,f){var g=c.i(R(function(k){return k===9})),h=b.element.i(jw);c=e.i(T(function(k){return k}),X(function(){return S([g,h])}),R(function(k){var l=z(k);k=l.next().value;return!l.next().value||k}),U());f=S([c,d.i(U()),f]).i(R(function(k){var l=z(k);k=l.next().value;var m=l.next().value;l=l.next().value;return hw(a,b,!k,m,l)}),Uq(1),xp());d=f.i(R(function(k){return k.Mc}));f=f.i(X(function(k){return k.Cb}),V(null),U(),Y(a.u,1));return{bb:d,kc:f}};function lw(a){var b=b===void 0?!1:b;return M(X(function(c){return ys(a.document,c,b)}),Y(a.u,1))};function mw(a,b,c,d,e,f){this.tc=b.element.i(lw(a),Y(a.u,1));this.g=bw(a,c,b.element,this.tc,d);c=kw(a,b,e,d,this.g,f);d=c.kc;this.bb=c.bb;this.kc=d;this.he=eq(xt(new Z(1),a.u),b.element.i(Jq(1),R(function(){return 2}),Y(a.u,1)),this.tc.i(Jq(1),R(function(){return 3}),Y(a.u,1)),this.g.i(T(Boolean),Jq(1),R(function(){return 0}),Y(a.u,1))).i(Yq(function(g){return g!==0},!0),Y(a.u,0))};function nw(a){var b=new er(13);if(a.length<1)return{chain:So,yd:So};var c=new O,d=a[0];return{chain:a.slice(1).reduce(function(e,f){return e.i(Cq(function(g){c.next(g);return f}))},d).i(Cq(function(e){c.next(e);return np(b)}),Tq(new O),xp()),yd:c}};function ow(){};function pw(a,b){this.context=a;this.h=b}y(pw,ow);pw.prototype.g=function(a,b){var c=this.h.map(function(f){return f.g(a,b)}),d=nw(c.map(function(f){return f.qb})),e=d.yd.i(qw());return{qb:d.chain.i(Y(this.context.u,1)),mb:Object.assign.apply(Object,[{be:e,Uh:d.yd}].concat(A(c.map(function(f){return f.mb}))))}};function qw(){return Wq(function(a,b){b instanceof er?a.push(b.g):a.push(-1);return a},[])};function rw(a,b){return function(c){return function(d){var e=d.i(Tq(new O),xp());d=c.element.i(U());e=e.i(R(function(f){return f.value}));return S([d,e,b]).i(R(function(f){var g=z(f);f=g.next().value;var h=g.next().value;g=g.next().value;if(f.g===void 0)var k={top:0,left:0,width:0,height:0};else{k=f.g.getBoundingClientRect();var l=nd(f.g,a.global);k={top:l.y,left:l.x,width:k.width,height:k.height}}k=Gs(k,h.sa);l=Fs(k,h.oa);var m=a.B.now(),q=Object,t=q.assign;a:if(g!==2||a.rc||l.width<=0||l.height<=0)var r=!1;else{try{var w=a.document;if(w.document&&w.document!==null&&typeof w.document.elementFromPoint==="function"){var u=w.document.elementFromPoint(l.left+l.width/2,l.top+l.height/2);var v=u===null?null:new ms(u)}else v=null;r=v?!sw(v,f):!1;break a}catch(B){r=!1;break a}r=void 0}return{timestamp:m,value:t.call(q,{},h,{X:"geo",Y:r?$u.Y:l,F:k})}}),Bs(a.u))}}}function sw(a,b,c){c=c===void 0?0:c;return a.g===void 0||b.g===void 0?!1:a.g===b.g||Yc(b.g,function(d){return d===a.g})?!0:b.g.ownerDocument&&b.g.ownerDocument.defaultView&&b.g.ownerDocument.defaultView===b.g.ownerDocument.defaultView.top?!1:c<10&&b.g.ownerDocument&&b.g.ownerDocument.defaultView&&b.g.ownerDocument.defaultView.frameElement?sw(a,new ms(b.g.ownerDocument.defaultView.frameElement),c+1):!0};function tw(a,b){return a&&b===0?15:a||b!==1?null:14}function uw(a,b,c){return b instanceof N?b.i(X(function(d){return(d=tw(d,c))?np(new er(d)):a})):(b=tw(b.value,c))?np(new er(b)):a};function vw(a,b){var c=a.i(Tq(new O),xp());return X(function(d){return c.i(b(d))})};function ww(a,b){if(a.rc)return np(new er(6));var c=new O;return eq(Q({}),b,c).i(R(function(){return{timestamp:a.B.now(),value:{ia:"geo",oa:xw(a),aa:at(a,!0),fa:c,sa:{x:0,y:0}}}}),Bs(a.u))}function xw(a){var b=at(a,!1);if(!a.Hd||!Bd(a.global.parent)||a.global.parent===a.global)return b;var c=new Xs(a.global.parent,a.Ga);c.L=a.L;c=xw(c);a=a.global.frameElement.getBoundingClientRect();return Fs(Gs(Fs(c,a),{x:b.left-a.left,y:b.top-a.top}),b)};function yw(a,b){this.context=a;this.Ab=b}y(yw,ow);yw.prototype.g=function(a,b){var c=vw(ww(this.context,this.Ab),rw(this.context,b.T));return{qb:uw(a.bb.i(c),b.ub,0),mb:{}}};function zw(a,b){this.l=a;this.j=b;this.h=this.g=null}function Aw(a,b){b?a.h||(b=Object.assign({},a.j,{delay:100,trackVisibility:!0}),a.h=new IntersectionObserver(a.l,b)):a.g||(a.g=new IntersectionObserver(a.l,a.j))}function Bw(a,b){a=b?a.h:a.g;if(!a)throw new hr;return a}zw.prototype.observe=function(a,b){Bw(this,a).observe(b)};zw.prototype.unobserve=function(a,b){Bw(this,a).unobserve(b)};zw.prototype.disconnect=function(a){Bw(this,a).disconnect()};function Cw(a){return function(b){return b.i(a.ResizeObserver?Dw(a):Ew(a),Uq(1),xp())}}function Dw(a){return function(b){return b.i(X(function(c){var d=a.ResizeObserver;if(!d||c.g===void 0)return Q($u.F);var e=(new N(function(f){function g(){c.g!==void 0&&h.unobserve(c.g);h.disconnect();k.unsubscribe()}if(c.g===void 0)return f.complete(),function(){};var h=new d(function(l){l.forEach(function(m){f.next(m)})});h.observe(c.g);var k=c.h.subscribe(g);return g})).i(Qr(a.K,736),R(function(f){return f.contentRect}));return eq(Q(c.g.getBoundingClientRect()),e)}),U(Es))}}function Ew(a){return function(b){var c=b.i(tu(a,{attributes:!0,childList:!0,characterData:!0,subtree:!0})),d=a.wg;c=eq(b.i(R(function(){return ls("resize")})),c,d);return S(b,c).i(Qr(a.K,737),R(function(e){e=z(e).next().value;return e.g===void 0?void 0:e.g.getBoundingClientRect()}),Ct(),U(Es))}};function Fw(a,b){var c=Gw(a,b).i(Uq(1),xp());return function(d){return function(e){e=e.i(X(function(f){return f.element}),U());return S([c,e]).i(X(function(f){var g=z(f);f=g.next().value;g=g.next().value;return Hw(a,f.gg,Cw(a),f.ug,d,f.Uf,g)}),Bs(a.u))}}}function Iw(a,b,c){var d=Fw(a,c)(b);return function(e){var f=d(Q(e));return function(g){return S([g,f]).i(R(function(h){var k=z(h);h=k.next().value;k=k.next().value;var l=Gs(k.value.F,h.value.sa),m=Fs(Gs(k.value.Y,h.value.sa),h.value.oa);return{timestamp:h.timestamp.maximum(k.timestamp),value:Object.assign({},h.value,{X:"nio",Y:m,F:l})}}))}}}function Jw(a){return R(function(b){return b.value.ia!=="nio"?b:Object.assign({},b,{value:Object.assign({},b.value,{oa:at(a,!0),aa:at(a,!0)})})})}function Kw(a,b){return Q(b).i(a,R(function(){return b}))}function Gw(a,b){return a.B.timeline!==or?np(new er(2)):a.MutationObserver?typeof IntersectionObserver==="undefined"?np(new er(0)):(new N(function(c){var d=new O,e=new zw(d.next.bind(d),{threshold:[].concat(A(b))});c.next({ug:d.i(Qr(a.K,735)),gg:e,Uf:function(f){f=Bw(e,f).takeRecords();f.length>0&&d.next(f)}})})).i(Jq(1),Uq(1),xp()):np(new er(1))}function Lw(a){return bp(a.sort(function(b,c){return b.time-c.time}),Bp)}function Hw(a,b,c,d,e,f,g){return new N(function(h){function k(){w||(w=!0,g.g!==void 0&&b.unobserve(e,g.g),m.unsubscribe(),r.unsubscribe(),t.unsubscribe(),u.unsubscribe())}if(g.g!==void 0){Aw(b,e);b.observe(e,g.g);var l=new Ro({timestamp:a.B.now(),value:Object.assign({},$u,{ia:"nio",X:"nio"})}),m=d.i(Op(function(v){return Lw(v)}),T(function(v){return v.target===g.g}),R(function(v){return{timestamp:new qr(v.time,or),value:{ia:"nio",oa:v.rootBounds||Ds,aa:v.rootBounds||at(a,!0),fa:q,X:"nio",Y:v.intersectionRect,F:v.boundingClientRect,sa:{x:0,y:0},isIntersecting:v.isIntersecting,Te:v.isVisible}}}),Tq(l),xp()).subscribe(h),q=new O,t=q.subscribe(function(){f(e);h.next({timestamp:a.B.now(),value:l.value.value});g.g!==void 0&&(b.unobserve(e,g.g),b.observe(e,g.g))}),r=Kw(c,g).subscribe(function(){q.next()}),w=!1,u=g.h.subscribe(function(){return k()});return k}})};function Mw(a,b,c){c=c===void 0?Fw(a,b):c;this.context=a;this.h=c}y(Mw,ow);Mw.prototype.g=function(a,b){var c=this.h(b.hf);return{qb:uw(a.bb.i(c,Jw(this.context)),b.ub,0),mb:{}}};function Nw(a,b,c,d,e){var f=f===void 0?new Xv(a):f;var g=g===void 0?cr(a.B,500):g;var h=h===void 0?cr(a.B,100):h;e=Q(f).i(Ow(c),Zq(function(k){d.next(k.La)}),Pw(a,h),Qw(a),Rw(a,e),Uq(1),xp());f=new O;b=eq(Q({}),b,f);return e.i(Sw(a,f,b,g,c),Y(a.u,1))}function Rw(a,b){return M(function(c){return S([c,b])},Kq(function(c){var d=z(c);c=d.next().value;return d.next().value!==9||Yv(c)?Q(!0):Tw(a,c,"viewableChange").i(T(function(e){return z(e).next().value}),Jq(1))}),R(function(c){return z(c).next().value}))}function Ow(a){return X(function(b){if(b.La===-1)return a.next("if"),np(new er(7));if(b.Ea!==0)switch(b.Ea){case 1:return a.next("mm"),np(new er(18));case 2:return a.next("ng"),np(new er(17));default:return a.next("i"),np(new er(8))}return Q(b)})}function Pw(a,b){return Kq(function(){var c=a.Xe;return ws(a.document)==="complete"?Q(!0):c.i(Kq(function(){return b}))})}function Qw(a){return X(function(b){return b.getState()!=="loading"?Q(b):Tw(a,b,"ready").i(R(function(){return b}))})}function Sw(a,b,c,d,e){return X(function(f){var g=Zv(f);if(typeof g!=="string")return e.next("nc"),np(new er(9));f.Be!==void 0&&(f.Be=!0);g=Tw(a,f,g,Uw);var h={version:f.Me(),La:f.La};g=g.i(R(function(l){return Vw.apply(null,[a,b,f,h].concat(A(l)))}));var k=d.i(Zq(function(){e.next("mt")}),X(function(){return np(new er(10))}));g=jq(g,k);return S([g,c]).i(R(function(l){l=z(l).next().value;return Object.assign({},l,{timestamp:a.B.now()})}))})}function Uw(a,b){return(b===null||typeof b==="number")&&(a===null||!!a&&typeof a.height==="number"&&typeof a.width==="number"&&typeof a.x==="number"&&typeof a.y==="number")}function Vw(a,b,c,d,e,f){e=e?{left:e.x,top:e.y,width:e.width,height:e.height}:Ds;c=c.Ub("getMaxSize");var g=c!=null&&typeof c.width==="number"&&typeof c.height==="number"?c:{width:0,height:0};c={left:0,top:0,width:-1,height:-1};if(g){var h=Number(String(g.width));g=Number(String(g.height));c=isNaN(h)||isNaN(g)?c:{left:0,top:0,width:h,height:g}}a={value:{oa:e,aa:c,ia:"mraid",fa:b,sa:{x:0,y:0}},timestamp:a.B.now()};return Object.assign({},a,d,{rh:f})}function Tw(a,b,c,d){d=d===void 0?function(){return!0}:d;return(new N(function(e){var f=a.K.Xb(745,function(){e.next(C.apply(0,arguments))});b.addEventListener(c,f);return function(){b.removeEventListener(c,f)}})).i(T(function(e){return d.apply(null,A(e))}))};function Ww(a,b){this.context=a;this.Ab=b}y(Ww,ow);Ww.prototype.g=function(a,b){var c=new pp(1),d=new pp(1),e=vw(Nw(this.context,this.Ab,c,d,b.T),rw(this.context,b.T));return{qb:uw(a.bb.i(e),b.ub,1),mb:{Sd:c.i(Y(this.context.u,1)),Td:d.i(Y(this.context.u,1))}}};function Xw(a){return["backgrounded","notFound","hidden","noOutputDevice"].includes(a)};function Yw(a,b){var c=c===void 0?null:c;var d=new O,e=void 0,f=a.Ke,g=d.i(R(function(){return e?Object.assign({},e,{timestamp:a.B.now()}):null}),T(function(k){return k!==null}),R(function(k){return k}));b=S([eq(f,g),b]);var h=c;return b.i(T(function(k){k=z(k).next().value;h===null&&(h=k.value.adSessionId);return k.value.adSessionId===h}),Zq(function(k){return void(e=z(k).next().value)}),R(function(k){var l=z(k);k=l.next().value;l=l.next().value;try{var m=k.value.data,q=k.timestamp,t=m.viewport,r,w,u=Object.assign({},t,{width:(r=t==null?void 0:t.width)!=null?r:0,height:(w=t==null?void 0:t.height)!=null?w:0,x:0,y:0,Oh:t?t.width*t.height:0}),v=Zw(u),B=m.adView,P=B.measuringElement&&B.containerGeometry?Zw(B.containerGeometry):Zw(B.geometry),I=Zw(B.geometry),ca=B.reasons.some(Xw),E=ca?Ds:Zw(B.onScreenGeometry),x;l&&(x=B.percentageInView/100);l&&ca&&(x=0);return{timestamp:q,value:{ia:"omid",oa:P,aa:v,fa:d,X:"omid",F:I,sa:{x:P.left,y:P.top},Y:E,Qc:x}}}catch(Sc){var W,Tc;m=(Tc=(W=Sc)==null?void 0:W.message)!=null?Tc:"An unknown error occurred";W="Error while processing geometryChange event: "+JSON.stringify(k.value)+"; "+m;throw Error(W);}}),Uq(1),xp())}function Zw(a){var b,c,d,e;return{left:Math.floor((b=a==null?void 0:a.x)!=null?b:0),top:Math.floor((c=a==null?void 0:a.y)!=null?c:0),width:Math.floor((d=a==null?void 0:a.width)!=null?d:0),height:Math.floor((e=a==null?void 0:a.height)!=null?e:0)}};function $w(a,b,c,d){c=c===void 0?fq:c;var e=a.u;if(b===null)return np(new er(20));if(!b.validate())return np(new er(21));var f;d=ax(e,b,d).i(R(function(g){var h=g.value;g=g.timestamp;var k=b.B,l=a.B;if(k.timeline!==g.timeline)throw new jr;g=new qr(g.value-k.now().value+l.now().value,l.timeline);return f={value:h,timestamp:g}}));return eq(d,c.i(R(function(){return f}))).i(T(function(g){return g!==void 0}),R(function(g){return g}),Y(a.u,1))}function ax(a,b,c){return Yw(b,c).i(Y(a,1),R(function(d){return{timestamp:d.timestamp,value:{sa:{x:d.value.F.left,y:d.value.F.top},oa:d.value.Y,aa:d.value.aa,ia:d.value.X,fa:d.value.fa}}}))};function bx(a,b,c){this.ha=a;this.U=b;this.Ab=c}y(bx,ow);bx.prototype.g=function(a,b){var c=b.T;b=$w(this.U,this.ha,this.Ab,b.We);c=vw(b,rw(this.U,c));return{qb:a.bb.i(c),mb:{}}};function cx(a,b,c){this.ha=a;this.U=b;this.h=c}y(cx,ow);cx.prototype.g=function(a,b){var c=$w(this.U,this.ha,void 0,b.We);b=Iw(this.U,b.hf,this.h);c=vw(c,b);return{qb:a.bb.i(c),mb:{}}};function dx(a){return a.document.j.i(R(function(b){return b==="visible"}),U(),Y(a.u,1))};function ex(a,b,c){var d;return b.i(U(),X(function(e){return c.i(R(function(){if(!d){d=!0;try{e.next()}finally{d=!1}}return!0}))}),V(!1),Y(a.u,1))};function fx(a,b){a.Fa&&(a.Bb=a.Fa);a.Fa=b;a.Bb&&a.Bb.value?(b=Math.max(0,sr(b.timestamp,a.Bb.timestamp)),a.totalTime+=b,a.ua+=b):a.ua=0;return a}function gx(){return M(Wq(fx,{totalTime:0,ua:0}),R(function(a){return a.totalTime}))}function hx(){return M(Wq(fx,{totalTime:0,ua:0}),R(function(a){return a.ua}))};function ix(){var a;return M(Zq(function(b){return void(a=b.timestamp)}),hx(),R(function(b){return{timestamp:a,value:Math.round(b)}}))};function jx(){return M(hx(),Wq(function(a,b){return Math.max(a,b)},0),R(function(a){return Math.round(a)}))};function kx(a){return M(gv(Q(a)),jx())};function lx(a){return M(bv(R(function(b){return fv(b,a)})),gx(),R(function(b){return Math.round(b)}))};function mx(a,b,c,d,e){var f=Gv;if(f.length>1)for(var g=0;g<f.length-1;g++)if(f[g]<f[g+1])throw Error();g=e.i(V(void 0),X(function(){return c.i(ix())}),U(),Y(a,1));e=e.i(V(void 0),X(function(){return c.i(jx())}),U(),Y(a,1));return{hb:d.i(V(void 0),X(function(){return b.i(R(function(h){return{timestamp:h.timestamp,value:!0}}),gx())}),U(),Y(a,1)),ib:d.i(V(void 0),X(function(){return b.i(R(function(h){return{timestamp:h.timestamp,value:h.value===0}}),gx())}),U(),Y(a,1)),vc:d.i(V(void 0),X(function(){return b.i(vv(kx,f))}),U(ib),Y(a,1)),Ma:d.i(V(void 0),X(function(){return b.i(vv(lx,f),R(function(h){return h.map(function(k,l){return l>0?k-h[l-1]:k})}))}),U(ib),Y(a,1)),uc:e,ob:g.i(U(Ru),Y(a,1))}};function nx(a,b){var c=this;this.B=a;this.h=this.g=null;this.j=b.i(U()).subscribe(function(d){ox(c);c.h=d})}function px(a,b){ox(a);a.g=a.B.setTimeout(function(){var c;return void((c=a.h)==null?void 0:c.next())},b)}function ox(a){a.g!==null&&a.B.clearTimeout(a.g);a.g=null}nx.prototype.dispose=function(){ox(this);this.j.unsubscribe();this.h=null};function qx(a,b,c,d,e){var f=Lv.ef;var g=g===void 0?new nx(b,d):g;return(new N(function(h){var k=c.i(V(void 0),X(function(){return rx(e)})).i(R(function(l){var m=l.value;l=l.timestamp;var q=m.visible;m=m.ob;var t=m>=f;t||!q?ox(g):(l=Math.max(0,sr(b.now(),l)),px(g,Math.max(0,f-m-l)));return t}),Wq(function(l,m){return m||l},!1),U()).subscribe(h);return function(){g.dispose();k.unsubscribe()}})).i(Yq(function(h){return!h},!0),Y(a,1))}function rx(a){return uv([a,a.i(ix())]).i(R(function(b){var c=z(b);b=c.next().value;c=c.next().value;return{timestamp:b.timestamp,value:{visible:b.value,ob:c.value}}}),U(function(b,c){return Ru(b,c,function(d,e){return d.ob===e.ob&&d.visible===e.visible})}))};function sx(a,b,c){var d=c.i(R(function(e){return{value:e,timestamp:a.B.now()}}),U(Ru));return b instanceof N?b.i(U(),X(function(e){return e?xt(new Z({value:!1,timestamp:a.B.now()}),a.u):d})):b.value===!1?d:new Z(!1)}function tx(a,b,c,d,e,f,g){var h=Lv;b=b instanceof N?b.i(V(!1),U()):b;var k=!(Ad()||zd());c=sx(a,c,d);a=g.bb.i(Fu(a.u));return Object.assign({},h,{Sc:c,jf:e,re:k,Ve:b,Wc:a,gf:f})};function ux(a){this.C=jg(a)}y(ux,$h);function vx(a,b){return Ug(a,1,b)};var Lv=Object.freeze({ef:1E3,Cd:.5,Nd:.3}),Gv=Object.freeze([1,.75,Lv.Cd,Lv.Nd,0]);function wx(a,b,c,d,e){this.A=a;this.g=void 0;this.m=b;this.hc=c;this.Ic=d;this.Jc=e;this.Aa=2;this.name="rxlidar";this.o=new pp;this.controlledEvents=[];this.subscribedEvents=[];this.j=new pp;this.h=new pp;this.controlledEvents.push(this.hc,this.Ic,this.Jc);this.subscribedEvents.push(this.m)}n=wx.prototype;n.start=function(a){if(this.l===void 0&&a.U){var b;if((b=this.g)!=null)var c=b;else{b=a.U;var d=(c=a.ha)!=null?c:null;c={Rf:.01,rg:cr(b.B,36E5),Ab:b.B.Ia(100).i(Y(b.u,1)),ha:d}}this.g=c;a=a.U;this.l=xx(a,this.j.i(Y(a.u,1)),this.g.Rf,this.g.rg,this.g.Ab,this.g.ha,this.h.i(V(!1),Y(a.u,1)),this.hc,this.Ic,this.Jc,this.Aa).subscribe(this.o)}};n.dispose=function(){this.j.complete();this.h.complete();var a;(a=this.l)==null||a.unsubscribe();this.l=void 0};n.Gd=function(a,b,c,d,e){var f;(f=!wg(b,Ig,2))||(f=Jg(b,Ig,2),f=Og(f,4,!0));if(f){this.j.next(Object.assign({},this.A.xe.get(a),{metadata:b,experimentState:c,Vh:a,Wa:d}));var g,h;e((h=(g=Jg(b,Ig,2))==null?void 0:Pg(g,6))!=null?h:-1)}};n.Mb=function(){};n.handleEvent=function(a,b){b===this.m&&(this.h.next(!0),this.h.complete())};function xx(a,b,c,d,e,f,g,h,k,l,m){var q=dx(a).i(R(function(r){return!r})),t=new pw(a,[new Mw(a,Gv),new yw(a,e),new cx(f,a,Gv),new bx(f,a,e),new Ww(a,e)]);return cu(a,b,function(r,w){var u=aw(r,w.element),v=u.td,B=u.jc,P=u.Ad,I=u.jb,ca=u.Lf,E=u.ub,x=u.ig,W=u.Jd,Tc=u.Kd,Sc=u.T,Id=u.ra,Ia=u.Qg,Jd=u.xb;u=u.Md;var Yg,nb=(Yg=Jt(Hg(w.metadata)))!=null?Yg:"";Yg=ai(vx(new ux,atob(nb)));nb=xt(new Z(w.experimentState),r.u);var Xp=new Z(new ur(r,new ds(r))),Yp=nb.i(R(function(J){return J.fetchLaterBeacons}),V(!1),U(),Y(r.u,1)),gy=Yp.i(R(function(J){return J&&(new Yr(r)).O({Ce:!0})}),Zq(function(J){J&&Xp.value.M("https://pagead2.googlesyndication.com/pagead/gen_204?id=av-js&type=fetch&later&start&control&fle=1&sfl=1").sendNow()})),Te=nb.i(R(function(J){return J.shouldIgnoreAdChoicesIcon})),Qa=E.i(Fq(x),R(function(J){var ob=z(J);J=ob.next().value;ob=ob.next().value;return J||ob||Zj()}));x=new mw(r,w,ca,E,Sc,Te);Te=nb.i(R(function(J){return J.considerOmidZOrderOcclusions}));var Uc,Mb=(Uc=Kt(Hg(w.metadata)))!=null?Uc:!1;Uc=t.g(x,{ub:E,hf:Mb,T:Sc,We:Te});var db=Uc.qb,Ue=Uc.mb;Uc=Ue.Sd;Te=Ue.Td;Ue=Ue.be;Mb=xt(new Z(Mb),r.u);var oc=tx(r,Tc,Qa,q,Ia,Mb,x);Ia=Mv(r.u,r.B,db,oc);Qa=mx(r.u,Ia.Ba.ce,Ia.Ba.visible,Ia.Yd,Ia.Ac);Mb=qx(r.u,r.B,Ia.Ac,Ia.Ba.fa,Ia.Ba.visible);db=ov(r.u,r.B,db,oc);oc=Fv(r.B,r.u,db.Ba.ce,db.Ba.visible,db.Yd,db.Ac);var sk={ge:Kv(r.u,r.B,db.Ac,oc.uc)},tk=nb.i(R(function(J){return J.extrapolators}),V(!1));db=Eu(r.u,tk,Object.assign({},db.Ba,oc,sk),Object.assign({},Ia.Ba,{ge:Yu(r,Mb),vc:Yu(r,Qa.vc),Ma:Yu(r,Qa.Ma),uc:Yu(r,Qa.uc),ob:Qa.ob.i(R(function(J){return new Xu(r.B,J)})),hb:Yu(r,Qa.hb),ib:Yu(r,Qa.ib)}));Qa=Ou(r,d.i(Iq("t")));Mb=(f!==null&&f.validate()?f.Dg:fq).i(Y(r.u,1),Iq("u"));Qa=jq(Qa,Mb);Mb=ex(r,db.fa,Qa.i(T(function(J){return J!==null})));oc=yx(r,x,v);sk=zx(r,Qa,w.element);tk=oc.yf.i(V({x:0,y:0}));var jy=nb.i(R(function(J){return J.rxlidarStatefulBeacons}),V(!1),U(),Zq(function(J){hs=J}),Y(r.u,1)),Zp=W.i(R(function(J){return J===40||J===41||J===42})),ky=nb.i(R(function(J){return J.waitForImpressionColleague}),V(!1),U(),Y(r.u,1)),ly=b.i(R(function(J){var ob;return J.experimentState.addQueryIdToErrorPing?(ob=Jg(J.metadata,Rt,3))==null?void 0:Uf(sg(ob,1)):void 0}));return Object.assign({},{L:new Z(r.L),Pb:new Z("lidar2"),Hg:new Z("lidartos"),Bf:new Z("unreleased"),Aa:new Z(m),zd:new Z(r.validate()?null:new fr),Ff:new Z(xs(r.document)),Z:new Z(nu),De:Qa,df:Qa,Ph:Mb,Vc:g,Pg:ky,Wa:new Z(w.Wa),hc:new Z(h),Ic:new Z(k),Jc:new Z(l),Kf:new Z(r.rc?1:void 0),Mf:new Z(r.Cf?1:void 0),ub:E,xb:Jd,de:new Z(Yg),Tb:Jd.i(T(function(J){return J}),R(function(){return r.Tb.bind(r)})),Sd:Uc.i(Y(r.u,1)),Td:Te.i(Y(r.u,1)),Sf:nb.i(R(function(J){return J.extraPings})),Pe:jy,ag:Yp,bf:gy,Md:u,hg:Zp,bg:nb.i(R(function(J){return J.dedicatedViewableAttributionPing})),Tf:Xp,cf:new Z(hs&&(new gs(r)).O({na:"GET"})),Eg:new Z(Number(w.experimentState.useReachIntegrationSharedStorage)<<Number(w.experimentState.useReachIntegrationPolyfill)<<1+Number(w.experimentState.sendBrowserIdInsteadOfVPID)<<2),Gf:w.element.i(R(function(J){return J!==null})),tb:Id,Ig:Id,Ad:P.i(V([])),jb:I.i(V([])),Xf:P.i(R(function(J){return J.length>0?!0:null}),V(null),U()),jc:B.i(V([]),Y(r.u,1)),wh:nb,shouldSendExplicitDisplayMeasurablePing:nb.i(R(function(J){return J.shouldSendExplicitDisplayMeasurablePing})),td:v,kc:x.kc,Jd:W.i(V(0),Y(r.u,1)),qg:ca,T:Sc.i(V(0),Y(r.u,1)),Eb:Zp.i(R(function(J){return J?Cu:mu})),Yb:new Z(Du),Kd:Tc,Qe:x.tc.i(Fu(r.u)),he:x.he},db,{Nc:S([db.Nc,tk]).i(R(function(J){var ob=z(J);J=ob.next().value;ob=ob.next().value;return Gs(J,ob)}),U(Es))},oc,{Bc:Xt(r),Yf:sk,be:Ue,fd:Ia.Ba.fd,Df:new Z(new yu),escapedQueryId:ly})},lu(a,"https://pagead2.googlesyndication.com/pagead/gen_204?id=av-js&type=error&bin="+m+"&v=unreleased",c))}function yx(a,b,c){var d=d===void 0?Ma:d;var e,f;d=((e=d.performance)==null?void 0:(f=e.timing)==null?void 0:f.navigationStart)||0;return Object.assign({},{xf:new Z(d),wf:Nu(a,b)},Mu(a,b,c))}function zx(a,b,c){return b.i(T(function(d){return d!==null}),X(function(){return c}),R(function(d){var e=At(a);return e.length>0&&e.indexOf(d)>=0}),R(function(d){return!d}))};function Ax(a){var b=b===void 0?[]:b;var c=c===void 0?[a]:c;this.j=a;this.subscribedEvents=b;this.controlledEvents=c;this.name="impression";this.g=new Map}n=Ax.prototype;n.start=function(a){this.h=a};n.dispose=function(){this.g.clear()};n.Gd=function(a,b,c,d){if(b=this.h)c=new Bx(b,c,this.j,d),this.g.set(a,c)};n.Mb=function(a){this.g.delete(a)};n.handleEvent=function(){};function Bx(a,b,c,d){var e=this;this.context=a;this.A=c;this.o=function(){};this.m=[];this.j="&avradf=1";this.l=kt([]);this.h=new pp;c=a.ha;var f=c!==null&&(c==null?void 0:c.validate()),g,h=(g=a.U)==null?void 0:g.u;this.h.i(V(!b.waitForImpressionColleague),Y(h,1));this.D=f?c==null?void 0:c.Oe.i(Jq(1),Iq(!0),V(!1)):xt(new Z(!0),h);this.o=function(k,l){e.h.next(!0);e.h.complete();S([e.h,e.D]).subscribe(function(m){var q=z(m);m=q.next().value;q=q.next().value;if(!q)return fq;m&&q&&d(e.A,k,l);return!0})};this.init(a.U)}Bx.prototype.init=function(a){var b=this;this.g=a.global.document;this.m.push(Cx(this));var c={};this.l=kt(this.m);this.l.then(function(){b.j="&vis="+sd(b.g)+"&uach=0&ms=0";c.paramString=b.j;c.view_type="DELAYED_IMPRESSION";b.o(c,function(){})})};function Cx(a){return new ht(function(b){var c=td(a.g);if(c)if(sd(a.g)===3){var d=function(){rd(a.g,c,d);b(!0)};qd(a.g,c,d)}else b(!0)})};function Dx(a){var b=bt(a);return b?b.i(R(function(c){var d;c=(d=Kg(c).find(function(f){return Uf(sg(f,1))==="Google Chrome"}))==null?void 0:Uf(sg(d,2));if(!c)return!1;var e;return((e=z(c.split(".").map(function(f){return Number(f)})).next().value)!=null?e:0)>=121})):xt(yt,a.u)};function Ex(a,b){b="https://pagead2.googlesyndication.com/pagead/gen_204?id=av-js&type=reach&proto="+encodeURIComponent(ke(b.g()));a.L.M(b,{na:"GET"}).sendNow()};function Fx(a){return[{Kb:2,zc:!1,lc:!0,filterIds:Gx(a==null?void 0:a.productionFilterIds)},{Kb:2,zc:!0,lc:!0,filterIds:Gx(a==null?void 0:a.testFilterIds)},{Kb:2,zc:!1,lc:!1,filterIds:Gx(a==null?void 0:a.testFilterIds)}]}function Gx(a){if(a!==void 0&&typeof BigInt==="function")return a.map(function(b){return BigInt(b)})};function Hx(a){this.C=jg(a)}y(Hx,$h);function Ix(a,b){return Vg(a,1,b)}function Jx(a,b){return Ug(a,2,b)}function Kx(a,b){return Ug(a,3,b)};Hx.prototype.g=aj([0,Xi,Ui,-1,Xi,-2,Ui,-1,Pi,Ui,Pt,Yi,Pi]);function Lx(a){this.context=a;this.g=[]}function Mx(a,b){Ga(function(c){if(c.g==1)return c.l=0,c.m=2,ua(c,b(),4);if(c.g!=2)return c.return(c.h);xa(c);a.flush();return ya(c,0)})}Lx.prototype.flush=function(){if(!(this.g.length<=0)){var a=new Hx;Ix(a,9);var b=Fx().length;ug(a,13,Hf(b));Ng(a,12,this.g);this.g.splice(0);Ex(this.context,a)}};function Nx(){this.blockSize=-1};function Ox(a,b){this.blockSize=-1;this.blockSize=64;this.l=Ma.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.j=this.h=0;this.g=[];this.o=a;this.m=b;this.A=Ma.Int32Array?new Int32Array(64):Array(64);Px===void 0&&(Ma.Int32Array?Px=new Int32Array(Qx):Px=Qx);this.reset()}Sa(Ox,Nx);for(var Rx=[],Sx=0;Sx<63;Sx++)Rx[Sx]=0;var Tx=[].concat(128,Rx);Ox.prototype.reset=function(){this.j=this.h=0;this.g=Ma.Int32Array?new Int32Array(this.m):eb(this.m)};function Ux(a){for(var b=a.l,c=a.A,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(b=16;b<64;b++)d=c[b-15]|0,e=c[b-2]|0,c[b]=((c[b-16]|0)+((d>>>7|d<<25)^(d>>>18|d<<14)^d>>>3)|0)+((c[b-7]|0)+((e>>>17|e<<15)^(e>>>19|e<<13)^e>>>10)|0)|0;b=a.g[0]|0;d=a.g[1]|0;e=a.g[2]|0;for(var f=a.g[3]|0,g=a.g[4]|0,h=a.g[5]|0,k=a.g[6]|0,l=a.g[7]|0,m=0;m<64;m++){var q=((b>>>2|b<<30)^(b>>>13|b<<19)^(b>>>22|b<<10))+(b&d^b&e^d&e)|0,t=(l+((g>>>6|g<<26)^(g>>>11|g<<21)^(g>>>25|g<<7))|0)+(((g&h^~g&k)+(Px[m]|0)|0)+(c[m]|0)|0)|0;l=k;k=h;h=g;g=f+t|0;f=e;e=d;d=b;b=t+q|0}a.g[0]=a.g[0]+b|0;a.g[1]=a.g[1]+d|0;a.g[2]=a.g[2]+e|0;a.g[3]=a.g[3]+f|0;a.g[4]=a.g[4]+g|0;a.g[5]=a.g[5]+h|0;a.g[6]=a.g[6]+k|0;a.g[7]=a.g[7]+l|0}Ox.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.h;if(typeof a==="string")for(;c<b;)this.l[d++]=a.charCodeAt(c++),d==this.blockSize&&(Ux(this),d=0);else if(Oa(a))for(;c<b;){var e=a[c++];if(!("number"==typeof e&&0<=e&&255>=e&&e==(e|0)))throw Error("message must be a byte array");this.l[d++]=e;d==this.blockSize&&(Ux(this),d=0)}else throw Error("message must be string or array");this.h=d;this.j+=b};var Qx=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],Px;function Vx(){Ox.call(this,8,Wx)}Sa(Vx,Ox);var Wx=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];function Xx(a){this.C=jg(a)}y(Xx,$h);Xx.prototype.g=aj([0,Hk,Ti,-1,Vi,-3,Zi,Ti]);var Yx=$i(Gk,Hk);function Zx(){var a;this.message=a=a===void 0?new Xx:a}function $x(a,b){var c=a.message;b=Yx(me(b));c=Mg(c,1,b);a.message=c;return a}function ay(a,b){var c=Rg(a.message,2,b.Kb===2);b=Rg(c,3,!b.zc);a.message=b;return a}function by(a,b){a.message=Sg(a.message,4,Math.max(1,b));return a}function cy(a,b){a.message=Ng(a.message,8,b);return a}function dy(a){var b="unreleased".match(/m\d{12}/g),c="unreleased".match(/\d{8}/g);if(b&&b.length>0){b=b[0].slice(1);c=a.message;var d=Number(b.slice(0,8));c=Sg(c,5,d);d=Number(b.slice(8,10));c=Sg(c,6,d);b=Number(b.slice(10,12));b=Sg(c,7,b);a.message=b;return a}if(c&&c.length>0)return b=Sg(a.message,5,Number(c[0])),b=ug(b,6),b=ug(b,7),a.message=b,a;b=ug(a.message,5);b=Sg(b,6,0);b=ug(b,7);a.message=b;return a}function ey(a){a=a.message;var b=ke(a.g());b.length>64&&(a=Sg(a,4,1),b=ke(a.g()));b.length>64&&(a=ug(a,6),b=ke(a.g()));b.length>64&&(a=ug(a,7),b=ke(a.g()));b.length>64&&(a=ug(a,5),b=ke(a.g()));return b};function fy(a,b){if(b===void 0||b.length===0)return Ex(a,Ix(new Hx,7)),[qq(0)].filter(function(d){return d!==void 0});var c=qq(-**********);return c===void 0?[]:b.map(function(d){var e=d%c;d!==e&&Ex(a,Ix(new Hx,6));return e})};function hy(a,b){var c=c===void 0?BigInt(0):c;return{bucket:a,value:b?1:16384,filteringId:c}};function iy(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}if(b.length>=24)throw Error("String too long for CBOR encoder.");return[96|b.length].concat(A(b))}function my(a){if(a.length>=24)throw Error("Map too long for CBOR encoder.");return[160|a.length].concat(A(a.sort(ny).map(function(b){return[].concat(A(b[0]),A(b[1]))}).flat()))}function oy(a){if(a.length>=24)throw Error("Array too long for CBOR encoder.");return[128|a.length].concat(A(a.flat()))}function py(a,b){for(var c=[];a>0;)c.push(Number(a%BigInt(255))),a/=BigInt(255);for(;c.length<b;)c.push(0);return c.reverse()}function ny(a,b){a=a[0];b=b[0];if(a.length!==b.length)return a.length-b.length;for(var c=0;c<a.length;c++)if(a[c]!==b[c])return a[c]-b[c];return 0};function qy(a,b,c,d){var e=hy(BigInt(c),d);b={shared_info:JSON.stringify({api:"shared-storage",report_id:"PRE_WORKLET_ERROR",reporting_origin:"https://www.googleadservices.com",scheduled_report_time:String((new Date).getUTCSeconds()),version:"polyfill"}),aggregation_service_payloads:[],context_id:b,aggregation_coordinator_origin:"https://publickeyservice.msmt.gcp.privacysandboxservices.com"};d?(b.debug_key="0",b.aggregation_service_payloads.push({payload:String(c),key_id:"0",debug_cleartext_payload:ry([e])})):b.aggregation_service_payloads.push({payload:String(c),key_id:"0"});try{var f,g;(f=a.global)==null||(g=f.fetch)==null||g.call(f,"https://www.googleadservices.com/.well-known/private-aggregation/report-shared-storage",{method:"POST",cache:"no-cache",keepalive:!0,mode:"no-cors",headers:{"content-type":"application/json"},body:JSON.stringify(b)}).catch(function(){})}catch(h){}}function ry(a){a=my([[iy("data"),oy(a.map(function(b){return my([[iy("value"),[68].concat(A(py(BigInt(b.value),4)))],[iy("bucket"),[80].concat(A(py(b.bucket,16)))],[iy("filteringId"),[68].concat(A(py(b.filteringId,4)))]])}))],[iy("operation"),iy("histogram")]]);return btoa(String.fromCharCode.apply(String,A(new Uint8Array(a))))};var sy={},ty=(sy[2]="prod",sy[1]="canary",sy);function uy(a,b,c,d){var e,f,g,h,k,l,m,q;return Ga(function(t){switch(t.g){case 1:e=Fx(c);f=function(r){e.forEach(function(w){var u,v=ey(by(dy(ay(cy($x(new Zx,c.escapedQueryId),(u=c.trafficTypes)!=null?u:[0]),w)),-1));qy(a,v,r,w.lc)})};g=Ys(a);if(g instanceof Error)return f(-16),h=Kx(Jx(Ix(new Hx,8),g.name),g.message),Ex(a,h),t.return();d.g.push(7);k=vy(a,c,e);return ua(t,c.experimentState.reachUseCreateWorklet?wy(a,b,f):xy(a,b,f),2);case 2:return l=t.h,ua(t,k,3);case 3:return m=t.h,d.g.push(8),q=e.map(function(r){var w,u,v;return yy(a,l,r,m,(w=c.deviceType)!=null?w:1,c.escapedQueryId,(u=c.trafficTypes)!=null?u:[0],(v=c.isProductSplitVpidLogsExperiment)!=null?v:!1,function(B){var P,I=ey(dy(ay(by(cy($x(new Zx,c.escapedQueryId),(P=c.trafficTypes)!=null?P:[0]),-1),r)));qy(a,I,B,r.lc)})}),ua(t,Promise.all(q),4);case 4:d.g.push(9),t.g=0}})}function xy(a,b,c){var d,e,f;return Ga(function(g){switch(g.g){case 1:d=a.sharedStorage;if(!d)return g.return(Promise.reject(Error("no shared storage API")));va(g,2);return ua(g,d.worklet.addModule(b),4);case 4:g.g=3;g.l=0;break;case 2:e=wa(g),c(-17),f=Kx(Jx(Ix(new Hx,1),e.name),e.message),Ex(a,f);case 3:return g.return(d)}})}function wy(a,b,c){var d,e,f;return Ga(function(g){if(g.g==1){d=a.sharedStorage;if(!d)return g.return(Promise.reject(Error("no shared storage API")));va(g,2);return ua(g,d.createWorklet(b,{dataOrigin:"script-origin"}),4)}if(g.g!=2)return g.return(g.h);e=wa(g);c(-17);f=Kx(Jx(Ix(new Hx,1),e.name),e.message);Ex(a,f);return g.return(Promise.reject(e))})}function vy(a,b,c){var d,e,f;return Ga(function(g){if(g.g==1)return d=[].concat(A(new Set(c.map(function(h){return h.Kb})))),e=d.map(function(h){return zy(a,b,h)}),ua(g,Promise.all(e),2);f=g.h;return g.return(new Map(f.map(function(h,k){return[d[k],h]})))})}function zy(a,b,c){var d,e,f,g,h,k,l,m,q;return Ga(function(t){switch(t.g){case 1:e=(d=b.clientsideModelFilename)!=null?d:"model_person_country_code_XX_person_region_code_5858.json";f=void 0;g=1;h={method:"GET"};k=200;l=b.geoTargetMessage?Nt(b.geoTargetMessage):void 0;var r=new Hx;r=Ug(r,10,b.escapedQueryId);m=Mg(r,11,l);va(t,2);return ua(t,a.global.fetch(Ay(c,e),h),4);case 4:f=t.h;k=f.status;if(f.ok){t.Ja(5);break}return ua(t,a.global.fetch(Ay(c,"model_person_country_code_XX_person_region_code_5858.json"),h),6);case 6:f=t.h,g=2;case 5:t.g=3;t.l=0;break;case 2:q=wa(t),k=-1,q instanceof Error&&Kx(Jx(m,q.name),q.message);case 3:r=Ix(m,2);ug(r,9,Hf(k));if(!f||!f.ok)return r=Vg(m,4,4),r=Ug(r,8,e),Ug(r,7,""),Ex(a,m),t.return();r=Vg(m,4,g);Ug(r,7,g===1?e:"");Ex(a,m);return ua(t,f.text(),7);case 7:return t.return(t.h)}})}function Ay(a,b){return"https://www.googletagservices.com/agrp/"+ty[a]+"/"+b}function yy(a,b,c,d,e,f,g,h,k){var l,m,q,t,r,w,u;return Ga(function(v){switch(v.g){case 1:l=d.get(c.Kb);if(l===void 0)return v.return();var B=qq(-**********);if(B===void 0)B=-1;else{var P=Number,I=new Vx;I.update(l);var ca=[],E=I.j*8;I.h<56?I.update(Tx,56-I.h):I.update(Tx,I.blockSize-(I.h-56));for(var x=63;x>=56;x--)I.l[x]=E&255,E/=256;Ux(I);for(x=E=0;x<I.o;x++)for(var W=24;W>=0;W-=8)ca[E++]=I.g[x]>>W&255;I=BigInt(0);ca=z(ca);for(E=ca.next();!E.done;E=ca.next())I=(I*BigInt(256)+BigInt(E.value))%B;B=P(I)}m=B;B=dy(by(ay(cy($x(new Zx,f),g),c),m));B.message=Rg(B.message,9,h);q=ey(B);t={contextId:q,aggregationCoordinatorOrigin:"https://publickeyservice.msmt.gcp.privacysandboxservices.com",filteringIdMaxBytes:4};r={modelJson:l,modelHash:m,deviceType:e,enableDebugMode:c.lc,reportBrowserIdInsteadOfVPID:c.zc,filterIds:fy(a,c.filterIds)};w=b.run("google_reach",{privateAggregationConfig:t,data:r,keepAlive:!0});if(w===void 0){v.Ja(2);break}va(v,3);return ua(v,w,5);case 5:v.g=2;v.l=0;break;case 3:u=wa(v),k(-18),B=u,I=Kx(Jx(Ix(new Hx,3),(P=B==null?void 0:B.name)!=null?P:"unknown"),(ca=B==null?void 0:B.message)!=null?ca:""),Ex(a,I);case 2:B=Ix(new Hx,5),B=Vg(B,5,c.Kb===1?1:2),B=Vg(B,6,c.zc?1:2),Ex(a,B),v.g=0}})};function By(a){var b=b===void 0?[]:b;var c=c===void 0?[a]:c;this.h=a;this.subscribedEvents=b;this.controlledEvents=c;this.name="reach";this.g=new Map}n=By.prototype;n.start=function(a){a.U&&(this.context=a.U)};n.dispose=function(){this.g.forEach(function(a){return void a.dispose()});this.g.clear()};n.Gd=function(a,b,c,d,e){var f=this,g=this.context;if(g){var h=new Lx(g);Mx(h,function(){var k,l,m,q;return Ga(function(t){if(t.g==1){h.g.push(1);var r;if(r=wg(b,Ot,1))r=Ut(b),r=!Og(r,3,!0);if(r)return t.return();h.g.push(2);return Ys(g)?ua(t,up(Dx(g)),2):t.return()}if(t.g!=3){k=t.h;if(!k)return t.return();h.g.push(3);l=new Cy(g,b,f.h,c,d,h);f.g.set(a,l);return ua(t,l.run(),3)}e((q=(m=Ut(b))==null?void 0:Pg(m,10))!=null?q:-1);t.g=0})})}};n.Mb=function(a){var b;(b=this.g.get(a))==null||b.dispose();this.g.delete(a)};n.handleEvent=function(){};function Cy(a,b,c,d,e,f){this.context=a;this.metadata=b;this.h=c;this.experimentState=d;this.Wa=e;this.g=f}Cy.prototype.run=function(){var a=this,b,c;return Ga(function(d){if(d.g==1)return b={},ua(d,new Promise(function(e){a.Wa(a.h,b,e)}),2);c=d.h;if(!c)return d.return();a.g.g.push(4);return ua(d,Dy(a),0)})};function Dy(a){var b,c,d,e,f,g,h,k,l,m,q,t,r,w,u,v,B,P;return Ga(function(I){var ca=a.experimentState,E=(l=(b=Ut(a.metadata))==null?void 0:Qg(b))!=null?l:"",x;(c=Ut(a.metadata))==null?x=void 0:x=yg(c,7,Gf,void 0===Xe?2:4);x=(m=x)!=null?m:void 0;var W=(d=Ut(a.metadata))==null?void 0:Uf(sg(d,1)),Tc=(q=(e=Ut(a.metadata))==null?void 0:(f=Jg(e,Mt,4))==null?void 0:ai(f))!=null?q:void 0,Sc=(t=(g=Ut(a.metadata))==null?void 0:Pg(g,8))!=null?t:void 0,Id=Ey,Ia;(h=Ut(a.metadata))==null?Ia=void 0:Ia=yg(h,5,If,Xe===Xe?2:4);Ia=Id(a,(r=Ia)!=null?r:void 0);Id=Ey;var Jd;(k=Ut(a.metadata))==null?Jd=void 0:Jd=yg(k,6,If,Xe===Xe?2:4);u={experimentState:ca,escapedQueryId:E,trafficTypes:x,isProductSplitVpidLogsExperiment:!0,clientsideModelFilename:W,geoTargetMessage:Tc,deviceType:Sc,productionFilterIds:Ia,testFilterIds:Id(a,(w=Jd)!=null?w:void 0)};if(a.experimentState.reachUseCreateWorklet)return P=a.context.ae[2],a.g.g.push(10),ua(I,uy(a.context,P,u,a.g),0);v=a.context.ae[0];B=btoa(JSON.stringify(u));return ua(I,As(a.context.document,v,B),0)})}function Ey(a,b){if(b!==void 0)return b.map(function(c){var d;return String((d=qq(c))!=null?d:0)})}Cy.prototype.dispose=function(){};function Fy(a){var b=new gt("impression"),c=new gt("begin to render"),d=new gt("unmeasurable"),e=new gt("viewable"),f=new gt("reach vpid"),g=new et(b,f,c,e,d),h=new Wt,k=new Ax(b.event);b=new wx(h,b.event,c.event,d.event,e.event);(new mt(a,g,h,k,b,new By(f.event))).start()};var Gy={rxInNonrx:!1,fetchLaterBeacons:!1,addQueryIdToErrorPing:!1,shouldSendExplicitDisplayMeasurablePing:!1,dedicatedViewableAttributionPing:0,shouldIgnoreAdChoicesIcon:!1,considerOmidZOrderOcclusions:!1,extraPings:0,extrapolators:!1,rxlidarStatefulBeacons:!1,waitForImpressionColleague:!1,reachUseCreateWorklet:!1,useReachIntegrationPolyfill:!1,useReachIntegrationSharedStorage:!0,sendBrowserIdInsteadOfVPID:!1};function Hy(a){return new ht(function(b){function c(){return void b(Gy)}try{(new Wt(!0)).start(a,function(k,l,m){b(m)},function(){},c);var d,e,f,g,h=(g=(f=a==null?void 0:(d=a.ha)==null?void 0:d.B)!=null?f:a==null?void 0:(e=a.U)==null?void 0:e.B)!=null?g:void 0;h!==void 0?h.setTimeout(c,10):c()}catch(k){c()}})};(function(){var a=Kr(),b=Pr(a),c={U:new Xs(void 0,void 0,void 0,a),ha:b};Hy(c).then(function(d){d.rxInNonrx?Fy(c):Ij(378,function(){var e=C.apply(0,arguments),f;return(f=Ud(so)).mg.apply(f,A(e))})})})();}).call(this,this,this.document);
