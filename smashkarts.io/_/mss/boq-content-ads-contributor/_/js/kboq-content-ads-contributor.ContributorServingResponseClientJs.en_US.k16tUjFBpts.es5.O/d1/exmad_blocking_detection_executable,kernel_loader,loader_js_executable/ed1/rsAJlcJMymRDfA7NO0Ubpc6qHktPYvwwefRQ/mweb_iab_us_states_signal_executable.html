if (typeof __googlefc.fcKernelManager.run === 'function') {"use strict";this.default_ContributorServingResponseClientJs=this.default_ContributorServingResponseClientJs||{};(function(_){var window=this;
try{
var Ur,Vr;_.Rr=function(a){a=_.n(a);for(var b=a.next();!b.done;b=a.next()){b=b.value;var c=_.I(b,1);if(Qr.has(c)&&_.I(b,3)===1)switch(_.I(b,2)){case 2:return 3;case 3:case 4:return 2;default:return 1}}return 1};_.Tr=function(a){a=_.n(a);for(var b=a.next();!b.done;b=a.next()){b=b.value;var c=_.I(b,1);if(Sr.has(c)&&_.I(b,3)===1)switch(_.I(b,2)){case 2:return 3;case 3:case 4:return 2;default:return 1}}return 1};Ur={UNKNOWN:0,CCPA_DOES_NOT_APPLY:1,NOT_OPTED_OUT:2,OPTED_OUT:3};
Vr={UNKNOWN:0,DOES_NOT_APPLY:1,NOT_OPTED_OUT:2,OPTED_OUT:3};_.Xr=function(a,b,c,d,e,f){this.j=a;this.F=b;this.o=c;this.localizedDnsText=d===void 0?null:d;this.localizedDnsCollapseText=e===void 0?null:e;this.l=f;this.overrideDnsLink=_.Wr(a);this.InitialCcpaStatusEnum=Ur};_.m=_.Xr.prototype;
_.m.load=function(){_.pn("ccpa",this,!0,this.j);if(this.j.frameElement){var a=_.on(this.j).callbackQueue||[];if(Array.isArray(a)){a=_.n(a);for(var b=a.next();!b.done;b=a.next())b=b.value.INITIAL_CCPA_DATA_READY,typeof b==="function"&&b()}}else _.$n(this.F,"initialCcpaData")};_.m.getInitialCcpaStatus=function(){return this.o};_.m.openConfirmationDialog=function(a){this.l?this.l(a):window.console.error("CCPA override API was used incorrectly! The CCPA message does not exist in this context.")};
_.m.getLocalizedDnsText=function(){return this.localizedDnsText};_.m.getLocalizedDnsCollapseText=function(){return this.localizedDnsCollapseText};_.Wr=function(a){var b,c;return((b=_.on(a))==null?void 0:(c=b.ccpa)==null?void 0:c.overrideDnsLink)===!0};_.Zr=function(a,b){this.j=a;this.F=b;this.overrideDnsLink=_.Yr(a)};_.Zr.prototype.load=function(a,b,c){a=a===void 0?null:a;b=b===void 0?null:b;_.pn("__fcusi",this,!0,this.j);var d=null;a&&b&&c&&(d={localizedDnsText:a,localizedDnsCollapseText:b,openConfirmationDialog:c});b={};a=this.F;b=(b.initialUsStatesData=d,b);d=_.Zn(a);a=_.n(Object.entries(b));for(b=a.next();!b.done;b=a.next())c=_.n(b.value),b=c.next().value,c=c.next().value,d.executeRemainingFunctionsWithArgument(b,c)};
_.Yr=function(a){var b,c;return((b=a.googlefc)==null?void 0:(c=b.__fcusi)==null?void 0:c.overrideDnsLink)===!0};_.as=function(a,b,c,d){this.o=a;this.F=b;this.l=c;this.j=d;this.overrideDnsLink=_.$r(a);this.InitialUsStatesOptOutStatusEnum=Vr};_.as.prototype.load=function(){_.pn("usstatesoptout",this,!0,this.o);_.$n(this.F,"initialUsStatesOptOutData")};_.as.prototype.getInitialUsStatesOptOutStatus=function(){return this.l};_.as.prototype.openConfirmationDialog=function(a){this.j?this.j(a):window.console.error("US States opt out override API was used incorrectly! The US states message does not exist in this context.")};
_.$r=function(a){var b,c;return((b=a.googlefc)==null?void 0:(c=b.usstatesoptout)==null?void 0:c.overrideDnsLink)===!0};var Qr=new Set([6,7]),Sr=new Set([6,1]);_.bs=function(a){this.A=_.t(a)};_.u(_.bs,_.L);
}catch(e){_._DumpException(e)}
try{
var it=function(a){this.A=_.t(a)};_.u(it,_.L);var jt=_.Pc(it);var kt=function(a,b,c,d){this.l=a;this.params=b;this.o=c;this.F=d;this.C=new _.Ug(this.l.document,_.O(this.params,3),new _.Ng(_.vk(this.o)));this.j=a.__gppManager;this.B=_.C(this.params,_.bs,5,_.z())};
kt.prototype.run=function(){var a=this,b;return _.v(function(c){if(a.j){for(var d=[],e=_.n(_.C(a.params,_.bs,5,_.z())),f=e.next();!f.done;f=e.next()){f=f.value;var g=_.I(f,1);_.Kp.has(g)&&_.I(f,2)!==2&&(d.push(_.Kp.get(g)),g===1&&(f=a.C,g=_.Vg(f),g=_.Ad(g,5),_.Zg(f,g)))}d.length>0&&(a.j.setCmpSignalStatusNotReady(),a.j.clearSectionValues(d),a.j.setCmpSignalStatusReady())}d=_.Tr(a.B);d===2?_.za(Error("Invalid user initial status for CCPA (NOT_OPTED_OUT).")):(new _.Xr(a.l,a.F,d)).load();(new _.Zr(a.l,
a.F)).load();d=_.Rr(a.B);d===2?_.za(Error("Invalid user initial status for US states opt-out (NOT_OPTED_OUT).")):(new _.as(a.l,a.F,d)).load();b=_.Qd(_.x(a.o,_.Pd,6)?_.Nd(_.wk(a.o)):new _.Pd,10);return c.return(b)})};var lt=function(){};lt.prototype.run=function(a,b,c){var d,e;return _.v(function(f){if(f.j==1)return d=jt(b),f.yield((new kt(a,d,_.A(d,_.uk,2),c)).run(),2);e=f.l;return f.return({ea:_.M(e)})})};_.yk(11,new lt);
}catch(e){_._DumpException(e)}
}).call(this,this.default_ContributorServingResponseClientJs);
// Google Inc.

//# sourceURL=/_/mss/boq-content-ads-contributor/_/js/k=boq-content-ads-contributor.ContributorServingResponseClientJs.en_US.k16tUjFBpts.es5.O/d=1/exm=ad_blocking_detection_executable,kernel_loader,loader_js_executable/ed=1/rs=AJlcJMymRDfA7NO0Ubpc6qHktPYvwwefRQ/m=web_iab_us_states_signal_executable
__googlefc.fcKernelManager.run('\x5b\x5b\x5b11,\x22\x5bnull,\x5b\x5bnull,null,null,\\\x22https:\/\/fundingchoicesmessages.google.com\/f\/AGSKWxV7EkkMettFwSmbQVbPiltmGWiOyUf9RQbUp3rmScTHFopMOYcLMajhvrX7gvil1F5ODlnvVAu6Ta5Ukt2mn7RDPeC-30Mb_SYKihNRYyYI5fEscBKdGgHnLjyKFx1NGaP_XX--Fw\\\\u003d\\\\u003d\\\x22\x5d,null,null,\x5bnull,null,null,\\\x22https:\/\/fundingchoicesmessages.google.com\/el\/AGSKWxWChjgG5YxvVbImuK0603RFMJmGwX3IUIqFQLD1yMcQ-cn6D8NLtnprmgOdzO_AmhdIJ_qi4kNwC1Tq9pRBK_GlsVXlgZqhpmzcFCxOG5dYjNo8H_z870dKtHFr2C7_jNnUxBvGtg\\\\u003d\\\\u003d\\\x22\x5d,null,\x5bnull,\x5b7,6\x5d,null,null,null,null,null,null,null,null,null,1\x5d\x5d,\\\x22smashkarts.io\\\x22\x5d\x22\x5d\x5d,\x5bnull,null,null,\x22https:\/\/fundingchoicesmessages.google.com\/f\/AGSKWxWEsRmVYxmPF-6HCVAAdhr3KjQ7uIYmCynW-EYi38tPW47D0WiCN2iIBZGS_tSM1ehcojOQbmvhrDgZnXXNpkXF41LhrcGlFdLOkKh0p6HTQt7OPGu6eg-IEKrAp0-7hS4oZG0IAg\\u003d\\u003d\x22\x5d\x5d');}